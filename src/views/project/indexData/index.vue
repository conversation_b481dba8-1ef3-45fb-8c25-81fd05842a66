<template>
  <div class="app-container">
    <el-form :model="addForm">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>基础数据</span>
        </div>
        <el-row>
          <el-form-item label="用户名/昵称" prop="projectId">
            <el-select v-model="addForm.userId" placeholder="请选择" clearable filterable @change="userChange">
              <el-option v-for="item in userList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="总里程(km)" prop="totalMiles" label-width="auto">
              <el-input-number v-model="addForm.totalMiles" placeholder="请输入总里程" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="隧道总数" prop="totalTunnel" label-width="auto">
              <el-input-number v-model="addForm.totalTunnel" placeholder="请输入隧道总数" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="总病害数" prop="totalDisease" label-width="auto">
              <el-input-number v-model="addForm.totalDisease" placeholder="请输入总病害数" clearable/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="衬砌裂缝占比" prop="little" label-width="auto">
              <el-input-number v-model="addForm.little" placeholder="请输入衬砌裂缝占比" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="渗漏水占比" prop="medium" label-width="auto">
              <el-input-number v-model="addForm.medium" placeholder="请输入渗漏水占比" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="剥落占比" prop="big" label-width="auto">
              <el-input-number v-model="addForm.big" placeholder="请输入剥落占比" clearable/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="钢筋锈蚀占比" prop="biggest" label-width="auto">
              <el-input-number v-model="addForm.biggest" placeholder="请输入钢筋锈蚀占比" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="一类隧道数" prop="rankOne" label-width="auto">
              <el-input-number v-model="addForm.rankOne" placeholder="请输入一类隧道数" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="二类隧道数" prop="rankTwo" label-width="auto">
              <el-input-number v-model="addForm.rankTwo" placeholder="请输入二类隧道数" clearable/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="三类隧道数" prop="rankThree" label-width="auto">
              <el-input-number v-model="addForm.rankThree" placeholder="请输入三类隧道数" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="四类隧道数" prop="rankFour" label-width="auto">
              <el-input-number v-model="addForm.rankFour" placeholder="请输入四类隧道数" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="五类隧道数" prop="rankFour" label-width="auto">
              <el-input-number v-model="addForm.rankFive" placeholder="请输入五类隧道数" clearable/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <el-row style="margin-top: 20px;">
          <el-col :span="12">
            <el-card class="box-card">
              <div slot="header" class="clearfix">
                <span>隧道地理位置统计 </span>
                <el-button type="text" @click="addList()">新增</el-button>
              </div>
              <el-table :data="addForm.list" style="height: 300px;">
                <el-table-column prop="province" label="省" width="180">
                  <template #default="scope">
                    <el-input v-model="scope.row.province" placeholder="请输入" style="width:100%;"/>
                  </template>
                </el-table-column>
                <el-table-column prop="city" label="市" width="180">
                  <template #default="scope">
                    <el-input v-model="scope.row.city" placeholder="请输入" style="width:100%;"/>
                  </template>
                </el-table-column>
                <el-table-column prop="count" label="数量" width="180">
                  <template #default="scope">
                    <el-input-number v-model="scope.row.count" placeholder="请输入" style="width:100%;"/>
                  </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="150" class-name="small-padding fixed-width">
                  <template #default="scope">
                    <el-button type="text" @click="deleteRow(scope.$index,addForm.list)">移除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-col>
          <el-col :span="10" :offset="2">
            <el-card class="box-card">
              <div slot="header" class="clearfix">
                <span>检测隧道规模统计 </span>
                <el-button type="text" @click="addMilesList()">新增</el-button>
              </div>
              <el-table :data="addForm.milesInfoList" style="height: 300px;">
                <el-table-column prop="province" label="隧道长度类型" width="180">
                  <template #default="scope">
                    <el-input v-model="scope.row.province" placeholder="请输入" style="width:100%;"/>
                  </template>
                </el-table-column>
                <el-table-column prop="count" label="数量" width="180">
                  <template #default="scope">
                    <el-input-number v-model="scope.row.count" placeholder="请输入" style="width:100%;"/>
                  </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="150" class-name="small-padding fixed-width">
                  <template #default="scope">
                    <el-button type="text" @click="deleteRow(scope.$index,addForm.milesInfoList)">移除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-col>
      </el-row>
    </el-form>
    <div class="btn-section" style="margin-top: 10px;">
      <el-button @click="handleUpdate()" type="primary" class="btn sure">确定更改</el-button>
    </div>
  </div>
</template>

<script>
import {queryIndexDTO, updateIndexDTO} from "@/api/project/indexData";
import {listAll} from "@/api/system/user";


export default {
  name: "indexData",
  //props的基本用法是父组件给子组件传输数据和验证
  props: {
    //父组件数据
  },
  data() {
    return {
      userList:[],
      proxy:null,
      addForm:{
        userId:null,
        milesInfoList:[],
        list:[],
        medium:null,
        little:null,
        biggest:null
      }
    }
  },
  methods: {
     userChange(value) {
       this.queryIndexDTO();
    },
    /** 查询用户列表 */
    getList() {
      let _this=this;
      listAll({}).then(res => {
        _this.userList=[];
        for (let i=0;i<res.data.length; i++) {
          if(i==0){
            _this.addForm.userId= res.data[i].userId;
          }
          let temp={};
          temp.value = res.data[i].userId;
          temp.label = res.data[i].userName + "/" + res.data[i].nickName;
          _this.userList.push(temp);
        }
        _this.queryIndexDTO();
      });
    },
    deleteRow(index, rows) {
      rows.splice(index, 1);
    },
    addMilesList(){
      let req={};
      req.province=null;
      req.city=null;
      req.count=0;
      if(this.addForm.milesInfoList ==null){
        this.addForm.milesInfoList=[];
      }
      this.addForm.milesInfoList.push(req);
    },
    addList(){
      let req={};
      req.province=null;
      req.count=0;
      if(this.addForm.list ==null){
        this.addForm.list=[];
      }
      this.addForm.list.push(req);
    },
    queryIndexDTO() {
       let _this=this;
      let req={};
      req.userId=_this.addForm.userId;
      queryIndexDTO(req).then(res => {
        _this.addForm=res.data;
        _this.addForm.userId=req.userId;
      });
    },
    handleUpdate() {
      let _this=this;
      updateIndexDTO(_this.addForm).then(response => {
        _this.$modal.msgSuccess("操作成功");
      });
    }
  },
  created() {
    this.getList();
    // this.proxy = getCurrentInstance();

  },
}
</script>
