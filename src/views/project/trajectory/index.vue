<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
         <el-form-item label="工程名称" prop="projectName">
            <el-input v-model="queryParams.projectName" placeholder="请输入工程名称" clearable style="width: 400px"
               @keyup.enter="handleSearch" @clear="clearProjectName" />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleSearch">查找工程轨迹</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            <el-button v-if="isSearch" icon="Plus" @click="handleAdd"
               v-hasPermi="['project:trajectory:add']">新增</el-button>
            <el-button v-if="isSearch" type="warning" plain icon="Download" @click="handleExport"
               v-hasPermi="['project:trajectory:export']">导出</el-button>
            <el-button v-if="isSearch" type="info" plain icon="Upload" @click="handleImport"
               v-hasPermi="['project:trajectory:import']">导入</el-button>
         </el-form-item>
      </el-form>

      <el-table v-if="refreshTable" v-loading="loading" :data="trajectoryList" row-key="id">
         <el-table-column prop="projectName" label="工程名称" width="400"></el-table-column>
         <el-table-column prop="sort" label="序号" width="100"></el-table-column>
         <el-table-column prop="longitude" label="经度" width="110"></el-table-column>
         <el-table-column prop="latitude" label="纬度" width="100"></el-table-column>
         <el-table-column prop="stakeNum" label="桩号" width="100"></el-table-column>
         <el-table-column label="操作" align="center" width="80" class-name="small-padding fixed-width" v-if="isSearch">
            <template #default="scope">
               <el-tooltip content="修改" placement="top">
                  <el-button type="text" icon="Edit" @click="handleUpdate(scope.row)"
                     v-hasPermi="['project:trajectory:edit']"></el-button>
               </el-tooltip>
               <el-tooltip content="删除" placement="top">
                  <el-button type="text" icon="Delete" @click="handleDelete(scope.row)"
                     v-hasPermi="['project:trajectory:remove']"></el-button>
               </el-tooltip>
            </template>
         </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
         @pagination="getList" />

      <!-- 添加或修改菜单对话框 -->
      <el-dialog :title="title" v-model="open" width="680px" append-to-body>
         <el-form ref="trajectoryRef" :model="form" :rules="rules" label-width="auto">
            <el-row>
               <el-col :span="22">
                  <el-form-item label="工程名称" prop="projectName">
                     <el-input v-model="form.projectName" placeholder="请输入工程名称" />
                  </el-form-item>
               </el-col>
               <el-col :span="11">
                  <el-form-item label="序号" prop="projectName">
                     <el-input v-model="form.sort" placeholder="请输入工程名称" />
                  </el-form-item>
               </el-col>
               <el-col :span="11">
                  <el-form-item label="桩号" prop="stakeNum">
                     <el-input v-model="form.stakeNum" placeholder="请输入桩号" />
                  </el-form-item>
               </el-col>
               <el-col :span="11">
                  <el-form-item label="经度" prop="longitude">
                     <el-input v-model="form.longitude" placeholder="请输入经度" />
                  </el-form-item>
               </el-col>
               <el-col :span="11">
                  <el-form-item label="纬度" prop="latitude">
                     <el-input v-model="form.latitude" placeholder="请输入路段名称" />
                  </el-form-item>
               </el-col>
               <el-col :span="11">
                  <el-form-item label="是否有效" prop="isAvailable">
                     <el-radio-group v-model="form.isAvailable">
                        <el-radio :label="false">无效</el-radio>
                        <el-radio :label="true">有效</el-radio>
                     </el-radio-group>
                  </el-form-item>
               </el-col>
               <el-col :span="11">
                  <el-form-item label="是否删除" prop="isDeleted">
                     <el-radio-group v-model="form.isDeleted">
                        <el-radio :label="false">未删除</el-radio>
                        <el-radio :label="true">删除</el-radio>
                     </el-radio-group>
                  </el-form-item>
               </el-col>
            </el-row>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">确 定</el-button>
               <el-button @click="cancel">取 消</el-button>
            </div>
         </template>
      </el-dialog>

      <!-- 导入Excel表对话框 -->
      <el-dialog :title="upload.title" v-model="upload.open" width="680px" append-to-body>
         <el-upload ref="uploadRef" :limit="1" name="file" accept=".xlsx, .xls" :headers="upload.headers"
            :action="upload.url" :disabled="upload.isUploading" :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccess" :auto-upload="false" drag>
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <template #tip>
               <div class="el-upload__tip text-center">
                  <span>仅允许导入xls、xlsx格式文件。</span>
               </div>
            </template>
         </el-upload>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitFileForm">确 定</el-button>
               <el-button @click="upload.open = false">取 消</el-button>
            </div>
         </template>
      </el-dialog>

   </div>
</template>

<script setup name="Disaster">
import { listTrajectories, listByProjectName, getById, selectById, addTrajectory, updateTrajectory, delTrajectory } from "@/api/project/trajectory";
import SvgIcon from "@/components/SvgIcon";
import IconSelect from "@/components/IconSelect";
import { ClickOutside as vClickOutside } from 'element-plus'
//import { genFileId } from 'element-plus'
//import { UploadInstance, UploadProps, UploadRawFile } from 'element-plus'
import { getToken } from "@/utils/auth";

const { proxy } = getCurrentInstance();
const { sys_show_hide, sys_normal_disable } = proxy.useDict("sys_show_hide", "sys_normal_disable");

const trajectoryList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const isSearch = ref(false);
const total = ref(0);
const title = ref("");
const isAdd = ref(false);
const isArea = ref(false);
const dateRange = ref([]);
const tables = ref([]);
//const visible = ref(false);
const fileList = ref([]);
//const excelTitle = ref("导入Excel表");
const menuOptions = ref([]);
const isExpandAll = ref(false);
const refreshTable = ref(true);
/*** 工程导入参数 */
const upload = reactive({
   // 是否显示弹出层（工程导入）
   open: false,
   // 弹出层标题（工程导入）
   title: "",
   // 是否禁用上传
   isUploading: false,
   // 是否更新已经存在的工程数据
   //updateSupport: 0,
   // 设置上传的请求头部
   headers: { Authorization: "Bearer " + getToken() },
   // 上传的地址
   url: import.meta.env.VITE_APP_BASE_API + "/project/trajectory/import"
});

const data = reactive({
   form: {},
   queryParams: {
      pageNum: 1,
      pageSize: 10,
      projectName: undefined
   },
   rules: {
      projectName: [{ required: true, message: "工程名称不能为空", trigger: "blur" }],
      sort: [{ required: true, message: "轨迹序号不能为空", trigger: "blur" }],
      isAvailable: [{ required: true, message: "是否有效不能为空", trigger: "blur" }],
      isDeleted: [{ required: true, message: "是否删除不能为空", trigger: "blur" }]
   },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询菜单列表 */
function getList() {
   loading.value = true;
   if (queryParams.value.projectName == null) {
      listTrajectories(proxy.addDateRange(queryParams.value, dateRange.value)).then(res => {
         loading.value = false;
         total.value = res.total;
         trajectoryList.value = res.rows;
      });
   }
   else {
      listByProjectName(queryParams.value.projectName).then(res => {
         loading.value = false;
         total.value = res.total;
         trajectoryList.value = res.rows;
      });
   }
}
/** 查询菜单列表 */
function getTrajectoriesByName(aName) {
   loading.value = true;
   listByProjectName(aName).then(res => {
      loading.value = false;
      total.value = res.total;
      trajectoryList.value = res.rows;
   });
}
/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}
/** 表单重置 */
function reset() {
   form.value = {
      projectName: undefined,
      sort: undefined,
      longitude: undefined,
      latitude: undefined,
      stakeNum: undefined,
      isAvailable: undefined,
      isDeleted: undefined
   };
   proxy.resetForm("trajectoryRef");
}
/** 搜索框清空 */
function clearProjectName() {
   queryParams.value.projectName = undefined;
   isSearch.value = false;
   getList();
}
/** 搜索按钮操作 */
function handleSearch() {
   let aname = queryParams.value.projectName;
   console.log(aname);
   if (aname != null && aname != "" && aname != undefined) {
      console.log(aname);
      isSearch.value = true;
      reset();
      getTrajectoriesByName(aname);
   }
   else {
      isSearch.value = false;
      getList();
   }
}
/** 查询按钮操作 */
function handleQuery() {
   queryParams.value.pageNum = 1;
   getList();
}
/** 重置按钮操作 */
function resetQuery() {
   dateRange.value = [];
   isSearch.value = false;
   proxy.resetForm("queryRef");
   handleQuery();
}
/** 新增按钮操作 */
function handleAdd() {
   reset();
   open.value = true;
   title.value = "添加轨迹";
   form.value.projectName = queryParams.value.projectName;
   form.value.isAvailable = true;
   form.value.isDeleted = false;
}
/** 修改按钮操作 */
async function handleUpdate(row) {
   reset();
   selectById(row.id).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "修改轨迹";
   });
}
/** 提交按钮 */
function submitForm() {
   proxy.$refs["trajectoryRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            updateTrajectory(form.value, form.value.id).then(response => {
               proxy.$modal.msgSuccess("修改成功");
               open.value = false;
               getTrajectoriesByName(queryParams.value.projectName);
            });

         } else {
            addTrajectory(form.value).then(response => {
               proxy.$modal.msgSuccess("新增成功");
               open.value = false;
               getTrajectoriesByName(queryParams.value.projectName);
            });
            //
         }
      }
   });

}
/** 删除按钮操作 */
function handleDelete(row) {
   proxy.$modal.confirm('是否确认删除此数据项?').then(function () {
      return delTrajectory(row.id);
   }).then(() => {
      //getList();
      handleSearch();
      proxy.$modal.msgSuccess("删除成功");
   }).catch(() => { });
}
/** 导出按钮操作 */
function handleExport() {
   let filename = "设备信息_" + queryParams.value.projectName + "_" + new Date().getTime() + ".xlsx";
   proxy.download("/project/trajectory/export/" + queryParams.value.projectName, {
      //...queryParams.value,
   }, filename);
}
/** 导入按钮操作 */
function handleImport() {
   upload.open = true;
   upload.title = "导入病害表";
   //visible.value = true;
}
/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
   upload.isUploading = true;
};
/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
   upload.open = false;
   upload.isUploading = false;
   proxy.$refs["uploadRef"].handleRemove(file);
   proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
   getList();
};
/** 提交上传文件 */
function submitFileForm() {
   console.log(ref["uploadRef"]);
   proxy.$refs["uploadRef"].submit();
};

getList();
</script>
