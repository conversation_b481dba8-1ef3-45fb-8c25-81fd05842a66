<template>
   <div class="app-container">
      <div class="search-section">
         <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
            <el-form-item label="隧道名称" prop="projectName">
               <el-input v-model="queryParams.projectName" placeholder="请选择隧道" clearable style="width: 400px"
                  @keyup.enter="handleSearch" @clear="clearProjectName" />
            </el-form-item>
            <el-form-item>
               <el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
               <el-button icon="Refresh" @click="resetQuery">重置</el-button>
               <el-button type="primary" icon="Plus" @click="handleSearch">新增</el-button>
               <el-button type="primary" icon="Plus" @click="handleSearch">批量新增</el-button>
               <el-button v-if="isSearch" icon="Plus" @click="handleAdd"
                  v-hasPermi="['project:disaster:add']">新增</el-button>
               <el-button v-if="isSearch" type="warning" plain icon="Download" @click="handleExport"
                  v-hasPermi="['project:disaster:export']">导出</el-button>
            </el-form-item>
         </el-form>
      </div>
      <el-table :data="disasterList" row-key="id">
         <el-table-column prop="projectName" label="分项工程"></el-table-column>
         <el-table-column prop="equipments" label="设备名称">
            <template #default="scope">
               <div class="list-section">
                  <span v-for="(item, index) in scope.row.equipments" class="list-item">{{ item }}</span>
               </div>
            </template>
         </el-table-column>
         <el-table-column prop="latitude" label="单位" width="100">
            <template #default="scope">
               <div class="list-section">
                  <span v-for="(item, index) in scope.row.unit" class="list-item">{{ item }}</span>
               </div>
            </template>
         </el-table-column>
         <el-table-column prop="upNumber" label="上行数量" width="100"></el-table-column>
         <el-table-column prop="downNumber" label="下行数量" width="100"></el-table-column>
         <el-table-column prop="total" label="合计" width="100"></el-table-column>
         <el-table-column label="操作" fixed="right" width="120" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button type="primary" @click="handleSearch">编辑</el-button>
            </template>
         </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
         @pagination="getList" />
   </div>
</template>

<script setup name="Disaster">
import { listDisaster, listByProjectName, getById, addDisaster, selectById, updateDisaster, delDisaster } from "@/api/project/disaster";
import SvgIcon from "@/components/SvgIcon";
import IconSelect from "@/components/IconSelect";
import { ClickOutside as vClickOutside } from 'element-plus'
//import { genFileId } from 'element-plus'
//import { UploadInstance, UploadProps, UploadRawFile } from 'element-plus'
import { getToken } from "@/utils/auth";

const { proxy } = getCurrentInstance();
const { sys_show_hide, sys_normal_disable } = proxy.useDict("sys_show_hide", "sys_normal_disable");

const disasterList = ref([
   {
      projectName: '消防设施',
      equipments: ['火灾报警设施', '水盆灭火器设施', '消防服设施'],
      unit: ['套', '套', '件'],
      upNumber: 120,
      downNumber: 20,
      total: 255
   }, {
      projectName: '消防设施',
      equipments: ['火灾报警设施', '水盆灭火器设施', '消防服设施'],
      unit: ['套', '套', '件'],
      upNumber: 120,
      downNumber: 20,
      total: 255
   }, {
      projectName: '消防设施',
      equipments: ['火灾报警设施', '水盆灭火器设施', '消防服设施'],
      unit: ['套', '套', '件'],
      upNumber: 120,
      downNumber: 20,
      total: 255
   }
]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const isSearch = ref(false);
const total = ref(0);
const title = ref("");
const isAdd = ref(false);
const isArea = ref(false);
const dateRange = ref([]);
const tables = ref([]);
//const visible = ref(false);
const fileList = ref([]);
//const excelTitle = ref("导入Excel表");
const menuOptions = ref([]);
const isExpandAll = ref(false);
const refreshTable = ref(true);
/*** 工程导入参数 */
const upload = reactive({
   // 是否显示弹出层（工程导入）
   open: false,
   // 弹出层标题（工程导入）
   title: "",
   // 是否禁用上传
   isUploading: false,
   // 是否更新已经存在的工程数据
   //updateSupport: 0,
   // 设置上传的请求头部
   headers: { Authorization: "Bearer " + getToken() },
   // 上传的地址
   url: import.meta.env.VITE_APP_BASE_API + "/project/disaster/import"
});

const data = reactive({
   form: {},
   queryParams: {
      pageNum: 1,
      pageSize: 10,
      projectName: undefined
   },
   rules: {
      projectName: [{ required: true, message: "工程名称不能为空", trigger: "blur" }],
      diseaseType: [{ required: true, message: "病害类型不能为空", trigger: "blur" }],
      isAvailable: [{ required: true, message: "是否有效不能为空", trigger: "blur" }],
      isDeleted: [{ required: true, message: "是否删除不能为空", trigger: "blur" }]
   },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询菜单列表 */
function getList() {
   loading.value = true;
   if (queryParams.value.projectName == null) {
      listDisaster(proxy.addDateRange(queryParams.value, dateRange.value)).then(res => {
         loading.value = false;
         total.value = res.total;
         disasterList.value = res.rows;
      });
   }
   else {
      listByProjectName(queryParams.value.projectName).then(res => {
         loading.value = false;
         total.value = res.total;
         disasterList.value = res.rows;
      });
   }
}
/** 查询菜单列表 */
function getDisasterByName(aName) {
   loading.value = true;
   listByProjectName(aName).then(res => {
      loading.value = false;
      total.value = res.total;
      disasterList.value = res.rows;
   });
}
/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}
/** 表单重置 */
function reset() {
   form.value = {
      projectName: undefined,
      longitude: undefined,
      latitude: undefined,
      stakeNum: undefined,
      diseaseTime: undefined,
      diseaseType: undefined,
      crackWidth: undefined,
      crackLength: undefined,
      crackHeight: undefined,
      diseaseArea: undefined,
      diseaseBelieve: undefined,
      diseaseUrl: undefined,
      isAvailable: undefined,
      isDeleted: undefined
   };
   proxy.resetForm("disasterRef");
}
/** 搜索框清空 */
function clearProjectName() {
   queryParams.value.projectName = undefined;
   isSearch.value = false;
   getList();
}
/** 搜索按钮操作 */
function handleSearch() {
   let aname = queryParams.value.projectName;
   console.log(aname);
   if (aname != null && aname != "" && aname != undefined) {
      console.log(aname);
      reset();
      getDisasterByName(aname);
      isSearch.value = true;
   }
   else {
      isSearch.value = false;
      getList();
   }
}
/** 查询按钮操作 */
function handleQuery() {
   queryParams.value.pageNum = 1;
   getList();
}
/** 重置按钮操作 */
function resetQuery() {
   dateRange.value = [];
   isSearch.value = false;
   proxy.resetForm("queryRef");
   handleQuery();
}
/** 新增按钮操作 */
function handleAdd() {
   reset();
   open.value = true;
   title.value = "添加病害";
   isAdd.value = true;
   form.value.projectName = queryParams.value.projectName;
   form.value.isAvailable = true;
   form.value.isDeleted = false;
}
/** 修改按钮操作 */
async function handleUpdate(row) {
   reset();
   selectById(row.id).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "修改病害";
      isAdd.value = false;
      if (form.value.diseaseArea > 0) {
         isArea.value = true;
      }
      else { isArea.value = false; }
   });
}
/** 提交按钮 */
function submitForm() {
   proxy.$refs["disasterRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            updateDisaster(form.value, form.value.id).then(response => {
               proxy.$modal.msgSuccess("修改成功");
               open.value = false;
               getDisasterByName(queryParams.value.projectName);
            });
         } else {
            addDisaster(form.value).then(response => {
               proxy.$modal.msgSuccess("新增成功");
               open.value = false;
               getDisasterByName(queryParams.value.projectName);
            });
         }
      }
   });
}
/** 删除按钮操作 */
function handleDelete(row) {
   proxy.$modal.confirm('是否确认删除此数据项?').then(function () {
      return delDisaster(row.id);
   }).then(() => {
      //getList();
      handleSearch();
      proxy.$modal.msgSuccess("删除成功");
   }).catch(() => { });
}
/** 导出按钮操作 */
function handleExport() {
   let filename = "病害信息_" + queryParams.value.projectName + "_" + new Date().getTime() + ".xlsx";
   proxy.download("/project/disaster/export/" + queryParams.value.projectName, {
      //...queryParams.value,
   }, filename);
}
/** 导入按钮操作 */
function handleImport() {
   upload.open = true;
   upload.title = "导入病害表";
   //visible.value = true;
}
/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
   upload.isUploading = true;
};
/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
   upload.open = false;
   upload.isUploading = false;
   proxy.$refs["uploadRef"].handleRemove(file);
   proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
   getList();
};
/** 提交上传文件 */
function submitFileForm() {
   console.log(ref["uploadRef"]);
   proxy.$refs["uploadRef"].submit();
};
</script>

<style lang="scss" scoped>
.app-container {
   height: 100%;
   padding: 0 20px;

   .list-section {
      display: flex;
      flex-direction: column;
   }

   .search-section {
        padding: 20px 0;
    }
}
</style>