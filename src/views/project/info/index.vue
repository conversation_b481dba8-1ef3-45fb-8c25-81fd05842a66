<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
         <el-form-item label="" prop="name">
            <el-input v-model="queryParams.name" placeholder="请输入隧道名称/编号" clearable style="width: 400px"
               @keyup.enter="handleSearch" @clear="clearName" />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            <el-button icon="Plus" @click="handleAdd" v-hasPermi="['project:info:add']">新增隧道</el-button>
            <el-button type="warning" plain icon="Download" @click="handleExport"
               v-hasPermi="['project:info:export']">导出</el-button>
            <el-button type="info" plain icon="Upload" @click="handleImport"
               v-hasPermi="['project:info:import']">导入</el-button>
         </el-form-item>
      </el-form>
      <el-table :data="infoList" row-key="id">
         <el-table-column prop="no" label="隧道编号" width="80"></el-table-column>
         <el-table-column prop="way" label="隧道所属路段" width="120"></el-table-column>
         <el-table-column prop="name" label="隧道名称" width="120"></el-table-column>
         <el-table-column prop="structure" label="结构形式" width="120"></el-table-column>
         <el-table-column prop="length" label="隧道长度/m" width="100"></el-table-column>
         <el-table-column prop="scale" label="隧道规模" width="100"></el-table-column>
         <el-table-column prop="startNo" label="起始桩号" width="80"></el-table-column>
         <el-table-column prop="endNo" label="施工期中心桩号" width="120"></el-table-column>
         <el-table-column prop="endNo" label="运营期中心桩号" width="120"></el-table-column>
         <el-table-column prop="deep" label="埋深范围" width="80">
         </el-table-column>
         <el-table-column prop="create" label="创建人" width="80">
         </el-table-column>
         <el-table-column prop="createTime" label="创建时间" width="150">
         </el-table-column>
         <el-table-column label="操作" fixed="right" width="150" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button type="text" @click="handleUpdate(scope.row)">查看</el-button>
               <el-button type="primary" @click="handleUpdate(scope.row)">编辑</el-button>
            </template>
         </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
         @pagination="getList" />
      <el-dialog v-model="state.addModal" title="新增隧道" width="1200px">
         <tunnelAdd @btnTap="addBtnTapHandler"></tunnelAdd>
      </el-dialog>
   </div>
</template>

<script setup name="Info">
import { listInfo, getInfo, selectInfo, getByName, addInfo, updateInfo, delInfo } from "@/api/project/info";
import SvgIcon from "@/components/SvgIcon";
import IconSelect from "@/components/IconSelect";
import { ClickOutside as vClickOutside } from 'element-plus'
//import { genFileId } from 'element-plus'
//import { UploadInstance, UploadProps, UploadRawFile } from 'element-plus'
import { getToken } from "@/utils/auth";
import tunnelAdd from './components/tunnelAdd.vue';
import tunnelModify from './components/tunnelModify.vue';

const { proxy } = getCurrentInstance();
const { sys_show_hide, sys_normal_disable } = proxy.useDict("sys_show_hide", "sys_normal_disable");

const state = reactive({
   addModal: false
})

/**
 * 
 *  no: 'SD1023',
      way: '武汉市东湖路',
      name: '武汉市东湖隧道',
      structure: '锲形结构',
      length: '13600m',
      scale: '中型规模',
      startNo: 'N1022',
      endNo: 'N1222',
      deep: '166m',
      create: '张三',
      createTime: '2022 02-25'
*/
const infoList = ref([
   {
      no: 'SD1023',
      way: '武汉市东湖路',
      name: '武汉市东湖隧道',
      structure: '锲形结构',
      length: '13600m',
      scale: '中型规模',
      startNo: 'N1022',
      endNo: 'N1222',
      deep: '166m',
      create: '张三',
      createTime: '2022 02-25'
   }, {
      no: 'SD1023',
      way: '武汉市东湖路',
      name: '武汉市东湖隧道',
      structure: '锲形结构',
      length: '13600m',
      scale: '中型规模',
      startNo: 'N1022',
      endNo: 'N1222',
      deep: '166m',
      create: '张三',
      createTime: '2022 02-25'
   }, {
      no: 'SD1023',
      way: '武汉市东湖路',
      name: '武汉市东湖隧道',
      structure: '锲形结构',
      length: '13600m',
      scale: '中型规模',
      startNo: 'N1022',
      endNo: 'N1222',
      deep: '166m',
      create: '张三',
      createTime: '2022 02-25'
   }, {
      no: 'SD1023',
      way: '武汉市东湖路',
      name: '武汉市东湖隧道',
      structure: '锲形结构',
      length: '13600m',
      scale: '中型规模',
      startNo: 'N1022',
      endNo: 'N1222',
      deep: '166m',
      create: '张三',
      createTime: '2022 02-25'
   }, {
      no: 'SD1023',
      way: '武汉市东湖路',
      name: '武汉市东湖隧道',
      structure: '锲形结构',
      length: '13600m',
      scale: '中型规模',
      startNo: 'N1022',
      endNo: 'N1222',
      deep: '166m',
      create: '张三',
      createTime: '2022 02-25'
   }
]);
const addBtnTapHandler = (type) => {
   switch (type) {
      case 'sure':
         state.addModal = false;
         break;
      case 'cancel':
         state.addModal = false;
         break;
   }
}

const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);
const tables = ref([]);
//const visible = ref(false);
const fileList = ref([]);
//const excelTitle = ref("导入Excel表");
const menuOptions = ref([]);
const isExpandAll = ref(false);
const refreshTable = ref(true);
/*** 工程导入参数 */
const upload = reactive({
   // 是否显示弹出层（工程导入）
   open: false,
   // 弹出层标题（工程导入）
   title: "",
   // 是否禁用上传
   isUploading: false,
   // 是否更新已经存在的工程数据
   //updateSupport: 0,
   // 设置上传的请求头部
   headers: { Authorization: "Bearer " + getToken() },
   // 上传的地址
   url: import.meta.env.VITE_APP_BASE_API + "/project/info/import"
});

const data = reactive({
   form: {},
   queryParams: {
      pageNum: 1,
      pageSize: 10,
      name: undefined
   },
   rules: {
      name: [{ required: true, message: "工程名称不能为空", trigger: "blur" }],
      isUp: [{ required: true, message: "上下行不能为空", trigger: "blur" }],
      laneType: [{ required: true, message: "车道不能为空", trigger: "blur" }],
      status: [{ required: true, message: "工程状态不能为空", trigger: "blur" }],
      isAvailable: [{ required: true, message: "是否有效不能为空", trigger: "blur" }],
      isDeleted: [{ required: true, message: "是否删除不能为空", trigger: "blur" }]
   },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询菜单列表 */
function getList() {
   loading.value = true;
   listInfo(proxy.addDateRange(queryParams.value, dateRange.value)).then(res => {
      loading.value = false;
      total.value = res.total;
      infoList.value = res.rows;
   });
}
/** 查询菜单列表 */
function getInfoByName(aName) {
   loading.value = true;
   getByName(aName).then(res => {
      loading.value = false;
      total.value = res.total;
      infoList.value = res.rows;
   });
}

function formatIsUp(row, column, cellValue) {
   var ret = ''  //你想在页面展示的值
   if (cellValue) {
      ret = "下行"  //根据自己的需求设定
   } else {
      ret = "上行"
   }
   return ret;
}
/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}
/** 表单重置 */
function reset() {
   form.value = {
      name: undefined,
      code: undefined,
      roadName: undefined,
      roadCode: undefined,
      startStake: undefined,
      endStake: undefined,
      isUp: undefined,
      laneType: undefined,
      startTime: undefined,
      endTime: undefined,
      isAvailable: undefined,
      isDeleted: undefined
   };
   proxy.resetForm("infoRef");
}
/** 搜索框清空 */
function clearName() {
   queryParams.value.name = undefined;
   getList();
}
/** 搜索按钮操作 */
function handleSearch() {
   let aname = queryParams.value.name;
   console.log(aname);
   if (aname != null) {
      console.log(aname);
      reset();
      getInfoByName(aname);
   }
}
/** 查询按钮操作 */
function handleQuery() {
   queryParams.value.pageNum = 1;
   getList();
}
/** 重置按钮操作 */
function resetQuery() {
   dateRange.value = [];
   proxy.resetForm("queryRef");
   handleQuery();
}
/** 新增按钮操作 */
function handleAdd() {
   state.addModal = true;
}
/** 修改按钮操作 */
async function handleUpdate(row) {
   reset();
   selectInfo(row.id).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "修改工程";
   });
}
/** 提交按钮 */
function submitForm() {
   proxy.$refs["infoRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            updateInfo(form.value, form.value.id).then(response => {
               proxy.$modal.msgSuccess("修改成功");
               open.value = false;
               if (queryParams.value.name != undefined) {
                  handleSearch();
               }
               else {
                  getList();
               }

            });
         } else {
            addInfo(form.value).then(response => {
               proxy.$modal.msgSuccess("新增成功");
               open.value = false;
               if (queryParams.value.name != undefined) {
                  handleSearch();
               }
               else {
                  getList();
               }
            });
         }
      }
   });
}
/** 删除按钮操作 */
function handleDelete(row) {
   proxy.$modal.confirm('是否确认删除名称为"' + row.name + '"的数据项?').then(function () {
      return delInfo(row.id);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
   }).catch(() => { });
}
/** 导出按钮操作 */
function handleExport() {
   let filename = "工程信息_" + new Date().getTime() + ".xlsx";
   proxy.download("/project/info/export", {
      //...queryParams.value,
   }, filename);//'job_${new Date().getTime()}.xlsx');
   //proxy.download(exportInfos().then(res => {res.data,);
   /*let msg1 ='';
   exportInfos().then(res => {
    console.log(res);
    msg1 = res.msg;
    console.log(msg1);
    //proxy.download(msg1,{},msg1);
   
    //var a = document.createElement("a");
    ///a.download = msg1;
    //a.href = "http://localhost:8080/download/";
    //("body").append(a);//修复firefox中无法触发click/profile
    //a.click();
    //(a).remove();
    window.location.href = "http://localhost:8080/download/"+ msg1;
   })*/
}

function handleImport() {
   upload.open = true;
   upload.title = "导入工程表";
   //visible.value = true;
}
/** 导入按钮操作 */
function handleImpTable() {
   console.log(fileList);
   //importTable(fileList).then(res => {
   //  proxy.$modal.msgSuccess(res.msg);
   //  if (res.code === 200) {
   //    visible.value = false;
   //    emit("ok");
   //  }
   //});
}
/** 下载模板操作 */
function importTemplate() {
   proxy.download("system/user/importTemplate", {
   }, `user_template_${new Date().getTime()}.xlsx`);
};
/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
   upload.isUploading = true;
};
/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
   upload.open = false;
   upload.isUploading = false;
   proxy.$refs["uploadRef"].handleRemove(file);
   proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
   getList();
};
/** 提交上传文件 */
function submitFileForm() {
   console.log(ref["uploadRef"]);
   proxy.$refs["uploadRef"].submit();
};
</script>
