<template>
    <div class="project-add-container">
        <el-form :model="state.addForm" :rules="state.rules" ref="queryRef">
            <el-row>
                <div class="header-section">
                    <span class="header-title">隧道基础信息录入</span>
                </div>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="隧道编号" prop="name" label-width="auto">
                        <el-input v-model="state.addForm.code" placeholder="请输入项目编码" clearable @keyup.enter="handleSearch"
                            @clear="clearName" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="隧道名称" prop="name" label-width="auto">
                        <el-input v-model="state.addForm.name" placeholder="请输入项目名称" clearable @keyup.enter="handleSearch"
                            @clear="clearName" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="隶属单位" prop="name" label-width="auto">
                        <el-input v-model="state.addForm.name" placeholder="请输入" clearable @keyup.enter="handleSearch"
                            @clear="clearName" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="所属路段" prop="name" label-width="auto">
                        <el-date-picker v-model="state.addForm.startDate" type="date" placeholder="选择开始日期" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="结构形式" prop="name" label-width="auto">
                        <el-date-picker v-model="state.addForm.endDate" type="date" placeholder="选择截止日期" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="交工时间" prop="name" label-width="auto">
                        <el-select v-model="state.addForm.status" class="m-2" placeholder="请选择状态" size="large">
                            <el-option v-for="item in state.statusList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="竣工时间" prop="name" label-width="auto">
                        <el-input v-model="state.addForm.code" placeholder="请输入甲方负责人" clearable @keyup.enter="handleSearch"
                            @clear="clearName" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="经度" prop="name" label-width="auto">
                        <el-input v-model="state.addForm.name" placeholder="请输入乙方负责人" clearable @keyup.enter="handleSearch"
                            @clear="clearName" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="纬度" prop="name" label-width="auto">
                        <el-select v-model="state.addForm.status" class="m-2" placeholder="请选择隶属单位" size="large">
                            <el-option v-for="item in state.companyList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <div class="header-section">
                    <span class="header-title">隧道结构信息录入</span>
                </div>
            </el-row>
            <el-row>
                <div class="header-section">
                    <span class="sub-title">左洞</span>
                </div>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="起讫桩号" prop="name" label-width="auto">
                        <el-input v-model="state.addForm.name" placeholder="请输入" clearable @keyup.enter="handleSearch"
                            @clear="clearName" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="施工中心桩号" prop="name" label-width="auto">
                        <el-input v-model="state.addForm.name" placeholder="请输入" clearable @keyup.enter="handleSearch"
                            @clear="clearName" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="隧道长度" prop="name" label-width="auto">
                        <el-input v-model="state.addForm.name" placeholder="请输入" clearable @keyup.enter="handleSearch"
                            @clear="clearName" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="埋深范围" prop="name" label-width="auto">
                        <el-input v-model="state.addForm.name" placeholder="请输入" clearable @keyup.enter="handleSearch"
                            @clear="clearName" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="运营期中心桩号" prop="name" label-width="auto">
                        <el-input v-model="state.addForm.name" placeholder="请输入" clearable @keyup.enter="handleSearch"
                            @clear="clearName" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <div class="header-section">
                    <span class="sub-title">右洞</span>
                </div>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="起讫桩号" prop="name" label-width="auto">
                        <el-input v-model="state.addForm.name" placeholder="请输入" clearable @keyup.enter="handleSearch"
                            @clear="clearName" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="施工中心桩号" prop="name" label-width="auto">
                        <el-input v-model="state.addForm.name" placeholder="请输入" clearable @keyup.enter="handleSearch"
                            @clear="clearName" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="隧道长度" prop="name" label-width="auto">
                        <el-input v-model="state.addForm.name" placeholder="请输入" clearable @keyup.enter="handleSearch"
                            @clear="clearName" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="埋深范围" prop="name" label-width="auto">
                        <el-input v-model="state.addForm.name" placeholder="请输入" clearable @keyup.enter="handleSearch"
                            @clear="clearName" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="运营期中心桩号" prop="name" label-width="auto">
                        <el-input v-model="state.addForm.name" placeholder="请输入" clearable @keyup.enter="handleSearch"
                            @clear="clearName" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div class="btn-section">
            <el-button @click="btnAction('cancel')" class="btn">取消</el-button>
            <el-button @click="btnAction('sure')" type="primary" class="btn sure">确定</el-button>
        </div>
    </div>
</template>
    
<script setup name="projectDetail">
import { ref, reactive } from 'vue';
const emit = defineEmits(['btnTap']);
const state = reactive({
    addForm: {
        startDate: '',
        endDate: ''
    },
    rules: [],
    statusList: [
        {
            label: '施工中',
            value: 'sgz'
        }, {
            label: '未开始',
            value: 'wks'
        }, {
            label: '已竣工',
            value: 'yjg'
        }, {
            label: '停工',
            value: 'tg'
        }
    ],
    companyList: [
        {
            label: '中国交建',
            value: 'zgjj'
        }, {
            label: '中交三局',
            value: 'zjsj'
        }, {
            label: '中铁三局',
            value: 'ztsj'
        }
    ]
})

const btnAction = (type) => {
    emit('btnTap', type);
}
</script>
    
<style lang="scss" scoped>
:deep(.el-input) {
    width: 250px;
}

:deep(.el-textarea__inner) {
    width: 250px;
}

.btn-section {
    text-align: right;
}

.header-section {
    padding-bottom: 20px;

    .header-title {
        font-size: 16px;
        // font-weight: 600;
    }
}
</style>