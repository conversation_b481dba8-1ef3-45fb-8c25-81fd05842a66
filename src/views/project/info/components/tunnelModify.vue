<template>
    <div class="project-add-container">
        <el-form :model="state.addForm" :rules="state.rules" ref="queryRef">
            <el-row>
                <el-col :span="8">
                    <el-form-item label="项目编码" prop="name" label-width="auto">
                        <el-input v-model="state.addForm.code" placeholder="请输入项目编码" clearable @keyup.enter="handleSearch"
                            @clear="clearName" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="项目名称" prop="name" label-width="auto">
                        <el-input v-model="state.addForm.name" placeholder="请输入项目名称" clearable @keyup.enter="handleSearch"
                            @clear="clearName" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="总里程" prop="name" label-width="auto">
                        <el-input v-model="state.addForm.name" placeholder="请输入总里程" clearable @keyup.enter="handleSearch"
                            @clear="clearName" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="开始时间" prop="name" label-width="auto">
                        <el-date-picker v-model="state.addForm.startDate" type="date" placeholder="选择开始日期" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="截止时间" prop="name" label-width="auto">
                        <el-date-picker v-model="state.addForm.endDate" type="date" placeholder="选择截止日期" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="状态" prop="name" label-width="auto">
                        <el-select v-model="state.addForm.status" class="m-2" placeholder="请选择状态" size="large">
                            <el-option v-for="item in state.statusList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="甲方负责人" prop="name" label-width="auto">
                        <el-input v-model="state.addForm.code" placeholder="请输入甲方负责人" clearable @keyup.enter="handleSearch"
                            @clear="clearName" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="乙方负责人" prop="name" label-width="auto">
                        <el-input v-model="state.addForm.name" placeholder="请输入乙方负责人" clearable @keyup.enter="handleSearch"
                            @clear="clearName" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="隶属单位" prop="name" label-width="auto">
                        <el-select v-model="state.addForm.status" class="m-2" placeholder="请选择隶属单位" size="large">
                            <el-option v-for="item in state.companyList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="隧道检测数量" prop="name" label-width="auto">
                        <el-input v-model="state.addForm.code" placeholder="请输入隧道检测数量" clearable @keyup.enter="handleSearch"
                            @clear="clearName" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="备注" prop="name" label-width="auto">
                        <el-input type="textarea" v-model="state.addForm.name" placeholder="请输入项目备注" clearable
                            @keyup.enter="handleSearch" @clear="clearName" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div class="btn-section">
            <el-button @click="btnAction('cancel')" class="btn">取消</el-button>
            <el-button @click="btnAction('sure')" type="primary" class="btn sure">确定</el-button>
        </div>
    </div>
</template>
    
<script setup name="projectDetail">
import { ref, reactive } from 'vue';
const emit = defineEmits(['btnTap']);
const state = reactive({
    addForm: {
        startDate: '',
        endDate: ''
    },
    rules: [],
    statusList: [
        {
            label: '施工中',
            value: 'sgz'
        }, {
            label: '未开始',
            value: 'wks'
        }, {
            label: '已竣工',
            value: 'yjg'
        }, {
            label: '停工',
            value: 'tg'
        }
    ],
    companyList: [
        {
            label: '中国交建',
            value: 'zgjj'
        }, {
            label: '中交三局',
            value: 'zjsj'
        }, {
            label: '中铁三局',
            value: 'ztsj'
        }
    ]
})

const btnAction = (type) => {
    emit('btnTap', type);
}
</script>
    
<style lang="scss" scoped>
:deep(.el-input) {
    width: 250px;
}

:deep(.el-textarea__inner) {
    width: 250px;
}

.btn-section {
    text-align: right;
}
</style>