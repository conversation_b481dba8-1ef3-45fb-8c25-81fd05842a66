<template xmlns="http://www.w3.org/1999/html">
  <div v-if="userId == 1 || userId == 102" id="container" style="height: 100%;width: 100%;z-index: 1;  position: absolute;"></div>
  <el-row v-if="userId == 1 || userId == 102" class="title-top">
    <el-col :span="6" style="pointer-events:auto;">
      <dv-decoration-8 style="width:100%;height:68px;"/>
    </el-col>
    <el-col :span="12" style="pointer-events:auto;text-align: center;margin-top: 11px;">
      <span style="color: white;font-size: 24px;font-weight: bold">
        隧道检测数字化平台
      </span>
      <dv-decoration-5 style="width:100%;height:50px;margin-top: -20px;" :dur="3">
      </dv-decoration-5>
    </el-col>
    <el-col :span="6" style="pointer-events:auto;">
      <dv-decoration-8 :reverse="true" style="width:100%;height:68px;"/>
    </el-col>
  </el-row>
  <el-row v-if="userId == 1 || userId == 102" class="foreground-div" style="pointer-events:none;">
    <el-col :span="6" style="pointer-events:auto;">
      <dv-border-box-12 style="height: 60px;" backgroundColor="rgb(255,255,255,0.1)">
        <el-row style="padding-top: 14px;margin-left: 5%;">
            <el-select v-model="tunnelCode" placeholder="请输入/选择隧道名称" clearable filterable style="width:95%;" @change="selectChange">
              <el-option
                  v-for="item in tunnels"
                  :key="item.tunnelCode"
                  :label="item.tunnelName"
                  :value="item.longitudeAndLatitude">
              </el-option>
              <template #prefix><svg-icon icon-class="search" /></template>
            </el-select>

        </el-row>
      </dv-border-box-12>
      <dv-border-box-12 style="height: 350px;" backgroundColor="rgb(255,255,255,0.1)">
        <el-row style="height: 350px;">
          <div style="width: 100%;text-align: center;margin-top: 20px;color: white;font-weight: bold;">隧道地理位置统计</div>
          <dv-scroll-board :config="configTable" style="color:#3de7c9; font-weight: bolder;font-size: 24px; left: 10% ;width:80%;height:80%;top:2%;"/>

        </el-row>
      </dv-border-box-12>
      <dv-border-box-12 style="height: 430px;" backgroundColor="rgb(255,255,255,0.1)">
        <el-row style="height: 100%;">
<!--                    <dv-charts :option="optionCircle"/>-->
          <div style="width: 100%;text-align: center;margin-top: 20px;color: white;font-weight: bold;font-size: 16px;">病害数据占比</div>
          <dv-active-ring-chart :config="config" style="width:100%;height:100%;margin-top: -30px;"/>
        </el-row>
      </dv-border-box-12>
    </el-col>
    <el-col :span="12" style="pointer-events:none;text-align:center;margin-top: 20px;">
    </el-col>
    <el-col :span="6" style="pointer-events:auto;">
      <el-row>
        <el-col :span="8">
          <dv-border-box-12 style="height: 120px;width: 100%;" backgroundColor="rgb(255,255,255,0.1)">
            <el-row style="width:100%;height:50px;padding-top: 25px;text-align: center;">
              <dv-digital-flop :config="configOne" style="width:100%;height:50px;"/>
              <span style="color: #3de7c9;text-align: center;width: 100%">总里程(km)</span>
            </el-row>
          </dv-border-box-12>
        </el-col>
        <el-col :span="8">
          <dv-border-box-12 style="height: 120px;width: 100%;" backgroundColor="rgb(255,255,255,0.1)">
            <el-row style="width:100%;height:50px;padding-top: 25px;text-align: center;">
              <dv-digital-flop :config="configTwo" style="width:100%;height:50px;"/>
              <span style="color: #3de7c9;text-align: center;width: 100%">隧道数(座)</span>
            </el-row>
          </dv-border-box-12>
        </el-col>
        <el-col :span="8">
          <dv-border-box-12 style="height: 120px;width: 100%;" backgroundColor="rgb(255,255,255,0.1)">
            <el-row style="width:100%;height:50px;padding-top: 25px;text-align: center;">
              <dv-digital-flop :config="configThree" style="width:100%;height:50px;"/>
              <span style="color: #3de7c9;text-align: center;width: 100%">总病害数</span>
            </el-row>
          </dv-border-box-12>
        </el-col>
      </el-row>
      <dv-border-box-12 style="height: 320px;" backgroundColor="rgb(255,255,255,0.1)">
        <el-row style="height:100%;">
          <dv-charts :option="optionZhu"/>
        </el-row>
      </dv-border-box-12>
      <dv-border-box-12 style="height: 400px;" backgroundColor="rgb(255,255,255,0.1)">
        <el-row style="height: 100%;">
          <dv-charts :option="optionHeng"/>
        </el-row>
      </dv-border-box-12>
    </el-col>
  </el-row>

</template>

<script>

import {getTunnelList, queryIndexDTO} from "@/api/project/indexData";
import {getUser} from "@/api/system/user";
import { getInfo } from '@/api/login'



export default {
  name: "index",
  data() {
    return {
      userId:null,
      map:{},
      tunnelCode:null,
      tunnels:[],
      data:{},
      proxy:null,
      config: {
        color:[
          "#37a2da",
          "#ffdb5c",
          "#9fe6b8",
          "#ff9f7f",
          "#67e0e3",
          "#FFDFFF"
        ],
        digitalFlopStyle:[2],
        data: []
      },
      configTable: {
        header: ['省', '市', '隧道数(座)'],
        data: [],
        headerBGC: '',
        oddRowBGC: '',
        evenRowBGC: '',
      },
      configOne: {
        number: [0],
        content: '{nt}'
      },
      configTwo: {
        number: [0],
        content: '{nt}'
      },
      configThree: {
        number: [0],
        content: '{nt}'
      },
      tunnelName: null,
      optionCircle: {
        color:[
          "#37a2da",
          "#ffdb5c",
          "#9fe6b8",
          "#ff9f7f",
          "#67e0e3",
          "#FFDFFF"
        ],
        title: {
          text: '畅销饮料占比饼状图',
          style: {
            fill: '#ffffff',
            fontSize: 17,
            fontWeight: 'bold',
          }
        },
        series: [
          {
            type: 'pie',
            data: [
              {name: '可口可乐', value: 93},
              {name: '百事可乐', value: 32},
              {name: '哇哈哈', value: 65},
              {name: '康师傅', value: 44},
              {name: '统一', value: 52},
            ],
            insideLabel: {
              show: true
            }
          }
        ]
      },
      optionZhu: {
        title: {
          text: '检测隧道规模统计',
          style: {
            fill: '#ffffff',
            fontSize: 17,
            fontWeight: 'bold',
          }
        },
        xAxis: {
          name: '规模',
          data: [],
          nameTextStyle: {
            fill: '#ffffff',
            fontSize: 10
          },
          axisLine: {
            style: {
              stroke: '#ffffff',
              lineWidth: 1
            }
          },
          axisLabel: {
            style: {
              fill: '#ffffff',
              fontSize: 10,
              rotate: 0
            }
          }
        },
        yAxis: {
          name: '座',
          data: 'value',
          splitLine: {
            show: false
          },
          nameTextStyle: {
            fill: '#ffffff',
            fontSize: 10
          },
          axisLine: {
            style: {
              stroke: '#ffffff',
              lineWidth: 1
            }
          },
          axisLabel: {
            style: {
              fill: '#ffffff',
              fontSize: 10,
              rotate: 0
            }
          }
        },
        series: [
          {
            data: [],
            type: 'bar',
            gradient: {
              color: ['#37a2da', '#67e0e3']
            }
          }
        ]
      },
      optionHeng: {
        title: {
          text: '隧道等级分布',
          style: {
            fill: '#ffffff',
            fontSize: 17,
            fontWeight: 'bold',
          }
        },
        xAxis: {
          name: '座',
          data: 'value',
          min:0,
          nameTextStyle: {
            fill: '#ffffff',
            fontSize: 10
          },
          axisLine: {
            style: {
              stroke: '#ffffff',
              lineWidth: 1
            }
          },
          axisLabel: {
            style: {
              fill: '#ffffff',
              fontSize: 10,
              rotate: 0
            }
          }
        },
        yAxis: {
          name: '等级',
          data: [],
          splitLine: {
            show: false
          },
          nameTextStyle: {
            fill: '#ffffff',
            fontSize: 10
          },
          axisLine: {
            style: {
              stroke: '#ffffff',
              lineWidth: 1
            }
          },
          axisLabel: {
            style: {
              fill: '#ffffff',
              fontSize: 10,
              rotate: 0
            }
          }
        },
        series: [
          {
            data: [],
            type: 'bar',
            gradient: {
              color: ['#37a2da', '#67e0e3']
            }
          }
        ]
      }
    };
  },
  created() {
    this.$nextTick(() => {
      this.getUserId();

    })

  },
  methods: {
    getUserId(){
      let _this=this;
      getInfo().then(response => {
        let user = response.user;
        if(null != user){
          _this.userId=user.userId;
        }
        _this.getTunnel();
        _this.queryIndexDTO();
      });
    },
    selectChange(value){
      if(null== value || value == undefined || value == ''){
        this.$modal.msgSuccess("隧道经纬度未初始化");
        return;
      }
      let longitude=value.split(",")[0];
      let latitude=value.split(",")[1];
      if(longitude !=null && longitude!='' && latitude !=null && latitude!=''){
        this.initMap(longitude,latitude,9);
      }
    },
    getTunnel() {
      let _this=this;
      getTunnelList().then(response => {
        this.tunnels = response.data;
        for (const item of this.tunnels) {
          item.longitudeAndLatitude=item.longitude+","+item.latitude;
        }
        if(_this.userId==102){
          this.initMap(116.912,31.596606,8);
        }else{
          this.initMap(107.167267, 34.386015,5);
        }
      });
    },
    queryIndexDTO() {
      let _this=this;
      let req={};
      req.userId=_this.userId;
      queryIndexDTO(req).then(res => {
        let data=res.data;
        //总里程
        _this.configOne.number=new Array();
        _this.configOne.number.push(data.totalMiles);
        _this.configOne={..._this.configOne};
        _this.configTwo.number=new Array();
        _this.configTwo.number.push(data.totalTunnel);
        _this.configTwo={..._this.configTwo};

        _this.configThree.number=new Array();
        _this.configThree.number.push(data.totalDisease);
        _this.configThree={..._this.configThree};

        //饼图
        _this.config.data.push({'name':'衬砌裂缝','value':data.little});
        _this.config.data.push({'name':'渗漏水','value':data.medium});
        _this.config.data.push({'name':'剥落','value':data.big});
        _this.config.data.push({'name':'钢筋锈蚀','value':data.biggest});

        _this.config={..._this.config};
        //隧道里程统计
        for (const temp of data.milesInfoList) {
          _this.optionZhu.xAxis.data.push(temp.province);
          _this.optionZhu.series[0].data.push(temp.count);
        }
        _this.optionZhu={..._this.optionZhu}
        //隧道数量排行
        for (const temp of data.list) {
          let req=[];
          req.push(temp.province);
          req.push(temp.city);
          req.push(temp.count);
          _this.configTable.data.push(req);
        }
        _this.configTable={..._this.configTable};
        //隧道等级分布
        _this.optionHeng.yAxis.data.push('一类');
        _this.optionHeng.yAxis.data.push('二类');
        _this.optionHeng.yAxis.data.push('三类');
        _this.optionHeng.yAxis.data.push('四类');
        _this.optionHeng.yAxis.data.push('五类');
        _this.optionHeng.series[0].data.push(data.rankOne);
        _this.optionHeng.series[0].data.push(data.rankTwo);
        _this.optionHeng.series[0].data.push(data.rankThree);
        _this.optionHeng.series[0].data.push(data.rankFour);
        _this.optionHeng.series[0].data.push(data.rankFive);
        _this.optionHeng={..._this.optionHeng};


      });
    },
    initCircle() {
      // const container = document.getElementById('container')
      // const myChart = new Charts(container);

      // myChart.setOption(option1);
    },
    initMap(longitude,latitude,rank) {
      let map = new BMapGL.Map("container");
      let point = new BMapGL.Point(longitude, latitude);
      map.centerAndZoom(point, rank);
      map.setMapStyleV2({
        styleId: '6a917b2cdcbcfa9836460ecf2fbc9113'
      });
      map.enableScrollWheelZoom(true);
      for (let tunnel of this.tunnels) {
        if(tunnel.longitude !=null && tunnel.longitude!='' && tunnel.latitude !=null && tunnel.latitude!=''){
          let point2 = new BMapGL.Point(tunnel.longitude, tunnel.latitude);
          let marker2 = new BMapGL.Marker(point2);
          map.addOverlay(marker2);
          let opts = {
            width : 300,     // 信息窗口宽度
            height: 100,     // 信息窗口高度
            title : "隧道信息" , // 信息窗口标题
            // message:"隧道名称:"+tunnel.tunnelName
          }
          let infoWindowTemp = new BMapGL.InfoWindow("隧道名称:"+tunnel.tunnelName+",隧道总编码:"+tunnel.totalTunnelCode+",隧道编码:"+tunnel.tunnelCode, opts);  // 创建信息窗口对象
          marker2.addEventListener("click", function(){
            map.openInfoWindow(infoWindowTemp, point2); //开启信息窗口
          });
        }
      }
      if(this.userId==102){
        let anhui = new BMapGL.Boundary();
        anhui.get('安徽省', function (rs) {
          // console.log('外轮廓：', rs.boundaries[0])
          // console.log('内镂空：', rs.boundaries[1])
          let hole = new BMapGL.Polygon(rs.boundaries, {
            fillColor: '#00F5FF',
            fillOpacity: 0.2,
            strokeWeight: 3,
            strokeColor: "gray"
          });
          map.addOverlay(hole);
        });
        return;
      }


      let anhui = new BMapGL.Boundary();
      anhui.get('安徽省', function (rs) {
        // console.log('外轮廓：', rs.boundaries[0])
        // console.log('内镂空：', rs.boundaries[1])
        let hole = new BMapGL.Polygon(rs.boundaries, {
          fillColor: '#00F5FF',
          fillOpacity: 0.2,
          strokeWeight: 3,
          strokeColor: "gray"
        });
        map.addOverlay(hole);
      });

      let hubei = new BMapGL.Boundary();
      hubei.get('湖北省', function (rs) {
        // console.log('外轮廓：', rs.boundaries[0])
        // console.log('内镂空：', rs.boundaries[1])
        let hole = new BMapGL.Polygon(rs.boundaries, {
          fillColor: '#00F5FF',
          fillOpacity: 0.2,
          strokeWeight: 3,
          strokeColor: "gray"
        });
        map.addOverlay(hole);
      });

      let yunnan = new BMapGL.Boundary();
      yunnan.get('云南省', function (rs) {
        // console.log('外轮廓：', rs.boundaries[0])
        // console.log('内镂空：', rs.boundaries[1])
        let hole = new BMapGL.Polygon(rs.boundaries, {
          fillColor: '#00F5FF',
          fillOpacity: 0.2,
          strokeWeight: 3,
          strokeColor: "gray"
        });
        map.addOverlay(hole);
      });


      let gansu = new BMapGL.Boundary();
      gansu.get('甘肃省', function (rs) {
        // console.log('外轮廓：', rs.boundaries[0])
        // console.log('内镂空：', rs.boundaries[1])
        let hole = new BMapGL.Polygon(rs.boundaries, {
          fillColor: '#00F5FF',
          fillOpacity: 0.2,
          strokeWeight: 3,
          strokeColor: "gray"
        });
        map.addOverlay(hole);
      });
    },
  }
};

</script>
<style lang="scss" scoped>

.myInput {
  width: 215px
}

/* 必须设定高度 */
.bm-view {
  width: 100%;
  height: 300px;
}

:deep(.anchorBL) {
  display: none;
}
</style>

<style scoped lang="scss">
.title-top {
  width: 100%;
  height: 80px;
  z-index: 2; /* 设置较高的z-index值 */
  background: rgb(255, 255, 255, 0.1);
}

.foreground-div {
  //position: absolute;
  width: 100%;
  height: 100%;
  //top: 50px;
  //left: 10%;
  z-index: 2; /* 设置较高的z-index值 */
  //background-color: #ff0000;
  //display: flex;
}

:deep(.dv-border-box-11-title[fill]) {
  fill: #96d8fd !important; // 更换颜色
}

//背景色设置为透明
:deep(.el-input__wrapper) {
  //background-color:rgba(0,0,0,0);
  background: transparent !important;
  box-shadow: unset;
  font-size: 16px;
}

//输入框颜色
:deep(.el-input__inner) {
  background-color: rgba(0, 0, 0, 0) !important;
  color: #fff;
}

//历史记录后自动补充的颜色
:deep(input:-webkit-autofill) {
  -webkit-text-fill-color: #fff;
  transition: background-color 5000s ease-out 0.5s;
}

//提示框的字体颜色
:deep(input::-webkit-input-placeholder) {
  color: #fff;
}
</style>

