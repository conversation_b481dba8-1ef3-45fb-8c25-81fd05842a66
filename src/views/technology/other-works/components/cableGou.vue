<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          @click="handleAdd"
          v-hasPermi="['other:cableGou:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['other:cableGou:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          size="mini"
          @click="handleExport"
          v-hasPermi="['other:cableGou:export']"
          >导出</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          @click="handleBatchAdd"
          v-hasPermi="['other:cableGou:batchAdd']"
          >批量新增</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="cableGouList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" fixed="left" label="序号" width="70" />
      <el-table-column
        v-for="(item, index) in tableConfig"
        :key="index"
        :prop="item.prop"
        show-tooltip-when-overflow="true"
        :label="item.label"
        :min-width="item?.width || 120"
      />
      <!-- <el-table-column
        label="项目名称"
        align="center"
        prop="projectName"
        width="150"
      />
      <el-table-column
        label="项目编码"
        align="center"
        prop="projectCode"
        width="150"
      />
      <el-table-column
        label="隧道名称"
        align="center"
        prop="tunnelName"
        width="150"
      />
      <el-table-column
        label="隧道编码"
        align="center"
        prop="tunnelCode"
        width="150"
      />
      <el-table-column
        label="开始时间"
        align="center"
        prop="beginTime"
        width="180"
      >
      </el-table-column>
      <el-table-column
        label="截至时间"
        align="center"
        prop="endTime"
        width="180"
      >
      </el-table-column>
      <el-table-column label="桩号" align="center" prop="pileNumber" />
      <el-table-column label="缺损位置" align="center" prop="defectLocation" />
      <el-table-column
        label="状况描述"
        align="center"
        prop="conditionDiscreption"
      />
      <el-table-column label="状况值" align="center" prop="conditionValue" />
      <el-table-column label="图片编号" align="center" prop="pictureNum" />
      <el-table-column label="创建人名称" align="center" prop="creatorName" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="creatorTime"
        width="180"
      >
      </el-table-column>
      <el-table-column label="修改人名称" align="center" prop="modifierName" />
      <el-table-column
        label="修改时间"
        align="center"
        prop="modifierTime"
        width="180"
      >
      </el-table-column> -->
      <el-table-column
        label="操作"
        align="center"
        fixed="right"
        width="200"
        class-name="small-padding fixed-width"
      >
        <template v-slot:default="scope">
          <el-button
            size="mini"
            type="success"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['other:cableGou:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(scope.row)"
            v-hasPermi="['other:cableGou:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改对话框 -->
    <el-dialog :title="title" v-model="open" width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="项目名称/编码" prop="projectId">
          <el-select
            v-model="form.projectId"
            placeholder="请选择"
            clearable
            filterable
            @change="getTunnelList"
          >
            <el-option
              v-for="item in projectList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="隧道名称/编码" prop="tunnelId">
          <el-select
            v-model="form.tunnelId"
            placeholder="请选择"
            clearable
            filterable
          >
            <el-option
              v-for="item in tunnelList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="开始~截至时间:" prop="date1">
          <el-date-picker
            v-model="form.date1"
            type="daterange"
            range-separator="~"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            start-placeholder="开始时间"
            end-placeholder="截至时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="桩号" prop="pileNumber">
          <el-input v-model="form.pileNumber" placeholder="请输入桩号" />
        </el-form-item>
        <el-form-item label="缺损位置" prop="defectLocation">
          <el-input
            v-model="form.defectLocation"
            placeholder="请输入缺损位置"
          />
        </el-form-item>
        <el-form-item label="状况描述" prop="conditionDiscreption">
          <el-input
            v-model="form.conditionDiscreption"
            placeholder="请输入状况描述"
          />
        </el-form-item>
        <el-form-item label="状况值" prop="conditionValue">
          <el-input v-model="form.conditionValue" placeholder="请输入状况值" />
        </el-form-item>
        <el-form-item label="图片编号" prop="pictureNum">
          <el-input v-model="form.pictureNum" placeholder="请输入图片编号" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 批量上传 -->
    <excelUpload :file="file" @refreshDataList="handleQuery"></excelUpload>
  </div>
</template>

<script>
import {
  listCableGou,
  getCableGou,
  delCableGou,
  addCableGou,
  updateCableGou,
} from "@/api/other/cableGou";
import { listProjectAll } from "@/api/system/project";
import { listTunnelAll } from "@/api/system/tunnlel";
import { tableConfig } from '../constants'
import excelUpload from "@/views/detection/technology/data/fileUpload/excelUpload.vue";
export default {
  components: {
    excelUpload,
  },
  name: "cableGou",
  data() {
    return {
      tableConfig,
      file: {
        batchAddOpen: false,
        templateUrl: "cable_gou.xlsx",
        uploadUrl: "/other/cableGou/batchAdd",
        excelName: "电缆沟.xlsx",
      },
      sun: "",
      projectList: [],
      tunnelList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      cableGouList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectTunnelInspectionId: null,
        pileNumber: null,
        defectLocation: null,
        projectId: null,
        tunnelId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        projectId: [
          { required: true, message: "项目不能为空", trigger: "blur" },
        ],
        tunnelId: [
          { required: true, message: "隧道不能为空", trigger: "blur" },
        ],
        projectTunnelInspectionId: [
          {
            required: true,
            message: "项目隧道关联ID不能为空",
            trigger: "blur",
          },
        ],
        pileNumber: [
          { required: true, message: "桩号不能为空", trigger: "blur" },
        ],
        conditionValue: [
          { pattern: /^[0-4]?$/, message: "只能输0-4的整数", trigger: "blur" },
          { required: true, message: "状况值不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.getTunnelList();
    this.getProjectList();
  },
  methods: {
    handleBatchAdd() {
      this.file.batchAddOpen = true;
      this.file.batchAddTitle = "批量新增";
    },
    getProjectList() {
      let _this = this;
      listProjectAll().then((response) => {
        response.forEach(function (e) {
          let arr1 = {};
          arr1.value = e.id;
          arr1.label = e.projectName + "/" + e.projectCode;
          _this.projectList.push(arr1);
        });
      });
    },
    getTunnelList() {
      let _this = this;
      let data = {};
      _this.tunnelList = [];
      if (null != _this.form.projectId && "" != _this.form.projectId) {
        data.projectId = _this.form.projectId;
      }
      listTunnelAll(data).then((response) => {
        response.forEach(function (e) {
          let arr1 = {};
          arr1.value = e.id;
          arr1.label = e.tunnelName + "/" + e.tunnelCode;
          _this.tunnelList.push(arr1);
        });
      });
    },
    /** 查询列表 */
    getList(query = {}) {
      this.loading = true;
      this.queryParams = { ...this.queryParams, ...query };
      listCableGou(this.queryParams).then((response) => {
        this.cableGouList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectTunnelInspectionId: null,
        beginTime: null,
        endTime: null,
        pileNumber: null,
        defectLocation: null,
        conditionDiscreption: null,
        conditionValue: null,
        pictureNum: null,
        inspectionTime: null,
        creatorCode: null,
        creatorName: null,
        creatorTime: null,
        modifierCode: null,
        modifierName: null,
        modifierTime: null,
        isAvailable: null,
        isDeleted: null,
        userId: null,
        deptId: null,
        projectId: null,
        tunnelId: null,
        date1: [],
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      let tmp = JSON.parse(JSON.stringify(row));
      this.form.beginTime = tmp.beginTime;
      this.form.id = tmp.id;
      this.form.projectId = tmp.projectId;
      this.form.tunnelId = tmp.tunnelId;
      this.form.projectTunnelInspectionId = tmp.projectTunnelInspectionId;
      this.form.beginTime = tmp.beginTime;
      this.form.endTime = tmp.endTime;
      this.form.pileNumber = tmp.pileNumber;
      this.form.defectLocation = tmp.defectLocation;
      this.form.conditionDiscreption = tmp.conditionDiscreption;
      this.form.conditionValue = tmp.conditionValue;
      this.form.pictureNum = tmp.pictureNum;
      this.form.date1 = [this.form.beginTime, this.form.endTime];
      this.open = true;
      this.title = "修改";
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.date1 != null && this.form.date1.length != 0) {
            this.form.beginTime = this.form.date1[0];
            this.form.endTime = this.form.date1[1];
          }
          if (this.form.id != null) {
            updateCableGou(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCableGou(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除编号为"' + ids + '"的数据项？')
        .then(function () {
          return delCableGou(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "other/cableGou/export",
        {
          ...this.queryParams,
        },
        `电缆沟_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.myInput {
  width: 215px;
}
</style>