<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          @click="handleAdd"
          v-hasPermi="['other:CaveTunnelNameplate:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['other:CaveTunnelNameplate:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          @click="handleExport"
          v-hasPermi="['other:CaveTunnelNameplate:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
                type="primary"
                plain
                @click="handleBatchAdd"
                v-hasPermi="['other:CaveTunnelNameplate:batchAdd']"
        >批量新增</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="caveTunnelNameplateList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号" width="60" />
      <el-table-column
        v-for="(item, index) in tableConfig"
        :key="index"
        :prop="item.prop"
        :show-tooltip-when-overflow="true"
        :label="item.label"
        :min-width="item?.width"
        align="center">
        <!-- 病害图片列 -->
        <template v-slot:default="scope" v-if="item.prop === 'defectImages'">
          <div v-if="scope.row.defectImages" class="img-list">
            <el-image
                v-for="(img, imgIndex) in scope.row.defectImages.split(',')"
                :key="imgIndex"
                :src="img"
                :preview-src-list="scope.row.defectImages.split(',')"
                style="height: 50px; width: 50px;"
                fit="cover"
                preview-teleported/>
          </div>
          <span v-else>暂无图片</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right" width="200" class-name="small-padding fixed-width">
        <template v-slot:default="scope">
          <el-button
            type="success"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['other:CaveTunnelNameplate:edit']"
          >修改</el-button>
          <el-button
            type="danger"
            @click="handleDelete(scope.row)"
            v-hasPermi="['other:CaveTunnelNameplate:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改对话框 -->
    <el-dialog :title="title" v-model="open" width="600px">
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名称" prop="projectCode">
              <el-select
                  v-model="form.projectCode"
                  placeholder="请选择"
                  clearable>
                <el-option
                    v-for="item in projects"
                    :key="item.projectCode"
                    :label="item.projectName"
                    :value="item.projectCode">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="隧道名称" prop="tunnelCode">
              <el-select v-model="form.tunnelCode" placeholder="请选择" clearable>
                <el-option
                    v-for="item in tunnels"
                    :key="item.tunnelCode"
                    :label="item.tunnelNameAndHole"
                    :value="item.tunnelCode">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="病害桩号" prop="pileNumber">
              <el-input
                  v-model="form.pileNumber"
                  placeholder="请输入病害桩号"
                  class="myInput"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="病害编号" prop="defectNumber">
              <el-input
                  v-model="form.defectNumber"
                  placeholder="请输入病害编号"
                  class="myInput"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="病害位置" prop="defectLocation">
              <el-input
                  v-model="form.defectLocation"
                  placeholder="请输入病害位置"
                  class="myInput"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="病害类型" prop="diseaseType">
              <el-input
                  v-model="form.diseaseType"
                  placeholder="请输入病害类型"
                  class="myInput"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="长度(m)" prop="lengthValue">
              <el-input
                  v-model="form.lengthValue"
                  placeholder="请输入长度"
                  class="myInput"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="宽度(m)" prop="widthValue">
              <el-input
                  v-model="form.widthValue"
                  placeholder="请输入宽度"
                  class="myInput"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="面积(m²)" prop="areaValue">
              <el-input
                  v-model="form.areaValue"
                  placeholder="请输入面积"
                  class="myInput"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检测时间" prop="inspectionTime">
              <el-date-picker
                  v-model="form.inspectionTime"
                  type="date"
                  placeholder="请选择检测时间"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="重点病害" prop="isKeyDefect">
              <el-radio-group v-model="form.isKeyDefect">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="重点病害编号" prop="keyDefectNumber">
              <el-input
                  v-model="form.keyDefectNumber"
                  placeholder="请输入重点病害编号"
                  class="myInput"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状况描述" prop="conditionDiscreption">
              <el-input
                  v-model="form.conditionDiscreption"
                  placeholder="请输入状况描述"
                  type="textarea"
                  class="myInput"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维修状态" prop="maintenanceStatus">
              <el-select v-model="form.maintenanceStatus" placeholder="请选择维修状态" clearable>
                <el-option label="未维修" value="未维修"></el-option>
                <el-option label="维修中" value="维修中"></el-option>
                <el-option label="已维修" value="已维修"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="维修时间" prop="maintenanceTime">
              <el-date-picker
                  v-model="form.maintenanceTime"
                  type="datetime"
                  placeholder="请选择维修时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="病害图片" prop="defectImages">
          <el-upload
              v-model="form.defectImages"
              class="upload-demo"
              action="/dev-api/tunnel/upload/uploadPicture"
              :on-remove="handleDefectImagesRemove"
              :on-success="defectImagesUpload"
              :show-file-list="true"
              :headers="{ Authorization: 'Bearer ' + token }"
              list-type="picture-card"
              :file-list="defectImagesLists"
              :limit="5"
              :on-exceed="handleExceed">
            <el-button type="primary">上传病害图片</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" style="text-align: center;">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 批量上传 -->
    <excelUpload :file="file" @refreshDataList="handleQuery"></excelUpload>
   </div>
</template>

<script>
import { listCaveTunnelNameplate, getCaveTunnelNameplate, delCaveTunnelNameplate, addCaveTunnelNameplate, updateCaveTunnelNameplate } from "@/api/other/caveTunnelNameplate";
import { getProjects, getTunnels } from "@/api/system/opening";
import { getToken } from "@/utils/auth";
import { tableConfig } from "../constants";
import excelUpload from "@/views/detection/technology/data/fileUpload/excelUpload.vue";
export default {
    components: {
        excelUpload
    },
  name: "caveTunnelNameplate",
  data() {
    return {
      tableConfig,
        file:{
            batchAddOpen:false,
            templateUrl:'cave_tunnel_nameplate.xlsx',
            uploadUrl:'/other/caveTunnelNameplate/batchAdd',
            excelName:'洞口雕塑隧道铭牌.xlsx'
        },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 洞内标识牌表格数据
      caveTunnelNameplateList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectCode: null,
        tunnelCode: null,
        defectNumber: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        projectCode: [
          { required: true, message: "项目编码不能为空", trigger: "blur" }
        ],
        tunnelCode: [
          { required: true, message: "隧道编码不能为空", trigger: "blur" }
        ],
        defectNumber: [
          { required: true, message: "病害编号不能为空", trigger: "blur" }
        ]
      },
      token: null,
      projects: [],
      tunnels: [],
      defectImagesLists: []
    };
  },
  created() {
    this.getList();
    this.getProject();
    this.getTunnel();
    this.token = getToken();
  },
  methods: {
      handleBatchAdd() {
          this.file.batchAddOpen = true;
          this.file.batchAddTitle = "批量新增";
      },
      getProject() {
        getProjects().then(response => {
          this.projects = response.data;
        });
      },
      getTunnel() {
        getTunnels().then(response => {
          this.tunnels = response.data;
        });
      },
      defectImagesUpload(response) {
        this.defectImagesLists.push({
          name: response.fileName,
          url: response.fileName
        });
        const images = this.defectImagesLists.map(item => item.url).join(',');
        this.form.defectImages = images;
      },
      handleDefectImagesRemove(file) {
        const index = this.defectImagesLists.findIndex(item => item.url === file.url);
        if (index > -1) {
          this.defectImagesLists.splice(index, 1);
        }
        const images = this.defectImagesLists.map(item => item.url).join(',');
        this.form.defectImages = images;
      },
    /** 查询列表 */
    getList(query={}) {
      this.loading = true;
       this.queryParams = { ...this.queryParams, ...query };
      listCaveTunnelNameplate(this.queryParams).then(response => {
        this.caveTunnelNameplateList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectCode: null,
        tunnelCode: null,
        defectNumber: null,
        pileNumber: null,
        conditionDescription: null,
        conditionValue: null,
        pictureNumber: null,
        defectImages: null
      };
      this.defectImagesLists = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getCaveTunnelNameplate(id).then(response => {
        this.form = response.data;
        if (this.form.defectImages) {
          this.defectImagesLists = this.form.defectImages.split(',').map(url => ({
            name: url,
            url: url
          }));
        }
        this.open = true;
        this.title = "修改";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          const images = this.defectImagesLists.map(item => item.url).join(',');
          this.form.defectImages = images;
          if (this.form.id != null) {
            updateCaveTunnelNameplate(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCaveTunnelNameplate(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除编号为"' + ids + '"的数据项？').then(function() {
        return delCaveTunnelNameplate(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('other/caveTunnelNameplate/export', {
        ...this.queryParams
      }, `洞口雕塑隧道铭牌_${new Date().getTime()}.xlsx`)
    },
    handleExceed(files, fileList) {
      this.$modal.msgWarning(`只能上传一个文件`);
    },
  }
};
</script>
<style lang="scss" scoped>

  .myInput {width: 215px}
</style>