export const tabsConfig = [
    {
        label: '洞口限高架',
        template: 'frame'
    },
    {
        label: '房屋设施',
        template: 'housingTreatement'
    },
    {
        label: '减光设施',
        template: 'lightReduction'
    },
    {
        label: '路消音设施',
        template: 'noiseReduction'
    },
    {
        label: '联络通道',
        template: 'passageAccess'
    },
    {
        label: '污水处理设施',
        template: 'wastewaterTreatment'
    },
    {
        label: '设备洞室',
        template: 'deviceHole'
    },
    {
        label: '洞口雕塑/隧道铭牌',
        template: 'caveTunnelNameplate'
    },
    {
        label: '洞口绿化',
        template: 'caveGreening'
    },
    {
        label: '电缆沟',
        template: 'cableGou'
    }
]
export const tableConfig = [
     {
        label: '项目名称',
        prop: 'projectName',
        width: '180'
    },
    {
        label: '所属路段',
        prop: 'affiliatedRoadSection'
    },
    {
        label: '隧道名称',
        prop: 'tunnelName'
    },
    {
        label: '检测时间',
        prop: 'inspectionTime'
    },
    {
        label: '桩号',
        prop: 'pileNumber'
    },
    {
        label: '病害位置',
        prop: 'diseaseLocation'
    },
    {
        label: '病害类型',
        prop: 'diseaseType'
    },
    {
        label: '状况描述',
        prop: 'conditionDiscreption'
    },
    {
        label: '状况值',
        prop: 'conditionValue'
    },
    {
        label: "病害照片",
        prop: 'pictureUrl',
    },
]