<template>
  <div class="app-container other-works">
    <el-form
      :model="queryParams"
      ref="ruleFormRef"
      :inline="true"
      label-width="auto"
    >
      <el-form-item label="项目名称/编码" prop="projectId">
        <el-select
          v-model="queryParams.projectId"
          placeholder="请选择"
          clearable
          filterable
        >
          <el-option
            v-for="item in projectList"
            :key="item.id"
            :label="`${item.projectName}/${item.projectCode}`"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="隧道名称/编码" prop="tunnelId">
        <el-select
          v-model="queryParams.tunnelId"
          placeholder="请选择"
          clearable
          filterable
        >
          <el-option
            v-for="item in tunnelList"
            :key="item.id"
            :label="`${item.tunnelName}/${item.tunnelCode}`"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="桩号" prop="pileNumber">
        <el-input
          v-model="queryParams.pileNumber"
          placeholder="请输入桩号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="缺损位置" prop="defectLocation">
        <el-input
          v-model="queryParams.defectLocation"
          placeholder="请输入缺损位置"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="mini" @click="handleQuery"
          >搜索</el-button
        >
        <el-button size="mini" @click="resetForm(ruleFormRef)">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="ce-tabs">
      <span class="ce-tabs-title">其他工程设施:</span>
      <span
        :class="[
          'ce-tabs-title',
          'ce-tabs-but',
          item.template === stores.template && 'ce-tabs-but_active',
        ]"
        v-for="item in tabsConfig"
        :key="item.label"
        @click="tabsChange(item)"
        >{{ item.label}}</span
      >
    </div>
    <div class="">
       <component
          ref="refOwComponent"
          :is="layouts[stores.template]"
        ></component>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, defineAsyncComponent, ref } from "vue";
import { tabsConfig } from "./constants";
import { listProjectAll } from "@/api/system/project";
import { listTunnelAll } from "@/api/system/tunnlel";
// 引入组件
const layouts: any = {
  // 洞口限高架================
  frame: defineAsyncComponent(() => import("./components/frame.vue")),
  // 房屋设施================
  housingTreatement: defineAsyncComponent(
    () => import("./components/housingTreatement.vue")
  ),
  // 减光设施================
  lightReduction: defineAsyncComponent(
    () => import("./components/lightReduction.vue")
  ),
  // 路消音设施================
  noiseReduction: defineAsyncComponent(
    () => import("./components/noiseReduction.vue")
  ),
  // 联络通道 ================
  passageAccess: defineAsyncComponent(
    () => import("./components/passageAccess.vue")
  ),
  // 污水处理设施 ================
  wastewaterTreatment: defineAsyncComponent(
    () => import("./components/wastewaterTreatment.vue")
  ),
  // 设备洞室 ================
  deviceHole: defineAsyncComponent(() => import("./components/deviceHole.vue")),
  // 洞口雕塑/隧道铭牌 ================
  caveTunnelNameplate: defineAsyncComponent(
    () => import("./components/caveTunnelNameplate.vue")
  ),
  // 洞口绿化 ================
  caveGreening: defineAsyncComponent(
    () => import("./components/caveGreening.vue")
  ),
  // 电缆沟 ================
  cableGou: defineAsyncComponent(() => import("./components/cableGou.vue")),
};
const queryParams = ref<any>({
  projectTunnelInspectionId: null,
  pileNumber: null,
  defectLocation: null,
  projectId: null,
  tunnelId: null,
});
const stores = ref<any>({
  label: "洞口限高架",
  template: "frame",
});
const tabsChange = (tab: any) => {
  stores.value = tab;
};
const projectList = ref<any>([]);
const getListProjectAll = async () => {
  try {
    const res = await listProjectAll();
    projectList.value = res;
  } catch (error) {
    console.log(error);
  }
};
const tunnelList = ref<any>([]);
const getListTunnelAll = async () => {
  try {
    tunnelList.value = [];
    const res = await listTunnelAll({
      projectId: queryParams.value?.projectId || "",
    });
    tunnelList.value = res;
  } catch (error) {
    console.log(error);
  }
};
const refOwComponent = ref<any>(null);
const handleQuery = () => {
  console.log("queryParams.value", refOwComponent?.value?.getList);
    refOwComponent?.value?.getList(queryParams.value);
};
/** 重置按钮操作 */
const ruleFormRef = ref<any>(null);
const resetForm = (formEl: any | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  // refComponent?.value?.getList({});
  handleQuery();
};
onMounted(() => {
  getListProjectAll();
  getListTunnelAll();
});
</script>
<style lang="scss" scoped>
.other-works {
  .ce-tabs {
    // padding: 16px;
    display: flex;
    align-items: center;
    .ce-tabs-title {
      height: 32px;
      line-height: 32px;
      font-size: 14px;
      padding: 0 12px;
      margin-right: 12px;
      border-radius: 3px;
      color: var(--el-text-color-regular);
      font-weight: 600;
    }
    .ce-tabs-but {
      border: 1px solid #dcdfe6;
      cursor: pointer;
      font-weight: 500;
      &:hover {
        color: var(--el-color-primary);
        border-color: var(--el-color-primary);
      }
    }
    .ce-tabs-but_active {
      background: var(--el-color-primary);
      color: #fff;
      &:hover {
        color: #fff;
      }
    }
  }
}
</style>