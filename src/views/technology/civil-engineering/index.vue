<template>
  <div class="app-container civil-engineering">
    <el-form
      :model="queryParams"
      label-width="auto"
      ref="ruleFormRef"
      :inline="true"
    >
      <el-form-item label="项目编码" prop="projectCode">
        <el-input
          v-model="queryParams.projectCode"
          placeholder="请输入项目编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-select
          v-model="queryParams.projectName"
          placeholder="请选择"
          clearable
          filterable
          reserve-keyword
        >
          <el-option
            v-for="item in projects"
            :key="item.projectCode"
            :label="item.projectName"
            :value="item.projectName"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="隧道编码" prop="tunnelCode">
        <el-input
          v-model="queryParams.tunnelCode"
          placeholder="请输入隧道编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="隧道名称" prop="tunnelName">
        <el-select
          v-model="queryParams.tunnelName"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="item in tunnels"
            :key="item.tunnelCode"
            :label="item.tunnelName"
            :value="item.tunnelName"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="桩号" prop="pileNumber">
        <el-input
          v-model="queryParams.pileNumber"
          placeholder="请输入桩号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item
        label="病害位置"
        v-show="stores.label === '衬砌'"
        prop="diseaseLocation"
      >
        <el-select
          v-model="queryParams.diseaseLocation"
          placeholder="请选择"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="item in diseaseLocations"
            :key="item.diseaseLocation"
            :label="item.diseaseLocation"
            :value="item.diseaseLocation"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="病害类型"
        v-show="stores.label === '衬砌'"
        prop="diseaseType"
      >
        <el-cascader
          v-model="queryParams.diseaseType"
          placeholder="请选择"
          clearable
          :options="diseaseTypes"
          :show-all-levels="false"
          style="width: 240px"
          :props="{ label: 'label', value: 'label', children: 'children' }"
          @change="changeDiseaseData"
        >
        </el-cascader>
      </el-form-item>
      <el-form-item
        label="路面类型"
        v-show="stores.label === '路面'"
        prop="roadType"
      >
        <el-select
          v-model="queryParams.roadType"
          placeholder="请选择"
          clearable
          @change="diseaseTypeChange"
        >
          <el-option
            v-for="item in roadTypes"
            :key="item.roadType"
            :label="item.roadType"
            :value="item.roadType"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="缺损位置"
        v-show="stores.label === '路面'"
        prop="diseaseLocation"
      >
        <el-input
          v-model="queryParams.diseaseLocation"
          placeholder="请输入缺损位置"
        />
      </el-form-item>
      <el-form-item
        label="病害类型"
        v-show="stores.label === '路面'"
        prop="diseaseType"
      >
        <el-cascader
          v-model="queryParams.diseaseType"
          placeholder="请选择"
          clearable
          :options="formDiseaseTypes"
          :show-all-levels="false"
          :props="{ label: 'label', value: 'label', children: 'children' }"
          @change="changeDiseaseData"
        >
        </el-cascader>
      </el-form-item>
      <el-form-item label="检测时间范围" prop="onDate">
        <el-date-picker
          style="width: 240px"
          v-model="queryParams.onDate"
          type="daterange"
          range-separator="~"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" size="mini" @click="handleQuery"
          >搜索</el-button
        >
        <el-button size="mini" @click="resetForm(ruleFormRef)">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="ce-tabs">
      <span class="ce-tabs-title">土建结构:</span>
      <span
        :class="[
          'ce-tabs-title',
          'ce-tabs-but',
          item.template === stores.template && 'ce-tabs-but_active',
        ]"
        v-for="item in tabsConfig"
        :key="item.label"
        @click="tabsChange(item)"
        >{{ item.label }}</span
      >
    </div>
    <div class="">
      <keep-alive>
        <component
          ref="refComponent"
          :is="layouts[stores.template]"
        ></component>
      </keep-alive>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, defineAsyncComponent, onMounted } from "vue";
import { getProjects, getTunnels } from "@/api/system/opening";
import {
  tabsConfig,
  diseaseLocations,
  diseaseTypes,
  roadTypes,
  diseaseTypesForLiQing,
  diseaseTypesForShuiNi,
} from "./constants";
// 引入组件
const layouts: any = {
  // 洞口================
  opening: defineAsyncComponent(() => import("./components/opening.vue")),
  // 洞门================
  portal: defineAsyncComponent(() => import("./components/portal.vue")),
  // 衬砌================
  lining: defineAsyncComponent(() => import("./components/lining.vue")),
  // 路面================
  road: defineAsyncComponent(() => import("./components/road.vue")),
  // 检修道 ================
  manhole: defineAsyncComponent(() => import("./components/manhole.vue")),
  // 排水系统 ================
  drainage: defineAsyncComponent(() => import("./components/drainage.vue")),
  // 吊顶预埋件 ================
  ceiling: defineAsyncComponent(() => import("./components/ceiling.vue")),
  // 内装饰 ================
  interior: defineAsyncComponent(() => import("./components/interior.vue")),
  // 标线轮廓标outline ================
  outline: defineAsyncComponent(() => import("./components/outline.vue")),
};
const queryParams = ref<any>({
  projectCode: "",
  projectName: "",
  tunnelCode: "",
  tunnelName: "",
  pileNumber: "",
  onDate: [],
  diseaseLocation: "",
  diseaseType: "",
  roadType: "",
});
const projects = ref<any[]>([]);
const tunnels = ref<any[]>([]);
const diseaseTypeArr = ref<any[]>([]);
const formDiseaseTypes = ref<any[]>([]);
const stores = ref<any>({
  label: "洞口",
  template: "opening",
});
const tabsChange = (tab: any) => {
  stores.value = tab;
};
const changeDiseaseData = (val) => {
  queryParams.value.diseaseType = val[val.length - 1];
};
const diseaseTypeChange = () => {
  if (queryParams.value.roadType == "沥青路面") {
    diseaseTypeArr.value = diseaseTypesForLiQing;
  } else if (queryParams.value.roadType == "水泥路面") {
    diseaseTypeArr.value = diseaseTypesForShuiNi;
  } else {
    diseaseTypeArr.value = [];
  }
  queryParams.value.diseaseType = null;
};
const refComponent = ref<any>(null);
const handleQuery = () => {
  const query = {
    ...queryParams.value,
    createBeginTime: queryParams.value?.onDate?.[0] || "",
    createEndTime: queryParams.value?.onDate?.[1] || "",
  };
  refComponent?.value?.getList(query);
};
/** 重置按钮操作 */
const ruleFormRef = ref<any>(null);
const resetForm = (formEl: any | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  // refComponent?.value?.getList({});
  handleQuery()
};
const getProject = () => {
  getProjects().then((response) => {
    projects.value = response.data;
  });
};
const getTunnel = () => {
  getTunnels().then((response) => {
    tunnels.value = response.data;
    tunnels.value.forEach(function (item, index, array) {
      item.tunnelNameAndHole = item.tunnelName + "-" + item.hole;
    });
  });
};
onMounted(() => {
  getProject();
  getTunnel();
});
</script>
<style lang="scss" scoped>
.civil-engineering {
  .ce-tabs {
    // padding: 16px;
    display: flex;
    align-items: center;
    .ce-tabs-title {
      height: 32px;
      line-height: 32px;
      font-size: 14px;
      padding: 0 12px;
      margin-right: 12px;
      border-radius: 3px;
      color: var(--el-text-color-regular);
      font-weight: 600;
    }
    .ce-tabs-but {
      border: 1px solid #dcdfe6;
      cursor: pointer;
      font-weight: 500;
      &:hover {
        color: var(--el-color-primary);
        border-color: var(--el-color-primary);
      }
    }
    .ce-tabs-but_active {
      background: var(--el-color-primary);
      color: #fff;
      &:hover {
        color: #fff;
      }
    }
  }
}
</style>