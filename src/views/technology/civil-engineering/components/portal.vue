<template>
  <div class="app-container">
    <!-- 操作按钮区域 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            @click="handleAdd"
            v-hasPermi="['tunnel:portal:add']">
          新增
        </el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['tunnel:portal:edit']">
          修改
        </el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['tunnel:portal:remove']">
          删除
        </el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            @click="handleBatchAdd"
            v-hasPermi="['tunnel:portal:batchAdd']">
          批量新增
        </el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            @click="handleExport"
            v-hasPermi="['tunnel:portal:export']"
            v-if="isExport">
          导出
        </el-button>
        <el-button type="primary" v-if="isExportIng" :loading="true">
          导出中
        </el-button>
      </el-col>

      <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table
        v-loading="loading"
        :data="portalList"
        @selection-change="handleSelectionChange">
      <el-table-column type="selection" align="center"/>
      <el-table-column type="index" fixed="left" label="序号" width="60"/>
      <el-table-column
          v-for="(item, index) in tableConfig"
          :key="index"
          :prop="item.prop"
          :show-tooltip-when-overflow="true"
          :label="item.label"
          :min-width="item?.width"
          align="center">
        <!-- 病害图片列 -->
        <template v-slot:default="scope" v-if="item.prop === 'defectImages'">
          <div v-if="scope.row.defectImages" class="img-list">
            <el-image
                v-for="(img, imgIndex) in scope.row.defectImages.split(',')"
                :key="imgIndex"
                :src="img"
                :preview-src-list="scope.row.defectImages.split(',')"
                style="height: 50px; width: 50px;"
                fit="cover"
                preview-teleported/>
          </div>
          <span v-else>暂无图片</span>
        </template>
      </el-table-column>
      <!-- 操作列 -->
      <el-table-column
          fixed="right"
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          width="200">
        <template v-slot:default="scope">
          <el-button
              type="success"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['tunnel:portal:edit']">
            修改
          </el-button>
          <el-button
              type="danger"
              @click="handleDelete(scope.row)"
              v-hasPermi="['tunnel:portal:remove']">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="queryParams.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="queryParams.pageSize"
        :total="total"
        layout="->,total, sizes, prev, pager, next, jumper"
        style="margin-top: 10px; text-align: center">
    </el-pagination>

    <!-- 添加或修改隧道检查-洞门信息对话框 -->
    <el-dialog :title="title" v-model="open" align-center width="600px">
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名称" prop="projectCode">
              <el-select
                  v-model="form.projectCode"
                  placeholder="请选择"
                  clearable>
                <el-option
                    v-for="item in projects"
                    :key="item.projectCode"
                    :label="item.projectName"
                    :value="item.projectCode">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="隧道名称" prop="tunnelCode">
              <el-select v-model="form.tunnelCode" placeholder="请选择" clearable>
                <el-option
                    v-for="item in tunnels"
                    :key="item.tunnelCode"
                    :label="item.tunnelNameAndHole"
                    :value="item.tunnelCode">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="病害桩号" prop="pileNumber">
              <el-input
                  v-model="form.pileNumber"
                  placeholder="请输入病害桩号"
                  class="myInput"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="病害编号" prop="defectNumber">
              <el-input
                  v-model="form.defectNumber"
                  placeholder="请输入病害编号"
                  class="myInput"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="病害位置" prop="defectLocation">
              <el-input
                  v-model="form.defectLocation"
                  placeholder="请输入病害位置"
                  class="myInput"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="病害类型" prop="diseaseType">
              <el-input
                  v-model="form.diseaseType"
                  placeholder="请输入病害类型"
                  class="myInput"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="长度(m)" prop="lengthValue">
              <el-input
                  v-model="form.lengthValue"
                  placeholder="请输入长度"
                  class="myInput"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="宽度(m)" prop="widthValue">
              <el-input
                  v-model="form.widthValue"
                  placeholder="请输入宽度"
                  class="myInput"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="高度(m)" prop="heightValue">
              <el-input
                  v-model="form.heightValue"
                  placeholder="请输入高度"
                  class="myInput"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="面积(m²)" prop="areaValue">
              <el-input
                  v-model="form.areaValue"
                  placeholder="请输入面积"
                  class="myInput"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="判定描述" prop="judgmentDescription">
              <el-input
                  v-model="form.judgmentDescription"
                  placeholder="请输入判定描述"
                  type="textarea"
                  class="myInput"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状况值" prop="conditionValue">
              <el-input
                  v-model="form.conditionValue"
                  placeholder="请输入状况值"
                  class="myInput"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="重点病害" prop="isKeyDefect">
              <el-radio-group v-model="form.isKeyDefect">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="重点病害编号" prop="keyDefectNumber">
              <el-input
                  v-model="form.keyDefectNumber"
                  placeholder="请输入重点病害编号"
                  class="myInput"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="维修状态" prop="maintenanceStatus">
              <el-select v-model="form.maintenanceStatus" placeholder="请选择维修状态" clearable>
                <el-option label="未维修" value="未维修"></el-option>
                <el-option label="维修中" value="维修中"></el-option>
                <el-option label="已维修" value="已维修"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维修时间" prop="maintenanceTime">
              <el-date-picker
                  v-model="form.maintenanceTime"
                  type="datetime"
                  placeholder="请选择维修时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 病害图片上传 -->
        <el-form-item label="病害图片" prop="defectImages">
          <el-upload
              v-model="form.defectImages"
              class="upload-demo"
              action="/dev-api/tunnel/upload/uploadPicture"
              :on-remove="handleDefectImagesRemove"
              :on-success="defectImagesUpload"
              :show-file-list="true"
              :headers="{ Authorization: 'Bearer ' + token }"
              list-type="picture-card"
              :file-list="defectImagesLists"
              :limit="5"
              :on-exceed="handleExceed">
            <el-button type="primary">上传病害图片</el-button>
          </el-upload>
        </el-form-item>
      </el-form>

      <!-- 对话框底部按钮 -->
      <div slot="footer" style="text-align: right;">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 批量上传对话框 -->
    <el-dialog
        v-model="batchAddOpen"
        :title="batchAddTitle"
        width="40%"
        :destroy-on-close="true"
        @closed="closeFileUpload()">
      <portalFileUpload></portalFileUpload>
    </el-dialog>
  </div>
</template>

<script>
import {
  listPortal,
  getPortal,
  delPortal,
  addPortal,
  updatePortal,
  exportData,
  getProjects,
  getTunnels,
} from "@/api/system/portal";
// 组件导入
import portalFileUpload from "../fileUpload/portalFileUpload.vue";
// 常量和工具导入
import {tableConfigFilter} from "../constants";
import {getToken} from "@/utils/auth";

export default {
  name: "Portal",
  components: {
    portalFileUpload
  },
  data() {
    return {
      // 表格配置
      tableConfig: tableConfigFilter('portal'),
      // 页面状态
      loading: true,
      selectLoading: true,
      showSearch: true,
      // 选择状态
      ids: [],
      single: true,
      multiple: true,
      // 数据相关
      total: 0,
      portalList: [],
      // 对话框状态
      title: "",
      open: false,
      batchAddTitle: "",
      batchAddOpen: false,
      // 其他状态
      token: null,
      isExport: true,
      isExportIng: false,
      fileLists: [],
      defectImagesLists: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectTunnelInspectionId: null,
        tunnelCode: null,
        tunnelName: null,
        pileNumber: null,
        defectNumber: null,
        defectLocation: null,
        widthValue: null,
        heightValue: null,
        areaValue: null,
        lengthValue: null,
        defectImages: null,
        isKeyDefect: null,
        keyDefectNumber: null,
        maintenanceStatus: null,
        maintenanceTime: null,
        diseaseType: null,
        conditionDiscreption: null,
        conditionValue: null,
        onDate: [],
        offDate: [],
      },
      // 下拉选项数据
      projects: [],
      tunnels: [],
      // 表单数据
      form: {
        date1: [],
      },
      // 表单验证规则
      rules: {
        tunnelCode: [
          {required: true, message: "隧道名称不能为空", trigger: "blur"},
        ],
        projectCode: [
          {required: true, message: "项目名称不能为空", trigger: "blur"},
        ],
        pileNumber: [
          {required: true, message: "病害桩号不能为空", trigger: "blur"},
        ],
        defectNumber: [
          {required: true, message: "病害编号不能为空", trigger: "blur"},
        ],
        defectLocation: [
          {required: true, message: "病害位置不能为空", trigger: "blur"},
        ],
        widthValue: [
          {pattern: /^\d+(\.\d{1,2})?$/, message: "请输入正确的数值格式", trigger: "blur"},
        ],
        heightValue: [
          {pattern: /^\d+(\.\d{1,2})?$/, message: "请输入正确的数值格式", trigger: "blur"},
        ],
        areaValue: [
          {pattern: /^\d+(\.\d{1,2})?$/, message: "请输入正确的数值格式", trigger: "blur"},
        ],
        lengthValue: [
          {pattern: /^\d+(\.\d{1,2})?$/, message: "请输入正确的数值格式", trigger: "blur"},
        ],
        conditionValue: [
          {pattern: /^[0-4]?$/, message: "只能输0-4的整数", trigger: "blur"},
          {required: true, message: "状况值不能为空", trigger: "blur"},
        ],
      },
    };
  },

  created() {
    this.getList();
    this.getProject();
    this.getTunnel();
    this.token = getToken();
  },

  methods: {
    // ==================== 数据获取方法 ====================

    /**
     * 查询隧道检查-洞门信息列表
     * @param {Object} query - 查询参数
     */
    getList(query = {}) {
      this.loading = true;
      this.reset();
      this.queryParams = {...this.queryParams, ...query};
      if (
          this.queryParams.onDate != null &&
          this.queryParams.onDate.length !== 0
      ) {
        this.queryParams.createBeginTime = this.queryParams.onDate[0];
        this.queryParams.createEndTime = this.queryParams.onDate[1];
      }
      listPortal(this.queryParams).then((response) => {
        this.portalList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // ==================== 表单操作方法 ====================

    /**
     * 取消按钮
     */
    cancel() {
      this.open = false;
      this.reset();
    },

    /**
     * 关闭文件上传对话框
     */
    closeFileUpload() {
      this.handleQuery();
    },

    /**
     * 表单重置
     */
    reset() {
      this.form = {
        id: null,
        projectTunnelInspectionId: null,
        tunnelCode: null,
        tunnelName: null,
        pileNumber: null,
        defectNumber: null,
        defectLocation: null,
        widthValue: null,
        heightValue: null,
        areaValue: null,
        lengthValue: null,
        defectImages: null,
        isKeyDefect: null,
        keyDefectNumber: null,
        maintenanceStatus: null,
        maintenanceTime: null,
        inspectioContent: null,
        judgmentDescription: null,
        conditionValue: null,
        date1: [],
      };
      this.resetForm("form");
      this.defectImagesLists = [];
    },
    // ==================== 搜索操作方法 ====================

    /**
     * 搜索按钮操作
     */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /**
     * 重置按钮操作
     */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.createEndTime = null;
      this.queryParams.createBeginTime = null;
      this.handleQuery();
    },
    // ==================== 表格操作方法 ====================

    /**
     * 多选框选中数据
     * @param {Array} selection - 选中的数据
     */
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    // ==================== 增删改操作方法 ====================

    /**
     * 新增按钮操作
     */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加隧道检查-洞门信息";
    },

    /**
     * 批量新增按钮操作
     */
    handleBatchAdd() {
      this.batchAddOpen = true;
      this.batchAddTitle = "批量新增";
    },

    /**
     * 修改按钮操作
     * @param {Object} row - 行数据
     */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getPortal(id).then((response) => {
        this.form = response.data;
        this.form.date1 = [response.data.beginTime, response.data.endTime];

        // 处理病害图片
        if (response.data.defectImages != null && response.data.defectImages !== '') {
          let defectImagesList = response.data.defectImages.split(',');
          this.defectImagesLists = defectImagesList.map((item) => {
            return {
              name: item,
              url: item,
            };
          });
        }
        this.open = true;
        this.title = "修改隧道检查-洞门信息";
      });
    },
    /**
     * 提交按钮
     */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.projects.forEach((item, index, arr) => {
            if (item.projectCode === this.form.projectCode) {
              this.form.projectName = item.projectName;
            }
          });
          this.tunnels.forEach((item, index, arr) => {
            if (item.tunnelCode === this.form.tunnelCode) {
              this.form.tunnelName = item.tunnelName;
            }
          });
          if (this.form.id != null) {
            updatePortal(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPortal(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    /**
     * 删除按钮操作
     * @param {Object} row - 行数据
     */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
          .confirm('是否确认删除隧道检查-洞门信息编号为"' + ids + '"的数据项？')
          .then(function () {
            return delPortal(ids);
          })
          .then(() => {
            this.getList();
            this.$modal.msgSuccess("删除成功");
          })
          .catch(() => {
          });
    },
    // ==================== 导出操作方法 ====================

    /**
     * 导出按钮操作
     */
    handleExport() {
      if (this.portalList.length === 0) {
        this.$alert("无导出数据", "", {
          confirmButtonText: "确定",
        });
      } else {
        //开启loading
        this.isExport = false;
        this.isExportIng = true;
        if (
            this.queryParams.onDate != null &&
            this.queryParams.onDate.length !== 0
        ) {
          this.queryParams.createBeginTime = this.queryParams.onDate[0];
          this.queryParams.createEndTime = this.queryParams.onDate[1];
        }
        exportData(this.queryParams)
            .then((res) => {
              if (res.code == "500") {
                this.$modal.msgSuccess("导出失败，请联系开发人员");
                return;
              } else {
                this.$modal.msgSuccess("导出成功");
              }
              let blob = new Blob([res], {type: "application/force-download"});
              console.log(blob);
              let fileReader = new FileReader();
              fileReader.readAsDataURL(blob);
              fileReader.onload = (e) => {
                let a = document.createElement("a");
                let date = new Date();
                let year = date.getFullYear();
                let month = date.getMonth() + 1;
                let day = date.getDate();
                a.download = `导出数据${year + "_" + month + "_" + day}.xlsx`;
                a.href = e.target.result;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
              };
              this.isExport = true;
              this.isExportIng = false;
            })
            .catch(() => {
              this.isExport = true;
              this.isExportIng = false;
            });
      }
    },
    // ==================== 分页操作方法 ====================

    /**
     * 每页显示条数改变
     * @param {Number} val - 每页显示条数
     */
    sizeChangeHandle(val) {
      this.queryParams.pageSize = val;
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /**
     * 当前页改变
     * @param {Number} val - 当前页
     */
    currentChangeHandle: function currentChangeHandle(val) {
      this.queryParams.pageNum = val;
      this.getList();
    },

    // ==================== 下拉选项数据获取方法 ====================

    /**
     * 获取项目列表
     */
    getProject() {
      getProjects().then((response) => {
        this.projects = response.data;
      });
    },

    /**
     * 获取隧道列表
     */
    getTunnel() {
      getTunnels().then((response) => {
        this.tunnels = response.data;
        this.tunnels.forEach(function (item, index, array) {
          item.tunnelNameAndHole = item.tunnelName + "-" + item.hole;
        });
      });
    },

    // ==================== 文件上传操作方法 ====================

    /**
     * 上传病害图片成功回调
     * @param {Object} response - 响应数据
     * @param {Object} file - 文件对象
     * @param {Array} fileList - 文件列表
     */
    defectImagesUpload: function (response, file, fileList) {
      let defectImagesList = fileList.map(item => {
        if (item.response) {
          return item.response.msg;
        } else {
          return item.url;
        }
      });
      this.defectImagesLists = fileList.map((item) => {
        let url = item.response ? item.response.msg : item.url;
        return {
          name: url,
          url: url,
        };
      });
      this.form.defectImages = defectImagesList.join(',');
    },

    /**
     * 删除病害图片
     * @param {Object} file - 文件对象
     * @param {Array} fileList - 文件列表
     */
    handleDefectImagesRemove(file, fileList) {
      let defectImagesList = fileList.map(item => {
        if (item.response) {
          return item.response.msg;
        } else {
          return item.url;
        }
      });
      this.defectImagesLists = fileList.map((item) => {
        let url = item.response ? item.response.msg : item.url;
        return {
          name: url,
          url: url,
        };
      });
      this.form.defectImages = defectImagesList.join(',');
    },

    /**
     * 文件超出限制处理
     */
    handleExceed() {
      this.$modal.msgWarning('最多只能上传5张图片');
    },

    /**
     * 删除文件
     * @param {Object} file - 文件对象
     * @param {Array} fileList - 文件列表
     */
    handleRemove(file, fileList) {
      this.fileLists = fileList;
    },

    /**
     * 预览文件
     * @param {Object} file - 文件对象
     */
    handlePreview(file) {
      console.log(file);
    },

  },
};
</script>


<style lang="scss" scoped>
/* 批量操作容器样式 */
.batch-container {
  .download {
    color: #409eff;
    cursor: pointer;
  }

  .desc-section {
    display: flex;
    flex-direction: column;
  }

  .img-list {
    display: flex;

    .img-item {
      width: 50px;
      height: 50px;
      margin-left: 5px;

      &:first-child {
        margin-left: 0;
      }
    }
  }
}

/* 主容器样式 */
.app-container {
  height: 200%;
  padding: 10px 20px;
  width: 100%;

  .search-section {
    padding: 20px 30px;
  }

  .page-section {
    padding-top: 20px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}

/* 表单输入框样式 */
.myInput {
  width: 215px;
}

/* 必填项标识样式 */
.slot_form_label::after {
  content: "*";
  width: 100%;
  display: inline-block;
  position: absolute;
  left: -10px;
}
</style>