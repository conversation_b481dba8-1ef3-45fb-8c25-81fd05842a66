<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            @click="handleAdd"
            v-hasPermi="['tunnel:road:add']"
        >新增
        </el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['tunnel:road:edit']"
        >修改
        </el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['tunnel:road:remove']"
        >删除
        </el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            @click="handleBatchAdd"
            v-hasPermi="['tunnel:road:batchAdd']"
        >批量新增
        </el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            @click="handleExport"
            v-hasPermi="['tunnel:road:export']"
            v-if="isExport"
        >导出
        </el-button
        >
        <el-button type="primary" v-if="isExportIng" :loading="true"
        >导出中
        </el-button
        >
      </el-col>
      <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
        v-loading="loading"
        :data="roadList"
        @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column type="index" label="序号" width="60"/>
      <el-table-column
          v-for="(item, index) in tableConfig"
          :key="index"
          :prop="item.prop"
          :show-tooltip-when-overflow="true"
          :label="item.label"
          :min-width="item?.width"
          align="center"
      >
        <template v-slot:default="scope" v-if="item.prop === 'defectImages'">
          <div v-if="scope.row.defectImages" class="img-list">
            <el-image
                v-for="(img, imgIndex) in scope.row.defectImages.split(',')"
                :key="imgIndex"
                :src="img"
                :preview-src-list="scope.row.defectImages.split(',')"
                style="height: 50px; width: 50px;"
                fit="cover"
                preview-teleported
            />
          </div>
          <span v-else>暂无图片</span>
        </template>
      </el-table-column>
      <el-table-column
          fixed="right"
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          width="200"
      >
        <template v-slot:default="scope">
          <el-button
              type="success"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['tunnel:road:edit']"
          >修改
          </el-button
          >
          <el-button
              type="danger"
              @click="handleDelete(scope.row)"
              v-hasPermi="['tunnel:road:remove']"
          >删除
          </el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="queryParams.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="queryParams.pageSize"
        :total="total"
        layout="->,total, sizes, prev, pager, next, jumper"
        style="margin-top: 10px; text-align: center"
    >
    </el-pagination>

    <!-- 添加或修改隧道检查-路面对话框 -->
    <el-dialog :title="title" v-model="open" width="600px">
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-row>
          <el-col :span="12">
            <el-form-item label="项目名称" prop="projectCode">
              <el-select v-model="form.projectCode" placeholder="请选择" clearable>
                <el-option
                    v-for="item in projects"
                    :key="item.projectCode"
                    :label="item.projectName"
                    :value="item.projectCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="隧道名称" prop="tunnelCode">
              <el-select v-model="form.tunnelCode" placeholder="请选择" clearable>
                <el-option
                    v-for="item in tunnels"
                    :key="item.tunnelCode"
                    :label="item.tunnelNameAndHole"
                    :value="item.tunnelCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="检测时间" prop="inspectionTime">
              <el-date-picker
                  clearable
                  v-model="form.inspectionTime"
                  type="datetime"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择检测时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="病害桩号" prop="pileNumber">
              <el-input
                  v-model="form.pileNumber"
                  placeholder="请输入病害桩号"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="病害编号" prop="defectNumber">
              <el-input
                  v-model="form.defectNumber"
                  placeholder="请输入病害编号"
                  class="myInput"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="病害位置" prop="defectLocation">
              <el-input
                  v-model="form.defectLocation"
                  placeholder="请输入病害位置"
                  class="myInput"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="路面类型" prop="pavementType">
              <el-select
                  v-model="form.pavementType"
                  placeholder="请选择"
                  clearable
                  @change="formDiseaseTypeChange"
              >
                <el-option
                    v-for="item in roadTypes"
                    :key="item.roadType"
                    :label="item.roadType"
                    :value="item.roadType"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="病害类型" prop="diseaseType">
              <el-cascader
                  v-model="form.diseaseType"
                  placeholder="请选择"
                  clearable
                  :options="formDiseaseTypes"
                  :show-all-levels="false"
                  :props="{ label: 'label', value: 'label', children: 'children' }"
                  @change="changeDiseaseData"
              >
              </el-cascader>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="长度(m)" prop="lengthValue">
              <el-input
                  v-model="form.lengthValue"
                  placeholder="请输入长度"
                  class="myInput"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="宽度(m)" prop="widthValue">
              <el-input
                  v-model="form.widthValue"
                  placeholder="请输入宽度"
                  class="myInput"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="面积(m²)" prop="areaValue">
              <el-input
                  v-model="form.areaValue"
                  placeholder="请输入面积"
                  class="myInput"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检测时间" prop="inspectionTime">
              <el-date-picker
                  v-model="form.inspectionTime"
                  type="date"
                  placeholder="请选择检测时间"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状况值" prop="conditionValue">
              <el-input
                  v-model="form.conditionValue"
                  placeholder="请输入状况值"
                  class="myInput"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状况描述" prop="conditionDiscreption">
              <el-input
                  v-model="form.conditionDiscreption"
                  placeholder="请输入状况描述"
                  type="textarea"
                  class="myInput"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="重点病害" prop="isKeyDefect">
              <el-radio-group v-model="form.isKeyDefect">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="重点病害编号" prop="keyDefectNumber">
              <el-input
                  v-model="form.keyDefectNumber"
                  placeholder="请输入重点病害编号"
                  class="myInput"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="维修状态" prop="maintenanceStatus">
              <el-select v-model="form.maintenanceStatus" placeholder="请选择维修状态" clearable>
                <el-option label="未维修" value="未维修"></el-option>
                <el-option label="维修中" value="维修中"></el-option>
                <el-option label="已维修" value="已维修"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维修时间" prop="maintenanceTime">
              <el-date-picker
                  v-model="form.maintenanceTime"
                  type="datetime"
                  placeholder="请选择维修时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状况描述" prop="conditionDiscreption">
              <el-input
                  v-model="form.conditionDiscreption"
                  placeholder="请输入状况描述"
                  type="textarea"
                  :rows="3"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="病害图片" prop="defectImages">
           <el-upload
               v-model="form.defectImages"
               class="upload-demo"
               action="/dev-api/tunnel/upload/uploadPicture"
               :on-remove="handleDefectImagesRemove"
               :on-success="defectImagesUpload"
               :show-file-list="true"
               :headers="{ Authorization: 'Bearer ' + token }"
               list-type="picture-card"
               :file-list="defectImagesLists"
               :limit="5"
               :on-exceed="handleExceed">
             <el-button type="primary">上传病害图片</el-button>
           </el-upload>
         </el-form-item>
      </el-form>
      <div slot="footer" style="text-align: right;">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 批量上传 -->
    <el-dialog
        v-model="batchAddOpen"
        :title="batchAddTitle"
        width="40%"
        v-if="batchAddOpen"
        :destroy-on-close="true"
        @closed="closeFileUpload()"
    >
      <roadFileUpload></roadFileUpload>
    </el-dialog>
  </div>
</template>

<script>
import {
  listRoad,
  getRoad,
  delRoad,
  addRoad,
  updateRoad,
  exportData,
  getProjects,
  getTunnels,
} from "@/api/system/road";
import roadFileUpload from "../fileUpload/roadFileUpload.vue";
import axios from "axios";
import {getToken} from "@/utils/auth";
import {tableConfigFilter} from "../constants";

export default {
  name: "Road",
  components: {roadFileUpload},
  data() {
    return {
      tableConfig: tableConfigFilter('road'),
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 隧道检查-路面表格数据
      roadList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      batchAddTitle: "",
      batchAddOpen: false,
      token: null,
      isExport: true,
      isExportIng: false,
      fileLists: [],
      defectImagesLists: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectTunnelInspectionId: null,
        projectCode: null,
        projectName: null,
        tunnelCode: null,
        tunnelName: null,
        createBeginTime: null,
        createEndTime: null,
        inspectionTime: null,
        pileNumber: null,
        pavementType: null,
        diseaseType: null,
        conditionValue: null,
        defectNumber: null,
        defectLocation: null,
        lengthValue: null,
        widthValue: null,
        areaValue: null,
        defectImages: null,
        isKeyDefect: null,
        keyDefectNumber: null,
        maintenanceStatus: null,
        maintenanceTime: null,
        conditionDiscreption: null,
        onDate: [],
        offDate: [],
        diseaseTypeChooseQuery: [],
      },
      projects: [],
      diseaseTypes: [
        // {diseaseType:'横向裂缝'},
        // {diseaseType:'纵向裂缝'},
        // {diseaseType:'龟裂'},
        // {diseaseType:'块裂'},
        // {diseaseType:'车辙'},
        // {diseaseType:'沉陷'},
        // {diseaseType:'波浪拥包'},
        // {diseaseType:'拱起'},
        // {diseaseType:'坑槽'},
        // {diseaseType:'松散'},
        // {diseaseType:'泛油'},
        // {diseaseType:'渗水积水'},
        // {diseaseType:'结冰'},
        // {diseaseType:'裂缝'},
        // {diseaseType:'露骨'},
        // {diseaseType:'坑洞'},
        // {diseaseType:'剥落'},
        // {diseaseType:'接缝错台'},
        // {diseaseType:'板角破损'},
        // {diseaseType:'破碎板'}
      ],
      diseaseTypesForLiQing: [
        {
          label: "裂缝",
          children: [
            {label: "横向裂缝"},
            {label: "纵向裂缝"},
            {label: "龟裂"},
            {label: "块裂"},
          ],
        },
        {
          label: "路面变形",
          children: [
            {label: "车辙"},
            {label: "沉陷"},
            {label: "波浪拥包"},
          ],
        },
        {label: "拱起", children: [{label: "拱起"}]},
        {
          label: "表面损坏",
          children: [{label: "坑槽"}, {label: "松散"}, {label: "泛油"}],
        },
        {
          label: "积水渗水涌水",
          children: [{label: "渗水积水"}, {label: "结冰"}],
        },
      ],
      diseaseTypesForShuiNi: [
        {label: "裂缝", children: [{label: "裂缝"}]},
        {
          label: "表面损坏",
          children: [{label: "露骨"}, {label: "坑洞"}, {label: "剥落"}],
        },
        {label: "错台断缝", children: [{label: "接缝错台"}]},
        {
          label: "缺损",
          children: [{label: "板角破损"}, {label: "破碎板"}],
        },
      ],
      formDiseaseTypes: [],
      diseaseLocations: [
        {diseaseLocation: "行车道"},
        {diseaseLocation: "超车道"},
        {diseaseLocation: "紧急停车带"},
      ],
      tunnels: [],
      roadTypes: [{roadType: "沥青路面"}, {roadType: "水泥路面"}],

      // 表单参数
      form: {
        date1: [],
        defectNumber: null,
        defectLocation: null,
        lengthValue: null,
        widthValue: null,
        areaValue: null,
        defectImages: null,
        isKeyDefect: 0,
        keyDefectNumber: null,
        maintenanceStatus: null,
        maintenanceTime: null,
        inspectionTime: null,
        conditionDiscreption: null
      },
      // 表单校验
      rules: {
        tunnelCode: [
          {required: true, message: "隧道名称不能为空", trigger: "blur"},
        ],
        projectCode: [
          {required: true, message: "项目名称不能为空", trigger: "blur"},
        ],
        pileNumber: [
          {required: true, message: "桩号不能为空", trigger: "blur"},
        ],
        conditionValue: [
          {pattern: /^[0-4]?$/, message: "只能输0-4的整数", trigger: "blur"},
          {required: true, message: "状况值不能为空", trigger: "blur"},
        ],
        lengthValue: [
          {pattern: /^\d+(\.\d{1,2})?$/, message: "请输入正确的数值格式，最多保留2位小数", trigger: "blur"},
        ],
        widthValue: [
          {pattern: /^\d+(\.\d{1,2})?$/, message: "请输入正确的数值格式，最多保留2位小数", trigger: "blur"},
        ],
        areaValue: [
          {pattern: /^\d+(\.\d{1,2})?$/, message: "请输入正确的数值格式，最多保留2位小数", trigger: "blur"},
        ],
        layoutNum: [
          {
            required: true,
            pattern: /^[1-9]*[1-9][0-9]*$/,
            message: "只能输入大于1整数",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.getProject();
    this.getTunnel();
    this.token = getToken();
  },
  methods: {
    /** 查询隧道检查-路面列表 */
    getList(query = {}) {
      this.loading = true;
      this.reset();
      this.queryParams = {...this.queryParams, ...query};
      if (
          this.queryParams.onDate != null &&
          this.queryParams.onDate.length != 0
      ) {
        this.queryParams.createBeginTime = this.queryParams.onDate[0];
        this.queryParams.createEndTime = this.queryParams.onDate[1];
      }
      if (
          this.queryParams.diseaseTypeChooseQuery != null &&
          this.queryParams.diseaseTypeChooseQuery.length != 0
      ) {
        this.queryParams.diseaseType =
            this.queryParams.diseaseTypeChooseQuery[
            this.queryParams.diseaseTypeChooseQuery.length - 1
                ];
      }
      listRoad(this.queryParams).then((response) => {
        this.roadList = response.rows;
        this.roadList.forEach((item) => {
          Object.assign(item, {prePictureList: [item.pictureUrl]});
        });
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    closeFileUpload() {
      this.handleQuery();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectTunnelInspectionId: null,
        tunnelCode: null,
        tunnelName: null,
        inspectionTime: null,
        pileNumber: null,
        pavementType: null,
        diseaseLocation: null,
        diseaseType: null,
        diseaseNumber: null,
        lengthValue: null,
        widthValue: null,
        areaValue: null,
        defectImages: null,
        crackLength: null,
        crackWidth: null,
        diseasePicture: null,
        isKeyDefect: 0,
        keyDefectNumber: null,
        maintenanceStatus: null,
        maintenanceTime: null,
        conditionDiscreption: null,
        conditionValue: null,
        diseaseTypechoose: [],
      };
      this.resetForm("form");
      this.fileLists = [];
      this.defectImagesLists = [];
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    changeDiseaseQuery(val) {
      this.queryParams.diseaseType = val[val.length - 1];
    },
    changeDiseaseData(val) {
      this.form.diseaseType = val[val.length - 1];
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.createEndTime = null;
      this.queryParams.createBeginTime = null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      console.log("56456465");
      this.open = true;
      console.log(this.open);
      this.title = "添加隧道检查-路面";
    },
    /** 批量新增按钮操作 */
    handleBatchAdd() {
      this.batchAddOpen = true;
      this.batchAddTitle = "批量新增";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getRoad(id).then((response) => {
        this.form = response.data;
        if (this.form.defectImages) {
          this.defectImagesLists = this.form.defectImages.split(',').map((item) => {
            return {
              name: item,
              url: item,
            };
          });
        }
        this.open = true;
        this.title = "修改隧道检查-路面";
      });
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // 移除对date1、beginTime、endTime的处理
          if (
              this.form.diseaseTypechoose != null &&
              this.form.diseaseTypechoose.length != 0
          ) {
            this.form.diseaseType =
                this.form.diseaseTypechoose[
                this.form.diseaseTypechoose.length - 1
                    ];
          }
          this.projects.forEach((item, index, arr) => {
            if (item.projectCode == this.form.projectCode) {
              this.form.projectName = item.projectName;
            }
          });
          this.tunnels.forEach((item, index, arr) => {
            if (item.tunnelCode == this.form.tunnelCode) {
              this.form.tunnelName = item.tunnelName;
            }
          });
          if (this.form.id != null) {
            updateRoad(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRoad(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
          .confirm('是否确认删除隧道检查-路面编号为"' + ids + '"的数据项？')
          .then(function () {
            return delRoad(ids);
          })
          .then(() => {
            this.getList();
            this.$modal.msgSuccess("删除成功");
          })
          .catch(() => {
          });
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.roadList.length == 0) {
        this.$alert("无导出数据", "", {
          confirmButtonText: "确定",
        });
      } else {
        //开启loading
        this.isExport = false;
        this.isExportIng = true;
        if (
            this.queryParams.onDate != null &&
            this.queryParams.onDate.length != 0
        ) {
          this.queryParams.createBeginTime = this.queryParams.onDate[0];
          this.queryParams.createEndTime = this.queryParams.onDate[1];
        }
        if (
            this.queryParams.diseaseTypeChooseQuery != null &&
            this.queryParams.diseaseTypeChooseQuery.length != 0
        ) {
          this.queryParams.diseaseType =
              this.queryParams.diseaseTypeChooseQuery[
              this.queryParams.diseaseTypeChooseQuery.length - 1
                  ];
        }
        exportData(this.queryParams)
            .then((res) => {
              if (res.code == "500") {
                this.$modal.msgSuccess("导出失败，请联系开发人员");
                return;
              } else {
                this.$modal.msgSuccess("导出成功");
              }
              let blob = new Blob([res], {type: "application/force-download"});
              console.log(blob);
              let fileReader = new FileReader();
              fileReader.readAsDataURL(blob);
              fileReader.onload = (e) => {
                let a = document.createElement("a");
                let date = new Date();
                let year = date.getFullYear();
                let month = date.getMonth() + 1;
                let day = date.getDate();
                a.download = `导出数据${year + "_" + month + "_" + day}.xlsx`;
                a.href = e.target.result;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
              };
              this.isExport = true;
              this.isExportIng = false;
            })
            .catch(() => {
              this.isExport = true;
              this.isExportIng = false;
            });
      }
    },
    sizeChangeHandle(val) {
      this.queryParams.pageSize = val;
      this.queryParams.pageNum = 1;
      this.getList();
    },
    currentChangeHandle: function currentChangeHandle(val) {
      this.queryParams.pageNum = val;
      this.getList();
    },
    handleExceed(files, fileList) {
      this.$modal.msgWarning(`只能上传一个文件`);
    },
    /** 病害图片上传成功处理 */
    defectImagesUpload: function (response, file, fileList) {
      let pictureList = this.form.defectImages ? this.form.defectImages.split(',') : [];
      pictureList.push(response.msg);
      this.form.defectImages = pictureList.join(',');
      this.defectImagesLists = pictureList.map((item) => {
        return {
          name: item,
          url: item,
        };
      });
    },
    /** 病害图片移除处理 */
    handleDefectImagesRemove(file, fileList) {
      let pictureList = this.form.defectImages ? this.form.defectImages.split(',') : [];
      pictureList = pictureList.filter(item => item !== file.url);
      this.form.defectImages = pictureList.length > 0 ? pictureList.join(',') : null;
      this.defectImagesLists = pictureList.map((item) => {
        return {
          name: item,
          url: item,
        };
      });
    },
    getProject() {
      getProjects().then((response) => {
        this.projects = response.data;
      });
    },
    getTunnel() {
      getTunnels().then((response) => {
        this.tunnels = response.data;
        this.tunnels.forEach(function (item, index, array) {
          item.tunnelNameAndHole = item.tunnelName + "-" + item.hole;
        });
      });
    },
    //上传图片
    handleRemove() {
      this.form.pictureUrl = null;
    },
    diseaseTypeChange() {
      if (this.queryParams.roadType == "沥青路面") {
        this.diseaseTypes = this.diseaseTypesForLiQing;
      } else if (this.queryParams.roadType == "水泥路面") {
        this.diseaseTypes = this.diseaseTypesForShuiNi;
      } else {
        this.diseaseTypes = [];
      }
      console.log("asljdaslkj");
      this.queryParams.diseaseType = null;
    },
    formDiseaseTypeChange() {
      if (this.form.pavementType == "沥青路面") {
        this.formDiseaseTypes = this.diseaseTypesForLiQing;
      } else if (this.form.pavementType == "水泥路面") {
        this.formDiseaseTypes = this.diseaseTypesForShuiNi;
      } else {
        this.formDiseaseTypes = [];
      }
      this.form.diseaseType = null;
    },
  },
};
</script>

<style lang="scss" scoped>
.batch-container {
  .download {
    color: #409eff;
    cursor: pointer;
  }

  .desc-section {
    display: flex;
    flex-direction: column;
  }

  .img-list {
    display: flex;

    .img-item {
      width: 50px;
      height: 50px;
      margin-left: 5px;

      &:first-child {
        margin-left: 0;
      }
    }
  }
}

.app-container {
  height: 200%;
  padding: 10px 20px;
  width: 100%;

  .search-section {
    padding: 20px 30px;
  }

  .page-section {
    padding-top: 20px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}

.myInput {
  width: 215px;
}
</style>
