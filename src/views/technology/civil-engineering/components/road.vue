<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          @click="handleAdd"
          v-hasPermi="['tunnel:road:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['tunnel:road:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['tunnel:road:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          @click="handleBatchAdd"
          v-hasPermi="['tunnel:road:batchAdd']"
          >批量新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          size="mini"
          @click="handleExport"
          v-hasPermi="['tunnel:road:export']"
          v-if="isExport"
          >导出</el-button
        >
        <el-button type="primary" v-if="isExportIng" :loading="true"
          >导出中</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="roadList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" fixed="left" label="序号" width="70" />
      <el-table-column
        v-for="(item, index) in tableConfig"
        :key="index"
        :prop="item.prop"
        show-tooltip-when-overflow="true"
        :label="item.label"
        :min-width="item?.width || 120"
      />
      <!-- <el-table-column label="项目编码" align="center" prop="projectCode" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="项目名称" align="center" prop="projectName" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="隧道编码" align="center" prop="tunnelCode" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="隧道名称" align="center" prop="tunnelNameAndHole" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="开始时间" align="center" prop="beginTime" width="150" show-tooltip-when-overflow="true">
      </el-table-column>
      <el-table-column label="截至时间" align="center" prop="endTime" width="150" show-tooltip-when-overflow="true">
      </el-table-column>
      <el-table-column label="桩号" align="center" prop="pileNumber" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="路面类型" align="center" prop="roadType" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="缺损位置" align="center" prop="diseaseLocation" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="病害类型" align="center" prop="diseaseType" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="裂缝长度" align="center" prop="crackLength" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="裂缝宽度" align="center" prop="crackWidth" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="面积" align="center" prop="area" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="图片" align="center" prop="pictureUrl"  >
        <template v-slot:default="scope">
          <el-image :src="scope.row.pictureUrl" :preview-src-list="scope.row.prePictureList" fit="cover"/>
        </template>
      </el-table-column>
      <el-table-column label="状况描述" align="center" prop="conditionDiscreption" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="状况值" align="center" prop="conditionValue" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="创建人编码" align="center" prop="creatorCode" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="创建人名称" align="center" prop="creatorName" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="创建时间" align="center" prop="creatorTime" width="180" show-tooltip-when-overflow="true">
      </el-table-column>
      <el-table-column label="修改人编码" align="center" prop="modifierCode" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="修改人名称" align="center" prop="modifierName" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="修改时间" align="center" prop="modifierTime" width="180" show-tooltip-when-overflow="true">
      </el-table-column> -->
      <el-table-column
        fixed="right"
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="200"
      >
        <template v-slot:default="scope">
          <el-button
            size="mini"
            type="success"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['tunnel:road:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(scope.row)"
            v-hasPermi="['tunnel:road:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="queryParams.pageSize"
      :total="total"
      layout="->,total, sizes, prev, pager, next, jumper"
      style="margin-top: 10px; text-align: center"
    >
    </el-pagination>

    <!-- 添加或修改隧道检查-路面对话框 -->
    <el-dialog :title="title" v-model="open" width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="项目名称" prop="projectCode">
          <el-select v-model="form.projectCode" placeholder="请选择" clearable>
            <el-option
              v-for="item in projects"
              :key="item.projectCode"
              :label="item.projectName"
              :value="item.projectCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="隧道名称" prop="tunnelCode">
          <el-select v-model="form.tunnelCode" placeholder="请选择" clearable>
            <el-option
              v-for="item in tunnels"
              :key="item.tunnelCode"
              :label="item.tunnelNameAndHole"
              :value="item.tunnelCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <!--        <el-form-item label="隧道编码" prop="tunnelCode">-->
        <!--          <el-input v-model="form.tunnelCode" placeholder="请输入隧道编码" class="input"/>-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="隧道名称" prop="tunnelName">-->
        <!--          <el-input v-model="form.tunnelName" placeholder="请输入隧道名称" />-->
        <!--        </el-form-item>-->
        <el-form-item label="开始~截至时间:" prop="date1">
          <el-date-picker
            v-model="form.date1"
            type="daterange"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            range-separator="~"
            start-placeholder="开始时间"
            end-placeholder="截至时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="桩号" prop="pileNumber">
          <el-input
            v-model="form.pileNumber"
            placeholder="请输入桩号"
            class="myInput"
          />
        </el-form-item>
        <el-form-item label="路面类型" prop="roadType">
          <el-select
            v-model="form.roadType"
            placeholder="请选择"
            clearable
            @change="formDiseaseTypeChange"
          >
            <el-option
              v-for="item in roadTypes"
              :key="item.roadType"
              :label="item.roadType"
              :value="item.roadType"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="缺损位置" prop="diseaseLocation">
          <el-input
            v-model="form.diseaseLocation"
            placeholder="请输入缺损位置"
            class="myInput"
          />
          <!--          <el-select v-model="form.diseaseLocation" placeholder="请选择" clearable>-->
          <!--            <el-option-->
          <!--                v-for="item in diseaseLocations"-->
          <!--                :key="item.diseaseLocation"-->
          <!--                :label="item.diseaseLocation"-->
          <!--                :value="item.diseaseLocation">-->
          <!--            </el-option>-->
          <!--          </el-select>-->
        </el-form-item>
        <el-form-item label="病害类型" prop="diseaseType">
          <el-cascader
            v-model="form.diseaseType"
            placeholder="请选择"
            clearable
            :options="formDiseaseTypes"
            :show-all-levels="false"
            :props="{ label: 'label', value: 'label', children: 'children' }"
            @change="changeDiseaseData"
          >
          </el-cascader>
        </el-form-item>

        <!--        <el-form-item label="病害类型" prop="diseaseType">-->
        <!--          <el-select v-model="form.diseaseType" placeholder="请选择" clearable>-->
        <!--            <el-option-->
        <!--                v-for="item in diseaseTypes"-->
        <!--                :key="item.diseaseType"-->
        <!--                :label="item.diseaseType"-->
        <!--                :value="item.diseaseType">-->
        <!--            </el-option>-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item label="裂缝长度" prop="crackLength">
          <el-input
            v-model="form.crackLength"
            placeholder="请输入裂缝长度"
            class="myInput"
          />
        </el-form-item>
        <el-form-item label="裂缝宽度" prop="crackWidth">
          <el-input
            v-model="form.crackWidth"
            placeholder="请输入裂缝宽度"
            class="myInput"
          />
        </el-form-item>
        <el-form-item label="面积" prop="area">
          <el-input
            v-model="form.area"
            placeholder="请输入面积"
            class="myInput"
          />
        </el-form-item>
        <el-form-item label="状况描述" prop="conditionDiscreption">
          <el-input
            v-model="form.conditionDiscreption"
            placeholder="请输入状况描述"
            type="textarea"
            class="myInput"
          />
        </el-form-item>
        <el-form-item label="状况值" prop="conditionValue">
          <el-input
            v-model="form.conditionValue"
            placeholder="请输入状况值"
            class="myInput"
          />
        </el-form-item>
        <el-form-item label="图片" prop="pictureUrl">
          <el-upload
            v-model="form.pictureUrl"
            class="upload-demo"
            action="/dev-api/tunnel/upload/uploadPicture"
            :on-remove="handleRemove"
            :on-success="fileUpload"
            :show-file-list="true"
            :headers="{ Authorization: 'Bearer ' + token }"
            list-type="picture-card"
            :file-list="fileLists"
            :limit="1"
            :on-exceed="handleExceed"
          >
            <el-button type="primary">上传图片</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 批量上传 -->
    <el-dialog
      v-model="batchAddOpen"
      :title="batchAddTitle"
      width="40%"
      v-if="batchAddOpen"
      destroy-on-close="true"
      @closed="closeFileUpload()"
    >
      <roadFileUpload></roadFileUpload>
    </el-dialog>
  </div>
</template>

<script>
import {
  listRoad,
  getRoad,
  delRoad,
  addRoad,
  updateRoad,
  exportData,
  getProjects,
  getTunnels,
} from "@/api/system/road";
import roadFileUpload from "../fileUpload/roadFileUpload.vue";
import axios from "axios";
import { getToken } from "@/utils/auth";
import { tableConfigFilter } from "../constants";
export default {
  name: "Road",
  components: { roadFileUpload },
  data() {
    return {
      tableConfig:tableConfigFilter('road'),
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 隧道检查-路面表格数据
      roadList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      batchAddTitle: "",
      batchAddOpen: false,
      token: null,
      isExport: true,
      isExportIng: false,
      fileLists: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectTunnelInspectionId: null,
        projectCode: null,
        projectName: null,
        tunnelCode: null,
        tunnelName: null,
        beginTime: null,
        endTime: null,
        createBeginTime: null,
        createEndTime: null,
        pileNumber: null,
        roadType: null,
        diseaseLocation: null,
        diseaseType: null,
        crackLength: null,
        crackWidth: null,
        area: null,
        pictureUrl: null,
        conditionDiscreption: null,
        conditionValue: null,
        creatorCode: null,
        creatorName: null,
        creatorTime: null,
        modifierCode: null,
        modifierName: null,
        modifierTime: null,
        isAvaliable: null,
        isDeleted: null,
        onDate: [],
        diseaseTypeChooseQuery: [],
      },
      projects: [],
      diseaseTypes: [
        // {diseaseType:'横向裂缝'},
        // {diseaseType:'纵向裂缝'},
        // {diseaseType:'龟裂'},
        // {diseaseType:'块裂'},
        // {diseaseType:'车辙'},
        // {diseaseType:'沉陷'},
        // {diseaseType:'波浪拥包'},
        // {diseaseType:'拱起'},
        // {diseaseType:'坑槽'},
        // {diseaseType:'松散'},
        // {diseaseType:'泛油'},
        // {diseaseType:'渗水积水'},
        // {diseaseType:'结冰'},
        // {diseaseType:'裂缝'},
        // {diseaseType:'露骨'},
        // {diseaseType:'坑洞'},
        // {diseaseType:'剥落'},
        // {diseaseType:'接缝错台'},
        // {diseaseType:'板角破损'},
        // {diseaseType:'破碎板'}
      ],
      diseaseTypesForLiQing: [
        {
          label: "裂缝",
          children: [
            { label: "横向裂缝" },
            { label: "纵向裂缝" },
            { label: "龟裂" },
            { label: "块裂" },
          ],
        },
        {
          label: "路面变形",
          children: [
            { label: "车辙" },
            { label: "沉陷" },
            { label: "波浪拥包" },
          ],
        },
        { label: "拱起", children: [{ label: "拱起" }] },
        {
          label: "表面损坏",
          children: [{ label: "坑槽" }, { label: "松散" }, { label: "泛油" }],
        },
        {
          label: "积水渗水涌水",
          children: [{ label: "渗水积水" }, { label: "结冰" }],
        },
      ],
      diseaseTypesForShuiNi: [
        { label: "裂缝", children: [{ label: "裂缝" }] },
        {
          label: "表面损坏",
          children: [{ label: "露骨" }, { label: "坑洞" }, { label: "剥落" }],
        },
        { label: "错台断缝", children: [{ label: "接缝错台" }] },
        {
          label: "缺损",
          children: [{ label: "板角破损" }, { label: "破碎板" }],
        },
      ],
      formDiseaseTypes: [],
      diseaseLocations: [
        { diseaseLocation: "行车道" },
        { diseaseLocation: "超车道" },
        { diseaseLocation: "紧急停车带" },
      ],
      tunnels: [],
      roadTypes: [{ roadType: "沥青路面" }, { roadType: "水泥路面" }],

      // 表单参数
      form: {
        date1: [],
      },
      // 表单校验
      rules: {
        // remainShelfLife :[{ pattern: /^[0-9]*[1-9][0-9]*$/, message: '只能输入正整数', trigger: 'blur' }],
        // totalShelfLife :[{required: true, message: '总货架寿命不能为空!', trigger: 'blur'},{ pattern: /^[0-9]*[1-9][0-9]*$/, message: '只能输入正整数', trigger: 'blur' }],
        // warehouseLatestDeliveryDay :[{ pattern: /^[0-9]*[1-9][0-9]*$/, message: '只能输入正整数', trigger: 'blur' }],
        // storeBestSaleDay :[{ pattern: /^[0-9]*[1-9][0-9]*$/, message: '只能输入正整数', trigger: 'blur' }],
        // maxSaleDay :[{ pattern: /^[0-9]*[1-9][0-9]*$/, message: '只能输入正整数', trigger: 'blur' }],
        // materialCode :[{required: true, message: '物料编码不能为空!', trigger: 'blur'},{ validator:validatePass },{max:30, message: '物料编码最大为30个字符',trigger:'blur'}],
        /*skuDateList: [{required: true, message: '有效日期不能为空', trigger: 'blur'}],*/
        tunnelCode: [
          { required: true, message: "隧道名称不能为空", trigger: "blur" },
        ],
        projectCode: [
          { required: true, message: "项目名称不能为空", trigger: "blur" },
        ],
        pileNumber: [
          { required: true, message: "桩号不能为空", trigger: "blur" },
        ],
        conditionValue: [
          { pattern: /^[0-4]?$/, message: "只能输0-4的整数", trigger: "blur" },
          { required: true, message: "状况值不能为空", trigger: "blur" },
        ],
        // area :[{required: true, pattern: /^[0-9]*[1-9][0-9]*$/, message: '只能输入正整数', trigger: 'blur' }],
        // crackLength :[{ required: true, pattern: /^[0-9]*[1-9][0-9]*$/, message: '只能输入正整数', trigger: 'blur' }],
        // crackWidth :[{ required: true, pattern: /^[0-9]*[1-9][0-9]*$/, message: '只能输入正整数', trigger: 'blur' }],
        layoutNum: [
          {
            required: true,
            pattern: /^[1-9]*[1-9][0-9]*$/,
            message: "只能输入大于1整数",
            trigger: "blur",
          },
        ],
        // num:[{ pattern: /^\d{1,8}(\.\d{0,3})?$/, message: '只能输数字，最多三位整数，最多3位小数', trigger: 'blur' },{ required: true, message: '数量不能为空', trigger: 'blur'}],
        // combineSkuUnit:[{ required: true, message: '单位不能为空', trigger: 'blur'}],
        // merchantId: [{required: true, message: '商家不能为空', trigger: 'blur'}],
        /*standardPrice: [
            { required: true, message: '标准价不能为空', trigger: 'blur' },
            {
                validator (rule, value, callback) {
                    var pattern = '^\\d{1,8}(\\.\\d{0,2})?$';
                    var reg = new RegExp(pattern, 'g');
                    if (reg.test(value)) {
                        callback()
                    } else {
                        callback(new Error('输入非法'))
                    }
                },
                trigger: 'blur'
            }
        ],*/
        // isDirectSending: [{required: true, message: '是否直送不能为空', trigger: 'blur'}],
        // needRequired: [{required: true, message: '是否质检不能为空', trigger: 'blur'}],
        // abbreviation: [{max:50, message: '简称最大为50个字符',trigger:'blur'}],
        // name: [{required: true, message: 'sku名称不能为空', trigger: 'blur'},{max:50, message: 'sku名称最大为50个字符',trigger:'blur'}]
      },
    };
  },
  created() {
    this.getList();
    this.getProject();
    this.getTunnel();
    this.token = getToken();
  },
  methods: {
    /** 查询隧道检查-路面列表 */
    getList(query = {}) {
      this.loading = true;
      this.reset();
      this.queryParams = { ...this.queryParams, ...query };
      if (
        this.queryParams.onDate != null &&
        this.queryParams.onDate.length != 0
      ) {
        this.queryParams.createBeginTime = this.queryParams.onDate[0];
        this.queryParams.createEndTime = this.queryParams.onDate[1];
      }
      if (
        this.queryParams.diseaseTypeChooseQuery != null &&
        this.queryParams.diseaseTypeChooseQuery.length != 0
      ) {
        this.queryParams.diseaseType =
          this.queryParams.diseaseTypeChooseQuery[
            this.queryParams.diseaseTypeChooseQuery.length - 1
          ];
      }
      listRoad(this.queryParams).then((response) => {
        this.roadList = response.rows;
        this.roadList.forEach((item) => {
          Object.assign(item, { prePictureList: [item.pictureUrl] });
        });
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    closeFileUpload() {
      this.handleQuery();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectTunnelInspectionId: null,
        tunnelCode: null,
        tunnelName: null,
        beginTime: null,
        endTime: null,
        pileNumber: null,
        roadType: null,
        diseaseLocation: null,
        diseaseType: null,
        crackLength: null,
        crackWidth: null,
        area: null,
        pictureUrl: null,
        conditionDiscreption: null,
        conditionValue: null,
        creatorCode: null,
        creatorName: null,
        creatorTime: null,
        modifierCode: null,
        modifierName: null,
        modifierTime: null,
        isAvaliable: null,
        isDeleted: null,
        diseaseTypechoose: [],
      };
      this.resetForm("form");
      this.fileLists = [];
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    changeDiseaseQuery(val) {
      this.queryParams.diseaseType = val[val.length - 1];
    },
    changeDiseaseData(val) {
      this.form.diseaseType = val[val.length - 1];
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.createEndTime = null;
      this.queryParams.createBeginTime = null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      console.log("56456465");
      this.open = true;
      console.log(this.open);
      this.title = "添加隧道检查-路面";
    },
    /** 批量新增按钮操作 */
    handleBatchAdd() {
      this.batchAddOpen = true;
      this.batchAddTitle = "批量新增";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getRoad(id).then((response) => {
        this.form = response.data;
        this.form.date1 = [response.data.beginTime, response.data.endTime];
        if (response.data.pictureUrl != null) {
          let pictureList = [response.data.pictureUrl];
          this.fileLists = pictureList.map((item) => {
            return {
              name: item,
              url: item,
            };
          });
        }
        this.open = true;
        this.title = "修改隧道检查-路面";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.date1 != null && this.form.date1.length != 0) {
            this.form.beginTime = this.form.date1[0];
            this.form.endTime = this.form.date1[1];
          }
          if (
            this.form.diseaseTypechoose != null &&
            this.form.diseaseTypechoose.length != 0
          ) {
            this.form.diseaseType =
              this.form.diseaseTypechoose[
                this.form.diseaseTypechoose.length - 1
              ];
          }
          this.projects.forEach((item, index, arr) => {
            if (item.projectCode == this.form.projectCode) {
              this.form.projectName = item.projectName;
            }
          });
          this.tunnels.forEach((item, index, arr) => {
            if (item.tunnelCode == this.form.tunnelCode) {
              this.form.tunnelName = item.tunnelName;
            }
          });
          if (this.form.id != null) {
            updateRoad(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRoad(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除隧道检查-路面编号为"' + ids + '"的数据项？')
        .then(function () {
          return delRoad(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.roadList.length == 0) {
        this.$alert("无导出数据", "", {
          confirmButtonText: "确定",
        });
      } else {
        //开启loading
        this.isExport = false;
        this.isExportIng = true;
        if (
          this.queryParams.onDate != null &&
          this.queryParams.onDate.length != 0
        ) {
          this.queryParams.createBeginTime = this.queryParams.onDate[0];
          this.queryParams.createEndTime = this.queryParams.onDate[1];
        }
        if (
          this.queryParams.diseaseTypeChooseQuery != null &&
          this.queryParams.diseaseTypeChooseQuery.length != 0
        ) {
          this.queryParams.diseaseType =
            this.queryParams.diseaseTypeChooseQuery[
              this.queryParams.diseaseTypeChooseQuery.length - 1
            ];
        }
        exportData(this.queryParams)
          .then((res) => {
            if (res.code == "500") {
              this.$modal.msgSuccess("导出失败，请联系开发人员");
              return;
            } else {
              this.$modal.msgSuccess("导出成功");
            }
            let blob = new Blob([res], { type: "application/force-download" });
            console.log(blob);
            let fileReader = new FileReader();
            fileReader.readAsDataURL(blob);
            fileReader.onload = (e) => {
              let a = document.createElement("a");
              let date = new Date();
              let year = date.getFullYear();
              let month = date.getMonth() + 1;
              let day = date.getDate();
              a.download = `导出数据${year + "_" + month + "_" + day}.xlsx`;
              a.href = e.target.result;
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
            };
            this.isExport = true;
            this.isExportIng = false;
          })
          .catch(() => {
            this.isExport = true;
            this.isExportIng = false;
          });
      }
    },
    sizeChangeHandle(val) {
      this.queryParams.pageSize = val;
      this.queryParams.pageNum = 1;
      this.getList();
    },
    currentChangeHandle: function currentChangeHandle(val) {
      this.queryParams.pageNum = val;
      this.getList();
    },
    handleExceed(files, fileList) {
      this.$modal.msgWarning(`只能上传一个文件`);
    },
    getProject() {
      getProjects().then((response) => {
        this.projects = response.data;
      });
    },
    getTunnel() {
      getTunnels().then((response) => {
        this.tunnels = response.data;
        this.tunnels.forEach(function (item, index, array) {
          item.tunnelNameAndHole = item.tunnelName + "-" + item.hole;
        });
      });
    },
    //上传图片
    fileUpload: function (response, file, fileList) {
      let pictureList = [response.msg];
      this.fileLists = pictureList.map((item) => {
        return {
          name: item,
          url: item,
        };
      });
      var url = response.msg;
      this.form.pictureUrl = url;
    },
    //上传图片
    handleRemove() {
      this.form.pictureUrl = null;
    },
    diseaseTypeChange() {
      if (this.queryParams.roadType == "沥青路面") {
        this.diseaseTypes = this.diseaseTypesForLiQing;
      } else if (this.queryParams.roadType == "水泥路面") {
        this.diseaseTypes = this.diseaseTypesForShuiNi;
      } else {
        this.diseaseTypes = [];
      }
      console.log("asljdaslkj");
      this.queryParams.diseaseType = null;
    },
    formDiseaseTypeChange() {
      if (this.form.roadType == "沥青路面") {
        this.formDiseaseTypes = this.diseaseTypesForLiQing;
      } else if (this.form.roadType == "水泥路面") {
        this.formDiseaseTypes = this.diseaseTypesForShuiNi;
      } else {
        this.formDiseaseTypes = [];
      }
      console.log("asljdaslkj");
      this.form.diseaseType = null;
    },
  },
};
</script>

<style lang="scss" scoped>
.batch-container {
  .download {
    color: #409eff;
    cursor: pointer;
  }

  .desc-section {
    display: flex;
    flex-direction: column;
  }
  .img-list {
    display: flex;

    .img-item {
      width: 50px;
      height: 50px;
      margin-left: 5px;

      &:first-child {
        margin-left: 0;
      }
    }
  }
}
.app-container {
  height: 200%;
  padding: 10 20px;
  width: 100%;

  .search-section {
    padding: 20px 30;
  }

  .page-section {
    padding-top: 20px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}
.myInput {
  width: 215px;
}
</style>
