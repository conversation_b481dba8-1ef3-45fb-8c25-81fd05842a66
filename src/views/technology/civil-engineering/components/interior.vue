<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            @click="handleAdd"
            v-hasPermi="['tunnel:interior:add']"
        >新增
        </el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['tunnel:interior:edit']"
        >修改
        </el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['tunnel:interior:remove']"
        >删除
        </el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            @click="handleBatchAdd"
            v-hasPermi="['tunnel:interior:batchAdd']"
        >批量新增
        </el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            @click="handleExport"
            v-hasPermi="['tunnel:interior:export']"
            v-if="isExport"
        >导出
        </el-button
        >
        <el-button type="primary" v-if="isExportIng" :loading="true"
        >导出中
        </el-button
        >
      </el-col>
      <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
        v-loading="loading"
        :data="interiorList"
        @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column type="index" label="序号" width="60"/>
      <el-table-column
          v-for="(item, index) in tableConfig"
          :key="index"
          :prop="item.prop"
          :show-tooltip-when-overflow="true"
          :label="item.label"
          :min-width="item?.width"
          align="center"
      >
        <template v-slot:default="scope" v-if="item.prop === 'defectImages'">
          <div v-if="scope.row.defectImages" class="img-list">
            <el-image
                v-for="(img, imgIndex) in scope.row.defectImages.split(',')"
                :key="imgIndex"
                :src="img"
                :preview-src-list="scope.row.defectImages.split(',')"
                style="height: 50px; width: 50px;"
                fit="cover"
                preview-teleported
            />
          </div>
          <span v-else>暂无图片</span>
        </template>
      </el-table-column>
      <el-table-column
          fixed="right"
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          width="200"
      >
        <template v-slot:default="scope">
          <el-button
              type="success"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['tunnel:interior:edit']"
          >修改
          </el-button
          >
          <el-button
              type="danger"
              @click="handleDelete(scope.row)"
              v-hasPermi="['tunnel:interior:remove']"
          >删除
          </el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="queryParams.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="queryParams.pageSize"
        :total="total"
        layout="->,total, sizes, prev, pager, next, jumper"
        style="margin-top: 10px; text-align: center"
    >
    </el-pagination>

    <!-- 添加或修改隧道检查-内装饰对话框 -->
    <el-dialog :title="title" v-model="open" width="600px">
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名称" prop="projectCode">
              <el-select
                  v-model="form.projectCode"
                  placeholder="请选择"
                  clearable
                  style="width: 300px"
              >
                <el-option
                    v-for="item in projects"
                    :key="item.projectCode"
                    :label="item.projectName"
                    :value="item.projectCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="隧道名称" prop="tunnelCode">
              <el-select
                  v-model="form.tunnelCode"
                  placeholder="请选择"
                  clearable
                  style="width: 300px"
              >
                <el-option
                    v-for="item in tunnels"
                    :key="item.tunnelCode"
                    :label="item.tunnelNameAndHole"
                    :value="item.tunnelCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="病害桩号" prop="pileNumber">
              <el-input
                  v-model="form.pileNumber"
                  placeholder="请输入病害桩号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="病害位置" prop="defectLocation">
              <el-select
                  v-model="form.defectLocation"
                  placeholder="请选择"
                  clearable
                  style="width: 300px"
              >
                <el-option
                    v-for="item in diseaseLocations"
                    :key="item.diseaseLocation"
                    :label="item.diseaseLocation"
                    :value="item.diseaseLocation"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="病害类型" prop="diseaseType">
              <el-select
                  v-model="form.diseaseType"
                  placeholder="请选择"
                  clearable
                  style="width: 300px"
              >
                <el-option
                    v-for="item in diseaseTypes"
                    :key="item.diseaseType"
                    :label="item.diseaseType"
                    :value="item.diseaseType"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检测时间" prop="inspectionTime">
              <el-date-picker
                  clearable
                  v-model="form.inspectionTime"
                  type="date"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择检测时间"
                  style="width: 300px"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="数量" prop="num">
              <el-input
                  v-model="form.num"
                  placeholder="请输入数量"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="长度(m)" prop="lengthValue">
              <el-input
                  v-model="form.lengthValue"
                  placeholder="请输入长度"
                  class="myInput"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="宽度(m)" prop="widthValue">
              <el-input
                  v-model="form.widthValue"
                  placeholder="请输入宽度"
                  class="myInput"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="面积(m²)" prop="areaValue">
              <el-input
                  v-model="form.areaValue"
                  placeholder="请输入面积"
                  class="myInput"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状况描述" prop="conditionDiscreption">
              <el-input
                  v-model="form.conditionDiscreption"
                  placeholder="请输入状况描述"
                  type="textarea"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状况值" prop="conditionValue">
              <el-input
                  v-model="form.conditionValue"
                  placeholder="请输入状况值"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="距进洞" prop="distanceEntranceHole">
              <el-input
                  v-model="form.distanceEntranceHole"
                  placeholder="请输入距进洞"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="距出洞" prop="distanceOutHole">
              <el-input
                  v-model="form.distanceOutHole"
                  placeholder="请输入距出洞"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="距左边墙" prop="fromLeftWall">
              <el-input
                  v-model="form.fromLeftWall"
                  placeholder="请输入距左边墙"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="距右边墙" prop="fromRightWall">
              <el-input
                  v-model="form.fromRightWall"
                  placeholder="请输入距右边墙"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="展布图名称" prop="layoutName">
              <el-input
                  v-model="form.layoutName"
                  placeholder="请输入典型病害图片名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="病害大类" prop="diseaseBigType">
              <el-input
                  v-model="form.diseaseBigType"
                  placeholder="请输入病害类型"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="病害坐标Ax" prop="coordinateAx">
              <el-input
                  v-model="form.coordinateAx"
                  placeholder="请输入病害坐标Ax"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="病害坐标Ay" prop="coordinateAy">
              <el-input
                  v-model="form.coordinateAy"
                  placeholder="请输入病害坐标Ay"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="病害坐标Bx" prop="coordinateBx">
              <el-input
                  v-model="form.coordinateBx"
                  placeholder="请输入病害坐标Bx"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="病害坐标By" prop="coordinateBy">
              <el-input
                  v-model="form.coordinateBy"
                  placeholder="请输入病害坐标By"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="病害坐标Cx" prop="coordinateCx">
              <el-input
                  v-model="form.coordinateCx"
                  placeholder="请输入病害坐标Cx"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="病害坐标Cy" prop="coordinateCy">
              <el-input
                  v-model="form.coordinateCy"
                  placeholder="请输入病害坐标Cy"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="病害坐标Dx" prop="coordinateDx">
              <el-input
                  v-model="form.coordinateDx"
                  placeholder="请输入病害坐标Dx"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="病害坐标Dy" prop="coordinateDy">
              <el-input
                  v-model="form.coordinateDy"
                  placeholder="请输入病害坐标Dy"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="重点病害" prop="isKeyDefect">
              <el-radio-group v-model="form.isKeyDefect">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="重点病害编号" prop="keyDefectNumber">
              <el-input
                  v-model="form.keyDefectNumber"
                  placeholder="请输入重点病害编号"
                  class="myInput"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="维修状态" prop="maintenanceStatus">
              <el-select
                  v-model="form.maintenanceStatus"
                  placeholder="请选择维修状态"
                  style="width: 100%"
              >
                <el-option label="未维修" value="0"/>
                <el-option label="维修中" value="1"/>
                <el-option label="已维修" value="2"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维修时间" prop="maintenanceTime">
              <el-date-picker
                  clearable
                  v-model="form.maintenanceTime"
                  type="datetime"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择维修时间"
                  style="width: 100%"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="结束桩号" prop="endPileNumber">
              <el-input
                  v-model="form.endPileNumber"
                  placeholder="请输入结束桩号"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="20">
            <el-form-item label="病害图片" prop="defectImages">
              <el-upload
                  class="upload-demo"
                  action="/dev-api/tunnel/upload/uploadPicture"
                  :on-remove="handleDefectImagesRemove"
                  :on-success="defectImagesUpload"
                  :show-file-list="true"
                  :headers="{ Authorization: 'Bearer ' + token }"
                  list-type="picture-card"
                  :file-list="defectImagesLists"
                  :limit="5"
                  :on-exceed="handleExceed"
              >
                <el-button type="primary">上传病害图片</el-button>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" style="text-align: right;">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 批量上传 -->
    <el-dialog
        v-model="batchAddOpen"
        :title="batchAddTitle"
        width="40%"
        :destroy-on-close="true"
        @closed="closeFileUpload()"
    >
      <interiorFileUpload></interiorFileUpload>
    </el-dialog>
  </div>
</template>

<script>
import {
  listInterior,
  getInterior,
  delInterior,
  addInterior,
  updateInterior,
  exportData,
  getProjects,
  getTunnels,
} from "@/api/system/interior";
import interiorFileUpload from "../fileUpload/interiorFileUpload.vue";
import axios from "axios";
import {getToken} from "@/utils/auth";
import {tableConfigFilter} from "../constants";

export default {
  name: "Interior",
  components: {interiorFileUpload},
  data() {
    return {
      tableConfig: tableConfigFilter('interior'),
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 隧道检查-内装饰表格数据
      interiorList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      batchAddTitle: "",
      batchAddOpen: false,
      token: null,
      isExport: true,
      isExportIng: false,
      // 病害图片列表
      defectImagesLists: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectTunnelInspectionId: null,
        tunnelCode: null,
        tunnelName: null,
        createBeginTime: null,
        createEndTime: null,
        pileNumber: null,
        defectLocation: null,
        diseaseType: null,
        num: null,
        lengthValue: null,
        widthValue: null,
        areaValue: null,
        conditionDiscreption: null,
        conditionValue: null,
        onDate: [],
        offDate: [],
        inspectionTime: null,
        layoutName: null,
        distanceEntranceHole: null,
        distanceOutHole: null,
        fromLeftWall: null,
        fromRightWall: null,
        coordinateAx: null,
        coordinateAy: null,
        coordinateBx: null,
        coordinateBy: null,
        coordinateCx: null,
        coordinateCy: null,
        coordinateDx: null,
        coordinateDy: null,
        diseaseBigType: null,
        commonPictureName: null,
        commonPictureNum: null,
        isKeyDefect: null,
        defectNumber: null,
        keyDefectNumber: null,
      },
      projects: [],
      diseaseTypes: [
        {diseaseType: "表面脏污"},
        {diseaseType: "饰面砖缺损"},
        {diseaseType: "防火涂层剥落"},
        {diseaseType: "防火涂层刮擦刮痕"},
        {diseaseType: "防火涂层开裂"},
        {diseaseType: "防火涂层脱空"},
        {diseaseType: "防火涂层空鼓"},
        {diseaseType: "装饰板变形"},
        {diseaseType: "装饰板破损"},
      ],
      diseaseLocations: [
        {diseaseLocation: "拱顶"},
        {diseaseLocation: "左侧拱腰"},
        {diseaseLocation: "右侧拱腰"},
        {diseaseLocation: "左侧边墙"},
        {diseaseLocation: "右侧边墙"},
      ],
      tunnels: [],
      // 表单参数
      form: {
        date1: [],
      },
      // 表单校验
      rules: {
        tunnelCode: [
          {required: true, message: "隧道名称不能为空", trigger: "blur"},
        ],
        projectCode: [
          {required: true, message: "项目名称不能为空", trigger: "blur"},
        ],
        pileNumber: [
          {required: true, message: "桩号不能为空", trigger: "blur"},
        ],
        conditionValue: [
          {pattern: /^[0-4]?$/, message: "只能输0-4的整数", trigger: "blur"},
          {required: true, message: "状况值不能为空", trigger: "blur"},
        ],
        // area :[{required: true, pattern: /^[0-9]*[1-9][0-9]*$/, message: '只能输入正整数', trigger: 'blur' }],
        conditionDiscreption: [
          {required: true, message: "状况描述不能为空", trigger: "blur"},
        ],
      },
    };
  },
  created() {
    this.getList();
    this.getProject();
    this.getTunnel();
    this.token = getToken();
  },
  methods: {
    /** 查询隧道检查-内装饰列表 */
    getList(query = {}) {
      this.loading = true;
      this.reset();
      this.queryParams = {...this.queryParams, ...query};
      if (
          this.queryParams.onDate != null &&
          this.queryParams.onDate.length != 0
      ) {
        this.queryParams.createBeginTime = this.queryParams.onDate[0];
        this.queryParams.createEndTime = this.queryParams.onDate[1];
      }
      listInterior(this.queryParams).then((response) => {
        this.interiorList = response.rows;
        this.interiorList.forEach((item) => {
          Object.assign(item, {prePictureList: [item.pictureUrl]});
        });
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    closeFileUpload() {
      this.handleQuery();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectTunnelInspectionId: null,
        tunnelCode: null,
        tunnelName: null,
        pileNumber: null,
        defectLocation: null,
        diseaseType: null,
        num: null,
        lengthValue: null,
        widthValue: null,
        areaValue: null,
        conditionDiscreption: null,
        conditionValue: null,
        inspectionTime: null,
        layoutName: null,
        distanceEntranceHole: null,
        distanceOutHole: null,
        fromLeftWall: null,
        fromRightWall: null,
        coordinateAx: null,
        coordinateAy: null,
        coordinateBx: null,
        coordinateBy: null,
        coordinateCx: null,
        coordinateCy: null,
        coordinateDx: null,
        coordinateDy: null,
        diseaseBigType: null,
        commonPictureName: null,
        commonPictureNum: null,
        isKeyDefect: null,
        defectNumber: null,
        keyDefectNumber: null,
      };
      this.defectImagesLists = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.createEndTime = null;
      this.queryParams.createBeginTime = null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加隧道检查-内装饰";
    },
    /** 批量新增按钮操作 */
    handleBatchAdd() {
      console.log("56456465");
      this.batchAddOpen = true;
      this.batchAddTitle = "批量新增";
      console.log("111111");
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getInterior(id).then((response) => {
        this.form = response.data;
        this.form.date1 = [response.data.beginTime, response.data.endTime];
        if (response.data.defectImages) {
          this.defectImagesLists = response.data.defectImages.split(',').map(url => ({
            name: url.substring(url.lastIndexOf('/') + 1),
            url: url
          }));
        }
        this.open = true;
        this.title = "修改隧道检查-内装饰";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.date1 != null && this.form.date1.length != 0) {
            this.form.beginTime = this.form.date1[0];
            this.form.endTime = this.form.date1[1];
          }
          this.projects.forEach((item, index, arr) => {
            if (item.projectCode == this.form.projectCode) {
              this.form.projectName = item.projectName;
            }
          });
          this.tunnels.forEach((item, index, arr) => {
            if (item.tunnelCode == this.form.tunnelCode) {
              this.form.tunnelName = item.tunnelName;
            }
          });
          if (this.form.id != null) {
            updateInterior(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInterior(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      debugger;
      const ids = row.id || this.ids;
      this.$modal
          .confirm('是否确认删除隧道检查-内装饰编号为"' + ids + '"的数据项？')
          .then(function () {
            return delInterior(ids);
          })
          .then(() => {
            this.getList();
            this.$modal.msgSuccess("删除成功");
          })
          .catch(() => {
          });
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.interiorList.length == 0) {
        this.$alert("无导出数据", "", {
          confirmButtonText: "确定",
        });
      } else {
        //开启loading
        this.isExport = false;
        this.isExportIng = true;
        if (
            this.queryParams.onDate != null &&
            this.queryParams.onDate.length != 0
        ) {
          this.queryParams.createBeginTime = this.queryParams.onDate[0];
          this.queryParams.createEndTime = this.queryParams.onDate[1];
        }
        exportData(this.queryParams)
            .then((res) => {
              if (res.code == "500") {
                this.$modal.msgSuccess("导出失败，请联系开发人员");
                return;
              } else {
                this.$modal.msgSuccess("导出成功");
              }
              let blob = new Blob([res], {type: "application/force-download"});
              console.log(blob);
              let fileReader = new FileReader();
              fileReader.readAsDataURL(blob);
              fileReader.onload = (e) => {
                let a = document.createElement("a");
                let date = new Date();
                let year = date.getFullYear();
                let month = date.getMonth() + 1;
                let day = date.getDate();
                a.download = `导出数据${year + "_" + month + "_" + day}.xlsx`;
                a.href = e.target.result;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
              };
              this.isExport = true;
              this.isExportIng = false;
            })
            .catch(() => {
              this.isExport = true;
              this.isExportIng = false;
            });
      }
    },
    sizeChangeHandle(val) {
      this.queryParams.pageSize = val;
      this.queryParams.pageNum = 1;
      this.getList();
    },
    currentChangeHandle: function currentChangeHandle(val) {
      this.queryParams.pageNum = val;
      this.getList();
    },
    getProject() {
      getProjects().then((response) => {
        this.projects = response.data;
      });
    },
    getTunnel() {
      getTunnels().then((response) => {
        this.tunnels = response.data;
        this.tunnels.forEach(function (item, index, array) {
          item.tunnelNameAndHole = item.tunnelName + "-" + item.hole;
        });
      });
    },

    /**
     * 病害图片上传成功
     * @param {Object} res - 响应数据
     * @param {Object} file - 文件对象
     * @param {Array} fileList - 文件列表
     */
    defectImagesUpload(res, file, fileList) {
      if (res.code === 200) {
        let defectImagesList = fileList.map(item => {
          if (item.response) {
            return item.response.msg;
          } else {
            return item.url;
          }
        });
        this.defectImagesLists = fileList.map((item) => {
          let url = item.response ? item.response.msg : item.url;
          return {
            name: url,
            url: url,
          };
        });
        this.form.defectImages = defectImagesList.join(',');
      } else {
        this.$modal.msgError('图片上传失败');
      }
    },
    /**
     * 删除病害图片
     * @param {Object} file - 文件对象
     * @param {Array} fileList - 文件列表
     */
    handleDefectImagesRemove(file, fileList) {
      let defectImagesList = fileList.map(item => {
        if (item.response) {
          return item.response.msg;
        } else {
          return item.url;
        }
      });
      this.defectImagesLists = fileList.map((item) => {
        let url = item.response ? item.response.msg : item.url;
        return {
          name: url,
          url: url,
        };
      });
      this.form.defectImages = defectImagesList.join(',');
    },

    /**
     * 文件超出限制处理
     */
    handleExceed() {
      this.$modal.msgWarning('最多只能上传5张图片');
    },
  },
};
</script>

<style scoped>
.batch-container {
  .download {
    color: #409eff;
    cursor: pointer;
  }

  .desc-section {
    margin-top: 20px;
    color: #999;
  }

  .img-list {
    display: flex;

    .img-item {
      width: 50px;
      height: 50px;
      margin-left: 5px;

      &:first-child {
        margin-left: 0;
      }
    }
  }
}

/* 主容器样式 */
.app-container {
  height: 200%;
  padding: 10px 20px;
  width: 100%;

  .search-section {
    padding: 20px 30px;
  }

  .page-section {
    padding-top: 20px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}

/* 表单输入框样式 */
.myInput {
  width: 287px;
}

/* 必填项标识样式 */
.slot_form_label::after {
  content: "*";
  width: 100%;
  display: inline-block;
  position: absolute;
  left: -10px;
}
</style>