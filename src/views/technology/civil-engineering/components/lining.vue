<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          @click="handleAdd"
          v-hasPermi="['tunnel:lining:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['tunnel:lining:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['tunnel:lining:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          @click="handleBatchAdd"
          v-hasPermi="['tunnel:lining:batchAdd']"
          >批量新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          size="mini"
          @click="handleExport"
          v-hasPermi="['tunnel:lining:export']"
          v-if="isExport"
          >导出</el-button
        >
        <el-button type="primary" v-if="isExportIng" :loading="true"
          >导出中</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="liningList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" fixed="left" label="序号" width="70" />
      <el-table-column
        v-for="(item, index) in tableConfig"
        :key="index"
        :prop="item.prop"
        show-tooltip-when-overflow="true"
        :label="item.label"
        :min-width="item?.width || 120"
      />
      <!-- <el-table-column
        label="项目编码"
        align="center"
        prop="projectCode"
        width="150"
        show-tooltip-when-overflow="true"
      />
      <el-table-column
        label="项目名称"
        align="center"
        prop="projectName"
        width="150"
        show-tooltip-when-overflow="true"
      />
      <el-table-column
        label="隧道编码"
        align="center"
        prop="tunnelCode"
        width="150"
        show-tooltip-when-overflow="true"
      />
      <el-table-column
        label="隧道名称"
        align="center"
        prop="tunnelNameAndHole"
        width="150"
        show-tooltip-when-overflow="true"
      />
      <el-table-column
        label="开始时间"
        align="center"
        prop="beginTime"
        width="150"
        show-tooltip-when-overflow="true"
      >
      </el-table-column>
      <el-table-column
        label="截至时间"
        align="center"
        prop="endTime"
        width="150"
        show-tooltip-when-overflow="true"
      >
      </el-table-column>
      <el-table-column
        label="检测时间"
        align="center"
        prop="inspectionTime"
        width="150"
        show-tooltip-when-overflow="true"
      >
      </el-table-column>
      <el-table-column
        label="桩号"
        align="center"
        prop="pileNumber"
        width="150"
        show-tooltip-when-overflow="true"
      />
      <el-table-column
        label="病害位置"
        align="center"
        prop="diseaseLocation"
        width="150"
        show-tooltip-when-overflow="true"
      />
      <el-table-column
        label="病害类型"
        align="center"
        prop="diseaseType"
        width="150"
        show-tooltip-when-overflow="true"
      /> -->
      <!--<el-table-column label="距进洞" align="center" prop="distanceEntranceHole" width="150" show-tooltip-when-overflow="true"/>-->
      <!--<el-table-column label="距出洞" align="center" prop="distanceOutHole" width="150" show-tooltip-when-overflow="true"/>-->
      <!--<el-table-column label="距左边墙" align="center" prop="fromLeftWall" width="150" show-tooltip-when-overflow="true"/>-->
      <!--<el-table-column label="距右边墙" align="center" prop="fromRightWall" width="150" show-tooltip-when-overflow="true"/>-->
      <!--<el-table-column label="裂缝宽度" align="center" prop="crackWidth" width="150" show-tooltip-when-overflow="true"/>-->
      <!-- <el-table-column
        label="裂缝长度"
        align="center"
        prop="crackLength"
        width="150"
        show-tooltip-when-overflow="true"
      />
      <el-table-column
        label="裂缝深度"
        align="center"
        prop="crackDepth"
        width="150"
        show-tooltip-when-overflow="true"
      />
      <el-table-column
        label="面积"
        align="center"
        prop="area"
        width="150"
        show-tooltip-when-overflow="true"
      />
      <el-table-column
        label="状况描述"
        align="center"
        prop="conditionDiscreption"
        width="150"
        show-tooltip-when-overflow="true"
      />
      <el-table-column
        label="状况值"
        align="center"
        prop="conditionValue"
        width="150"
        show-tooltip-when-overflow="true"
      /> -->
      <!--<el-table-column label="展布图编号" align="center" prop="layoutNum" width="150" show-tooltip-when-overflow="true"/>-->
      <!--<el-table-column label="典型病害图片名称" align="center" prop="diseaseNum" width="150" show-tooltip-when-overflow="true"/>-->

      <!--<el-table-column label="病害坐标Ax" align="center" prop="coordinateAx" width="150" show-tooltip-when-overflow="true"/>-->
      <!--<el-table-column label="病害坐标Ay" align="center" prop="coordinateAy" width="150" show-tooltip-when-overflow="true"/>-->
      <!--<el-table-column label="病害坐标Bx" align="center" prop="coordinateBx" width="150" show-tooltip-when-overflow="true"/>-->
      <!--<el-table-column label="病害坐标By" align="center" prop="coordinateBy" width="150" show-tooltip-when-overflow="true"/>-->
      <!--<el-table-column label="病害坐标Cx" align="center" prop="coordinateCx" width="150" show-tooltip-when-overflow="true"/>-->
      <!--<el-table-column label="病害坐标Cy" align="center" prop="coordinateCy" width="150" show-tooltip-when-overflow="true"/>-->
      <!--<el-table-column label="病害坐标Dx" align="center" prop="coordinateDx" width="150" show-tooltip-when-overflow="true"/>-->
      <!--<el-table-column label="病害坐标Dy" align="center" prop="coordinateDy" width="150" show-tooltip-when-overflow="true"/>-->
      <!--<el-table-column label="病害大类" align="center" prop="diseaseBigType" width="150" show-tooltip-when-overflow="true"/>-->
      <!--<el-table-column label="重点关注病害" align="center" prop="isFocusOn" width="150" show-tooltip-when-overflow="true">-->
      <!--<template v-slot:default="scope">-->
      <!--{{scope.row.isFocusOn==0?'否':'是'}}-->
      <!--</template>-->
      <!--</el-table-column>-->
      <!--<el-table-column label="普通病害图片名称" align="center" prop="commonPictureName" width="150" show-tooltip-when-overflow="true"/>-->
      <!--<el-table-column label="普通图片图号" align="center" prop="commonPictureNum" width="150" show-tooltip-when-overflow="true"/>-->

      <!-- <el-table-column
        label="创建人编码"
        align="center"
        prop="creatorCode"
        width="150"
        show-tooltip-when-overflow="true"
      />
      <el-table-column
        label="创建人名称"
        align="center"
        prop="creatorName"
        width="150"
        show-tooltip-when-overflow="true"
      />
      <el-table-column
        label="创建时间"
        align="center"
        prop="creatorTime"
        width="180"
        show-tooltip-when-overflow="true"
      >
      </el-table-column>
      <el-table-column
        label="修改人编码"
        align="center"
        prop="modifierCode"
        width="150"
        show-tooltip-when-overflow="true"
      />
      <el-table-column
        label="修改人名称"
        align="center"
        prop="modifierName"
        width="150"
        show-tooltip-when-overflow="true"
      />
      <el-table-column
        label="修改时间"
        align="center"
        prop="modifierTime"
        width="180"
        show-tooltip-when-overflow="true"
      >
      </el-table-column> -->
      <el-table-column
        fixed="right"
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="200"
      >
        <template v-slot:default="scope">
          <el-button
            size="mini"
            type="success"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['tunnel:lining:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(scope.row)"
            v-hasPermi="['tunnel:lining:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="queryParams.pageSize"
      :total="total"
      layout="->,total, sizes, prev, pager, next, jumper"
      style="margin-top: 10px; text-align: center"
    >
    </el-pagination>

    <!-- 添加或修改隧道检查-衬砌对话框 -->
    <el-dialog :title="title" v-model="open" width="50%">
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-row>
          <el-col :span="10">
            <el-form-item label="项目名称" prop="projectCode">
              <el-select
                v-model="form.projectCode"
                placeholder="请选择"
                clearable
                style="width: 300px"
              >
                <el-option
                  v-for="item in projects"
                  :key="item.projectCode"
                  :label="item.projectName"
                  :value="item.projectCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="隧道名称" prop="tunnelCode">
              <el-select
                v-model="form.tunnelCode"
                placeholder="请选择"
                clearable
                style="width: 300px"
              >
                <el-option
                  v-for="item in tunnels"
                  :key="item.tunnelCode"
                  :label="item.tunnelNameAndHole"
                  :value="item.tunnelCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="开始~截至时间:" prop="date1">
              <el-date-picker
                v-model="form.date1"
                type="daterange"
                range-separator="~"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                start-placeholder="开始时间"
                end-placeholder="截至时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="检测时间" prop="inspectionTime">
              <el-date-picker
                clearable
                v-model="form.inspectionTime"
                type="date"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                placeholder="请选择检测时间"
                style="width: 300px"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="桩号" prop="pileNumber">
              <el-input v-model="form.pileNumber" placeholder="请输入桩号" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="病害位置" prop="diseaseLocation">
              <el-select
                v-model="form.diseaseLocation"
                placeholder="请选择"
                clearable
                style="width: 300px"
              >
                <el-option
                  v-for="item in diseaseLocations"
                  :key="item.diseaseLocation"
                  :label="item.diseaseLocation"
                  :value="item.diseaseLocation"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="病害类型" prop="diseaseType">
              <el-cascader
                v-model="form.diseaseType"
                placeholder="请选择"
                clearable
                :options="diseaseTypes"
                :show-all-levels="false"
                style="width: 300px"
                :props="{
                  label: 'label',
                  value: 'label',
                  children: 'children',
                }"
                @change="changeDiseaseData"
              >
              </el-cascader>
              <!--              <el-select v-model="form.diseaseType" placeholder="请选择" clearable>-->
              <!--                <el-option-->
              <!--                    v-for="item in diseaseTypes"-->
              <!--                    :key="item.diseaseType"-->
              <!--                    :label="item.diseaseType"-->
              <!--                    :value="item.diseaseType">-->
              <!--                </el-option>-->
              <!--              </el-select>-->
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="距进洞" prop="distanceEntranceHole">
              <el-input
                v-model="form.distanceEntranceHole"
                placeholder="请输入距进洞"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="距出洞" prop="distanceOutHole">
              <el-input
                v-model="form.distanceOutHole"
                placeholder="请输入距出洞"
              />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="距左边墙" prop="fromLeftWall">
              <el-input
                v-model="form.fromLeftWall"
                placeholder="请输入距左边墙"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="距右边墙" prop="fromRightWall">
              <el-input
                v-model="form.fromRightWall"
                placeholder="请输入距右边墙"
              />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="裂缝宽度" prop="crackWidth">
              <el-input
                v-model="form.crackWidth"
                placeholder="请输入裂缝宽度"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="裂缝长度" prop="crackLength">
              <el-input
                v-model="form.crackLength"
                placeholder="请输入裂缝长度"
              />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="裂缝深度" prop="crackDepth">
              <el-input
                v-model="form.crackDepth"
                placeholder="请输入裂缝深度"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="面积" prop="area">
              <el-input v-model="form.area" placeholder="请输入面积" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="状况值" prop="conditionValue">
              <el-input
                v-model="form.conditionValue"
                placeholder="请输入状况值"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="典型病害图片名称" prop="diseaseNum">
              <el-input
                v-model="form.diseaseNum"
                placeholder="请输入典型病害图片名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="展布图编号" prop="layoutNum">
              <el-input
                v-model="form.layoutNum"
                placeholder="请输入展布图编号"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="病害坐标Ax" prop="coordinateAx">
              <el-input
                v-model="form.coordinateAx"
                placeholder="请输入病害坐标Ax"
              />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="病害坐标Ay" prop="coordinateAy">
              <el-input
                v-model="form.coordinateAy"
                placeholder="请输入病害坐标Ay"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="病害坐标Bx" prop="coordinateBx">
              <el-input
                v-model="form.coordinateBx"
                placeholder="请输入病害坐标Bx"
              />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="病害坐标By" prop="coordinateBy">
              <el-input
                v-model="form.coordinateBy"
                placeholder="请输入病害坐标By"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="病害坐标Cx" prop="coordinateCx">
              <el-input
                v-model="form.coordinateCx"
                placeholder="请输入病害坐标Cx"
              />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="病害坐标Cy" prop="coordinateCy">
              <el-input
                v-model="form.coordinateCy"
                placeholder="请输入病害坐标Cy"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="病害坐标Dx" prop="coordinateDx">
              <el-input
                v-model="form.coordinateDx"
                placeholder="请输入病害坐标Dx"
              />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="病害坐标Dy" prop="coordinateDy">
              <el-input
                v-model="form.coordinateDy"
                placeholder="请输入病害坐标Dy"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="病害类型" prop="diseaseBigType">
              <el-input
                v-model="form.diseaseBigType"
                placeholder="请输入病害类型"
              />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="重点关注病害" prop="isFocusOn">
              <el-select
                v-model="form.isFocusOn"
                placeholder="请选择重点关注病害"
              >
                <el-option label="否" :value="0"></el-option>
                <el-option label="是" :value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="普通病害图片名称" prop="commonPictureName">
              <el-input
                v-model="form.commonPictureName"
                placeholder="请输入普通图片名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="普通图片图号" prop="commonPictureNum">
              <el-input
                v-model="form.commonPictureNum"
                placeholder="请输入普通图片图号"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="状况描述" prop="conditionDiscreption">
              <el-input
                v-model="form.conditionDiscreption"
                placeholder="请输入状况描述"
                type="textarea"
              />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="结束桩号" prop="endPileNumber">
              <el-input v-model="form.endPileNumber" placeholder="请输入桩号" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 批量上传 -->
    <el-dialog
      v-model="batchAddOpen"
      :title="batchAddTitle"
      width="40%"
      destroy-on-close="true"
      @closed="closeFileUpload()"
    >
      <liningFileUpload></liningFileUpload>
    </el-dialog>
  </div>
</template>

<script>
import {
  listLining,
  getLining,
  delLining,
  addLining,
  updateLining,
  exportData,
  getProjects,
  getTunnels,
} from "@/api/system/lining";
import liningFileUpload from "../fileUpload/liningFileUpload.vue";
import axios from "axios";
import { getToken } from "@/utils/auth";
import { tableConfigFilter } from "../constants";
export default {
  name: "Lining",
  components: { liningFileUpload },
  data() {
    return {
      tableConfig:tableConfigFilter('lining'),
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 隧道检查-衬砌表格数据
      liningList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      batchAddTitle: "",
      batchAddOpen: false,
      token: null,
      isExport: true,
      isExportIng: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectTunnelInspectionId: null,
        tunnelCode: null,
        tunnelName: null,
        beginTime: null,
        endTime: null,
        createBeginTime: null,
        createEndTime: null,
        inspectionTime: null,
        pileNumber: null,
        diseaseLocation: null,
        diseaseType: null,
        diseaseLocationId: null,
        distanceEntranceHole: null,
        distanceOutHole: null,
        fromLeftWall: null,
        fromRightWall: null,
        crackWidth: null,
        crackLength: null,
        crackDepth: null,
        area: null,
        conditionDiscreption: null,
        conditionValue: null,
        creatorCode: null,
        creatorName: null,
        creatorTime: null,
        modifierCode: null,
        modifierName: null,
        modifierTime: null,
        isAvaliable: null,
        isDeleted: null,
        onDate: [],
        offDate: [],
        diseaseTypeChooseQuery: [],
        coordinateAx: null,
        coordinateAy: null,
        coordinateBx: null,
        coordinateBy: null,
        coordinateCx: null,
        coordinateCy: null,
        coordinateDx: null,
        coordinateDy: null,
        diseaseBigType: null,
        isFocusOn: null,
        commonPictureName: null,
        commonPictureNum: null,
      },
      projects: [],
      diseaseTypes: [
        {
          label: "裂缝",
          children: [
            { label: "纵向裂缝" },
            { label: "环向裂缝" },
            { label: "斜向裂缝" },
            { label: "网状裂缝" },
          ],
        },
        {
          label: "表面损坏",
          children: [
            { label: "蜂窝麻面" },
            { label: "表层起层" },
            { label: "剥落" },
            { label: "破损露筋" },
          ],
        },
        { label: "积水渗水涌水", children: [{ label: "衬砌渗漏水" }] },
        { label: "裂缝", children: [{ label: "施工缝开裂" }] },
        { label: "积水渗水涌水", children: [{ label: "施工缝渗漏水" }] },
      ],
      diseaseLocations: [
        { diseaseLocation: "主洞拱顶" },
        { diseaseLocation: "主洞左拱腰" },
        { diseaseLocation: "主洞右拱腰" },
        { diseaseLocation: "主洞左边墙" },
        { diseaseLocation: "主洞右边墙" },
      ],
      tunnels: [],
      // 表单参数
      form: {
        date1: [],
      },
      // 表单校验
      rules: {
        // remainShelfLife :[{ pattern: /^[0-9]*[1-9][0-9]*$/, message: '只能输入正整数', trigger: 'blur' }],
        // totalShelfLife :[{required: true, message: '总货架寿命不能为空!', trigger: 'blur'},{ pattern: /^[0-9]*[1-9][0-9]*$/, message: '只能输入正整数', trigger: 'blur' }],
        // area :[{required: true, pattern: /^[0-9]*[1-9][0-9]*$/, message: '只能输入正整数', trigger: 'blur' }],
        // crackLength :[{ required: true, pattern: /^[0-9]*[1-9][0-9]*$/, message: '只能输入正整数', trigger: 'blur' }],
        // crackWidth :[{ required: true, pattern: /^[0-9]*[1-9][0-9]*$/, message: '只能输入正整数', trigger: 'blur' }],
        layoutNum: [
          {
            required: true,
            pattern: /^[1-9]*[1-9][0-9]*$/,
            message: "只能输入大于1整数",
            trigger: "blur",
          },
        ],
        // materialCode :[{required: true, message: '物料编码不能为空!', trigger: 'blur'},{ validator:validatePass },{max:30, message: '物料编码最大为30个字符',trigger:'blur'}],
        /*skuDateList: [{required: true, message: '有效日期不能为空', trigger: 'blur'}],*/
        tunnelCode: [
          { required: true, message: "隧道名称不能为空", trigger: "blur" },
        ],
        projectCode: [
          { required: true, message: "项目名称不能为空", trigger: "blur" },
        ],
        pileNumber: [
          { required: true, message: "桩号不能为空", trigger: "blur" },
        ],
        conditionValue: [
          { pattern: /^[0-4]?$/, message: "只能输0-4的整数", trigger: "blur" },
          { required: true, message: "状况值不能为空", trigger: "blur" },
        ],
        // num:[{ pattern: /^\d{1,8}(\.\d{0,3})?$/, message: '只能输数字，最多三位整数，最多3位小数', trigger: 'blur' },{ required: true, message: '数量不能为空', trigger: 'blur'}],
        // combineSkuUnit:[{ required: true, message: '单位不能为空', trigger: 'blur'}],
        // merchantId: [{required: true, message: '商家不能为空', trigger: 'blur'}],
        /*standardPrice: [
            { required: true, message: '标准价不能为空', trigger: 'blur' },
            {
                validator (rule, value, callback) {
                    var pattern = '^\\d{1,8}(\\.\\d{0,2})?$';
                    var reg = new RegExp(pattern, 'g');
                    if (reg.test(value)) {
                        callback()
                    } else {
                        callback(new Error('输入非法'))
                    }
                },
                trigger: 'blur'
            }
        ],*/
        // isDirectSending: [{required: true, message: '是否直送不能为空', trigger: 'blur'}],
        // needRequired: [{required: true, message: '是否质检不能为空', trigger: 'blur'}],
        // abbreviation: [{max:50, message: '简称最大为50个字符',trigger:'blur'}],
        // name: [{required: true, message: 'sku名称不能为空', trigger: 'blur'},{max:50, message: 'sku名称最大为50个字符',trigger:'blur'}]
      },
    };
  },
  created() {
    this.getList();
    this.getProject();
    this.getTunnel();
    this.token = getToken();
  },
  methods: {
    /** 查询隧道检查-衬砌列表 */
    getList(query = {}) {
      this.loading = true;
      this.reset();
      this.queryParams = { ...this.queryParams, ...query };
      if (
        this.queryParams.onDate != null &&
        this.queryParams.onDate.length != 0
      ) {
        this.queryParams.createBeginTime = this.queryParams.onDate[0];
        this.queryParams.createEndTime = this.queryParams.onDate[1];
      }
      if (
        this.queryParams.diseaseTypeChooseQuery != null &&
        this.queryParams.diseaseTypeChooseQuery.length != 0
      ) {
        this.queryParams.diseaseType =
          this.queryParams.diseaseTypeChooseQuery[
            this.queryParams.diseaseTypeChooseQuery.length - 1
          ];
      }
      listLining(this.queryParams).then((response) => {
        this.liningList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectTunnelInspectionId: null,
        tunnelCode: null,
        tunnelName: null,
        beginTime: null,
        endTime: null,
        inspectionTime: null,
        pileNumber: null,
        diseaseLocation: null,
        diseaseType: null,
        diseaseLocationId: null,
        distanceEntranceHole: null,
        distanceOutHole: null,
        fromLeftWall: null,
        fromRightWall: null,
        crackWidth: null,
        crackLength: null,
        crackDepth: null,
        area: null,
        conditionDiscreption: null,
        conditionValue: null,
        diseaseNum: null,
        layoutNum: null,
        pictureName: null,
        creatorCode: null,
        creatorName: null,
        creatorTime: null,
        modifierCode: null,
        modifierName: null,
        modifierTime: null,
        isAvaliable: null,
        isDeleted: null,
        diseaseTypechoose: [],
        coordinateAx: null,
        coordinateAy: null,
        coordinateBx: null,
        coordinateBy: null,
        coordinateCx: null,
        coordinateCy: null,
        coordinateDx: null,
        coordinateDy: null,
        diseaseBigType: null,
        isFocusOn: null,
        commonPictureName: null,
        commonPictureNum: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    closeFileUpload() {
      this.handleQuery();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.createEndTime = null;
      this.queryParams.createBeginTime = null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    changeDiseaseQuery(val) {
      this.queryParams.diseaseType = val[val.length - 1];
    },
    changeDiseaseData(val) {
      this.form.diseaseType = val[val.length - 1];
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加隧道检查-衬砌";
    },
    /** 批量新增按钮操作 */
    handleBatchAdd() {
      console.log("56456465");
      this.batchAddOpen = true;
      this.batchAddTitle = "批量新增";
      console.log("111111");
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getLining(id).then((response) => {
        this.form = response.data;
        this.form.date1 = [response.data.beginTime, response.data.endTime];
        this.open = true;
        this.title = "修改隧道检查-衬砌";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.date1 != null && this.form.date1.length != 0) {
            this.form.beginTime = this.form.date1[0];
            this.form.endTime = this.form.date1[1];
          }
          if (
            this.form.diseaseTypechoose != null &&
            this.form.diseaseTypechoose.length != 0
          ) {
            this.form.diseaseType =
              this.form.diseaseTypechoose[
                this.form.diseaseTypechoose.length - 1
              ];
          }
          this.projects.forEach((item, index, arr) => {
            if (item.projectCode == this.form.projectCode) {
              this.form.projectName = item.projectName;
            }
          });
          this.tunnels.forEach((item, index, arr) => {
            if (item.tunnelCode == this.form.tunnelCode) {
              this.form.tunnelName = item.tunnelName;
            }
          });
          if (this.form.id != null) {
            updateLining(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addLining(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除隧道检查-衬砌编号为"' + ids + '"的数据项？')
        .then(function () {
          return delLining(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.liningList.length == 0) {
        this.$alert("无导出数据", "", {
          confirmButtonText: "确定",
        });
      } else {
        //开启loading
        this.isExport = false;
        this.isExportIng = true;
        if (
          this.queryParams.onDate != null &&
          this.queryParams.onDate.length != 0
        ) {
          this.queryParams.createBeginTime = this.queryParams.onDate[0];
          this.queryParams.createEndTime = this.queryParams.onDate[1];
        }
        if (
          this.queryParams.diseaseTypeChooseQuery != null &&
          this.queryParams.diseaseTypeChooseQuery.length != 0
        ) {
          this.queryParams.diseaseType =
            this.queryParams.diseaseTypeChooseQuery[
              this.queryParams.diseaseTypeChooseQuery.length - 1
            ];
        }
        exportData(this.queryParams)
          .then((res) => {
            if (res.code == "500") {
              this.$modal.msgSuccess("导出失败，请联系开发人员");
              return;
            } else {
              this.$modal.msgSuccess("导出成功");
            }
            let blob = new Blob([res], { type: "application/force-download" });
            console.log(blob);
            let fileReader = new FileReader();
            fileReader.readAsDataURL(blob);
            fileReader.onload = (e) => {
              let a = document.createElement("a");
              let date = new Date();
              let year = date.getFullYear();
              let month = date.getMonth() + 1;
              let day = date.getDate();
              a.download = `导出数据${year + "_" + month + "_" + day}.xlsx`;
              a.href = e.target.result;
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
            };
            this.isExport = true;
            this.isExportIng = false;
          })
          .catch(() => {
            this.isExport = true;
            this.isExportIng = false;
          });
      }
    },
    sizeChangeHandle(val) {
      this.queryParams.pageSize = val;
      this.queryParams.pageNum = 1;
      this.getList();
    },
    currentChangeHandle: function currentChangeHandle(val) {
      this.queryParams.pageNum = val;
      this.getList();
    },
    handleExceed(files, fileList) {
      this.$modal.msgWarning(`只能上传一个文件`);
    },
    getProject() {
      getProjects().then((response) => {
        this.projects = response.data;
      });
    },
    getTunnel() {
      getTunnels().then((response) => {
        this.tunnels = response.data;
        this.tunnels.forEach(function (item, index, array) {
          item.tunnelNameAndHole = item.tunnelName + "-" + item.hole;
        });
      });
    },
  },
};
</script>


<style lang="scss" scoped>
.batch-container {
  .download {
    color: #409eff;
    cursor: pointer;
  }

  .desc-section {
    display: flex;
    flex-direction: column;
  }
  .img-list {
    display: flex;

    .img-item {
      width: 50px;
      height: 50px;
      margin-left: 5px;

      &:first-child {
        margin-left: 0;
      }
    }
  }
}
.myInput {
  width: 287px;
}
</style>