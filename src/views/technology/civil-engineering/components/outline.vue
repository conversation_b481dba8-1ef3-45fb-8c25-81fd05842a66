<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            @click="handleAdd"
            v-hasPermi="['tunnel:outline:add']"
        >新增
        </el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['tunnel:outline:edit']"
        >修改
        </el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['tunnel:outline:remove']"
        >删除
        </el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            @click="handleBatchAdd"
            v-hasPermi="['tunnel:outline:batchAdd']"
        >批量新增
        </el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            @click="handleExport"
            v-hasPermi="['tunnel:outline:export']"
            v-if="isExport"
        >导出
        </el-button
        >
        <el-button type="primary" v-if="isExportIng" :loading="true"
        >导出中
        </el-button
        >
      </el-col>
      <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
        v-loading="loading"
        :data="outlineList"
        @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column type="index" label="序号" width="60"/>
      <el-table-column
          v-for="(item, index) in tableConfig"
          :key="index"
          :prop="item.prop"
          :show-tooltip-when-overflow="true"
          :label="item.label"
          :min-width="item?.width"
          align="center"
      >
        <template v-slot:default="scope" v-if="item.prop === 'defectImages'">
          <div v-if="scope.row.defectImages" class="img-list">
            <el-image
                v-for="(img, imgIndex) in scope.row.defectImages.split(',')"
                :key="imgIndex"
                :src="img"
                :preview-src-list="scope.row.defectImages.split(',')"
                style="height: 50px; width: 50px;"
                fit="cover"
                preview-teleported
            />
          </div>
          <span v-else>暂无图片</span>
        </template>
      </el-table-column>
      <el-table-column
          fixed="right"
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          width="200"
      >
        <template v-slot:default="scope">
          <el-button
              type="success"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['tunnel:outline:edit']"
          >修改
          </el-button
          >
          <el-button
              type="danger"
              @click="handleDelete(scope.row)"
              v-hasPermi="['tunnel:outline:remove']"
          >删除
          </el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="queryParams.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="queryParams.pageSize"
        :total="total"
        layout="->,total, sizes, prev, pager, next, jumper"
        style="margin-top: 10px; text-align: center"
    >
    </el-pagination>

    <!-- 添加或修改隧道检查-标线轮廓标对话框 -->
    <el-dialog :title="title" v-model="open" width="600px">
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名称" prop="projectCode">
              <el-select v-model="form.projectCode" placeholder="请选择" clearable>
                <el-option
                    v-for="item in projects"
                    :key="item.projectCode"
                    :label="item.projectName"
                    :value="item.projectCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="隧道名称" prop="tunnelCode">
              <el-select v-model="form.tunnelCode" placeholder="请选择" clearable>
                <el-option
                    v-for="item in tunnels"
                    :key="item.tunnelCode"
                    :label="item.tunnelNameAndHole"
                    :value="item.tunnelCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="病害桩号" prop="pileNumber">
              <el-input
                  v-model="form.pileNumber"
                  placeholder="请输入病害桩号"
                  class="myInput"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="病害编号" prop="defectNumber">
              <el-input
                  v-model="form.defectNumber"
                  placeholder="请输入病害编号"
                  class="myInput"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="病害位置" prop="defectLocation">
              <el-input
                  v-model="form.defectLocation"
                  placeholder="请输入病害位置"
                  class="myInput"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="病害类型" prop="diseaseType">
              <el-input
                  v-model="form.diseaseType"
                  placeholder="请输入病害类型"
                  class="myInput"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="长度(m)" prop="lengthValue">
              <el-input
                  v-model="form.lengthValue"
                  placeholder="请输入长度"
                  class="myInput"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="宽度(m)" prop="widthValue">
              <el-input
                  v-model="form.widthValue"
                  placeholder="请输入宽度"
                  class="myInput"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="面积(m²)" prop="areaValue">
              <el-input
                  v-model="form.areaValue"
                  placeholder="请输入面积"
                  class="myInput"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数量" prop="num">
              <el-input
                  v-model="form.num"
                  placeholder="请输入数量"
                  class="myInput"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状况值" prop="conditionValue">
              <el-input
                  v-model="form.conditionValue"
                  placeholder="请输入状况值"
                  class="myInput"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="重点病害" prop="isKeyDefect">
              <el-radio-group v-model="form.isKeyDefect">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-if="form.isKeyDefect === 1">
          <el-col :span="12">
            <el-form-item label="重点病害编号" prop="keyDefectNumber">
              <el-input
                  v-model="form.keyDefectNumber"
                  placeholder="请输入重点病害编号"
                  class="myInput"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维修状态" prop="maintenanceStatus">
              <el-select v-model="form.maintenanceStatus" placeholder="请选择维修状态" class="myInput">
                <el-option label="未维修" value="未维修"></el-option>
                <el-option label="维修中" value="维修中"></el-option>
                <el-option label="已维修" value="已维修"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="维修时间" prop="maintenanceTime">
              <el-date-picker
                  v-model="form.maintenanceTime"
                  type="datetime"
                  placeholder="请选择维修时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  class="myInput"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检测时间" prop="inspectionTime">
              <el-date-picker
                  v-model="form.inspectionTime"
                  type="datetime"
                  placeholder="请选择检测时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  class="myInput"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状况描述" prop="conditionDiscreption">
              <el-input
                  v-model="form.conditionDiscreption"
                  placeholder="请输入状况描述"
                  type="textarea"
                  class="myInput"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="病害图片" prop="defectImages">
          <el-upload
              class="upload-demo"
              action="/dev-api/tunnel/upload/uploadPicture"
              :on-remove="handleDefectImagesRemove"
              :on-success="defectImagesUpload"
              :show-file-list="true"
              :headers="{ Authorization: 'Bearer ' + token }"
              list-type="picture-card"
              :file-list="defectImagesLists"
              :limit="5"
              :on-exceed="handleExceed"
          >
            <el-button type="primary">上传病害图片</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" style="text-align: right;">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 批量上传 -->
    <el-dialog
        v-model="batchAddOpen"
        :title="batchAddTitle"
        width="40%"
        :destroy-on-close="true"
        @closed="closeFileUpload()"
    >
      <outlineFileUpload></outlineFileUpload>
    </el-dialog>
  </div>
</template>

<script>
import {
  listOutline,
  getOutline,
  delOutline,
  addOutline,
  updateOutline,
  exportData,
  getProjects,
  getTunnels,
} from "@/api/system/outline";
import outlineFileUpload from "../fileUpload/outlineFileUpload.vue";
import axios from "axios";
import {getToken} from "@/utils/auth";
import {tableConfigFilter} from "../constants";

export default {
  name: "Outline",
  components: {outlineFileUpload},
  data() {
    return {
      tableConfig: tableConfigFilter('outline'),
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 隧道检查-标线轮廓标表格数据
      outlineList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      batchAddTitle: "",
      batchAddOpen: false,
      token: null,
      isExport: true,
      isExportIng: false,
      fileLists: [],
      defectImagesLists: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectTunnelInspectionId: null,
        tunnelCode: null,
        tunnelName: null,
        pileNumber: null,
        defectNumber: null,
        defectLocation: null,
        diseaseType: null,
        num: null,
        lengthValue: null,
        widthValue: null,
        areaValue: null,
        conditionDiscreption: null,
        defectImages: null,
        isKeyDefect: null,
        keyDefectNumber: null,
        maintenanceStatus: null,
        maintenanceTime: null,
        inspectionTime: null,
        conditionValue: null,
        onDate: [],
        offDate: [],
      },
      projects: [],
      diseaseTypes: [
        {diseaseType: "消防设备指示标志缺失"},
        {diseaseType: "消防设备指示标志损坏"},
        {diseaseType: "线形诱导标缺失"},
        {diseaseType: "疏散指示标志缺失"},
        {diseaseType: "疏散指示标志损坏"},
        {diseaseType: "紧急停车带位置提示标志缺失"},
        {diseaseType: "紧急停车带位置提示标志损坏"},
        {diseaseType: "紧急停车带标志缺失"},
        {diseaseType: "紧急停车带标志损坏"},
        {diseaseType: "车行通道指示标志缺失"},
        {diseaseType: "车行通道指示标志损坏"},
        {diseaseType: "人行通道指示标志缺失"},
        {diseaseType: "人行通道指示标志损坏"},
        {diseaseType: "限速标志缺失"},
        {diseaseType: "限速标志损坏"},
        {diseaseType: "隧道开灯标志缺失"},
        {diseaseType: "隧道开灯标志损坏"},
        {diseaseType: "禁止超车标志缺失"},
        {diseaseType: "禁止超车标志损坏"},
        {diseaseType: "解除禁止超车标志缺失"},
        {diseaseType: "解除禁止超车标志损坏"},
        {diseaseType: "导流线缺损"},
        {diseaseType: "入口防护和过渡缺损"},
        {diseaseType: "洞内反光轮廓带缺损"},
      ],
      diseaseLocations: [
        {diseaseLocation: "左侧"},
        {diseaseLocation: "右侧"},
      ],
      tunnels: [],
      // 表单参数
      form: {
        date1: [],
        defectNumber: null,
        defectLocation: null,
        lengthValue: null,
        widthValue: null,
        areaValue: null,
        defectImages: null,
        isKeyDefect: 0,
        keyDefectNumber: null,
        maintenanceStatus: null,
        maintenanceTime: null,
        inspectionTime: null,
      },
      // 表单校验
      rules: {
        tunnelCode: [
          {required: true, message: "隧道名称不能为空", trigger: "blur"},
        ],
        projectCode: [
          {required: true, message: "项目名称不能为空", trigger: "blur"},
        ],
        pileNumber: [
          {required: true, message: "桩号不能为空", trigger: "blur"},
        ],
        defectNumber: [
          {required: true, message: "病害编号不能为空", trigger: "blur"},
        ],
        defectLocation: [
          {required: true, message: "病害位置不能为空", trigger: "blur"},
        ],
        lengthValue: [
          {pattern: /^\d+(\.\d{1,2})?$/, message: "请输入正确的数值格式", trigger: "blur"},
        ],
        widthValue: [
          {pattern: /^\d+(\.\d{1,2})?$/, message: "请输入正确的数值格式", trigger: "blur"},
        ],
        areaValue: [
          {pattern: /^\d+(\.\d{1,2})?$/, message: "请输入正确的数值格式", trigger: "blur"},
        ],
        conditionValue: [
          {pattern: /^[0-4]?$/, message: "只能输0-4的整数", trigger: "blur"},
          {required: true, message: "状况值不能为空", trigger: "blur"},
        ],
        // area :[{required: true, pattern: /^[0-9]*[1-9][0-9]*$/, message: '只能输入正整数', trigger: 'blur' }],
        conditionDiscreption: [
          {required: true, message: "状况描述不能为空", trigger: "blur"},
        ],
      },
    };
  },
  created() {
    this.getList();
    this.getProject();
    this.getTunnel();
    this.token = getToken();
  },
  methods: {
    /** 查询隧道检查-标线轮廓标列表 */
    getList(query = {}) {
      this.loading = true;
      this.reset();
      this.queryParams = {...this.queryParams, ...query};
      if (
          this.queryParams.onDate != null &&
          this.queryParams.onDate.length != 0
      ) {
        this.queryParams.createBeginTime = this.queryParams.onDate[0];
        this.queryParams.createEndTime = this.queryParams.onDate[1];
      }
      listOutline(this.queryParams).then((response) => {
        this.outlineList = response.rows;
        this.outlineList.forEach((item) => {
          Object.assign(item, {prePictureList: [item.pictureUrl]});
        });
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    closeFileUpload() {
      this.handleQuery();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectTunnelInspectionId: null,
        tunnelCode: null,
        tunnelName: null,
        pileNumber: null,
        diseaseType: null,
        num: null,
        conditionDiscreption: null,
        conditionValue: null,
        defectNumber: null,
        defectLocation: null,
        lengthValue: null,
        widthValue: null,
        areaValue: null,
        defectImages: null,
        isKeyDefect: 0,
        keyDefectNumber: null,
        maintenanceStatus: null,
        maintenanceTime: null,
        inspectionTime: null,
      };
      this.resetForm("form");
      this.fileLists = [];
      this.defectImagesLists = [];
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.createEndTime = null;
      this.queryParams.createBeginTime = null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加隧道检查-标线轮廓标";
    },
    /** 批量新增按钮操作 */
    handleBatchAdd() {
      console.log("56456465");
      this.batchAddOpen = true;
      this.batchAddTitle = "批量新增";
      console.log("111111");
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getOutline(id).then((response) => {
        this.form = response.data;
        if (response.data.defectImages != null && response.data.defectImages != '') {
          let defectImagesList = response.data.defectImages.split(',');
          this.defectImagesLists = defectImagesList.map((item) => {
            return {
              name: item,
              url: item,
            };
          });
        }
        this.open = true;
        this.title = "修改隧道检查-标线轮廓标";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.date1 != null && this.form.date1.length != 0) {
            this.form.beginTime = this.form.date1[0];
            this.form.endTime = this.form.date1[1];
          }
          this.projects.forEach((item, index, arr) => {
            if (item.projectCode == this.form.projectCode) {
              this.form.projectName = item.projectName;
            }
          });
          this.tunnels.forEach((item, index, arr) => {
            if (item.tunnelCode == this.form.tunnelCode) {
              this.form.tunnelName = item.tunnelName;
            }
          });
          if (this.form.id != null) {
            updateOutline(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addOutline(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
          .confirm('是否确认删除隧道检查-标线轮廓标编号为"' + ids + '"的数据项？')
          .then(function () {
            return delOutline(ids);
          })
          .then(() => {
            this.getList();
            this.$modal.msgSuccess("删除成功");
          })
          .catch(() => {
          });
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.outlineList.length == 0) {
        this.$alert("无导出数据", "", {
          confirmButtonText: "确定",
        });
      } else {
        //开启loading
        this.isExport = false;
        this.isExportIng = true;
        if (
            this.queryParams.onDate != null &&
            this.queryParams.onDate.length != 0
        ) {
          this.queryParams.createBeginTime = this.queryParams.onDate[0];
          this.queryParams.createEndTime = this.queryParams.onDate[1];
        }
        exportData(this.queryParams)
            .then((res) => {
              if (res.code == "500") {
                this.$modal.msgSuccess("导出失败，请联系开发人员");
                return;
              } else {
                this.$modal.msgSuccess("导出成功");
              }
              let blob = new Blob([res], {type: "application/force-download"});
              console.log(blob);
              let fileReader = new FileReader();
              fileReader.readAsDataURL(blob);
              fileReader.onload = (e) => {
                let a = document.createElement("a");
                let date = new Date();
                let year = date.getFullYear();
                let month = date.getMonth() + 1;
                let day = date.getDate();
                a.download = `导出数据${year + "_" + month + "_" + day}.xlsx`;
                a.href = e.target.result;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
              };
              this.isExport = true;
              this.isExportIng = false;
            })
            .catch(() => {
              this.isExport = true;
              this.isExportIng = false;
            });
      }
    },
    sizeChangeHandle(val) {
      this.queryParams.pageSize = val;
      this.queryParams.pageNum = 1;
      this.getList();
    },
    currentChangeHandle: function currentChangeHandle(val) {
      this.queryParams.pageNum = val;
      this.getList();
    },
    handleExceed(files, fileList) {
      this.$modal.msgWarning(`只能上传一个文件`);
    },
    getProject() {
      getProjects().then((response) => {
        this.projects = response.data;
      });
    },
    getTunnel() {
      getTunnels().then((response) => {
        this.tunnels = response.data;
        this.tunnels.forEach(function (item, index, array) {
          item.tunnelNameAndHole = item.tunnelName + "-" + item.hole;
        });
      });
    },
    //上传图片
    fileUpload: function (response, file, fileList) {
      let pictureList = [response.msg];
      this.fileLists = pictureList.map((item) => {
        return {
          name: item,
          url: item,
        };
      });
      var url = response.msg;
      this.form.pictureUrl = url;
    },
    //上传图片
    handleRemove() {
      this.form.pictureUrl = null;
    },
    //病害图片上传
    defectImagesUpload: function (response, file, fileList) {
      let pictureList = this.form.defectImages ? this.form.defectImages.split(',') : [];
      pictureList.push(response.msg);
      this.form.defectImages = pictureList.join(',');
      this.defectImagesLists = pictureList.map((item) => {
        return {
          name: item,
          url: item,
        };
      });
    },
    //病害图片移除
    handleDefectImagesRemove(file, fileList) {
      let pictureList = this.form.defectImages ? this.form.defectImages.split(',') : [];
      pictureList = pictureList.filter(item => item !== file.url);
      this.form.defectImages = pictureList.length > 0 ? pictureList.join(',') : null;
      this.defectImagesLists = pictureList.map((item) => {
        return {
          name: item,
          url: item,
        };
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.myInput {
  width: 215px;
}

.batch-container {
  .download {
    color: #409eff;
    cursor: pointer;
  }

  .desc-section {
    display: flex;
    flex-direction: column;
  }

  .img-list {
    display: flex;

    .img-item {
      width: 50px;
      height: 50px;
      margin-left: 5px;

      &:first-child {
        margin-left: 0;
      }
    }
  }
}

.app-container {
  height: 200%;
  padding: 10px 20px;
  width: 100%;

  .search-section {
    padding: 20px 30px;
  }

  .page-section {
    padding-top: 20px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}

.myInput {
  width: 215px;
}
</style>