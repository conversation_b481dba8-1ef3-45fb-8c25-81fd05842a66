<template>

    <el-dialog v-model="file.batchAddOpen" :title="file.batchAddTitle" width="40%" :destroyOnClose="true" @closed="closeFileUpload()">
        <div class="upload-file">
            <el-form label-width="auto" @submit.native.prevent>
                <el-form-item label="">
                    <el-button @click="frameWorkDownload" type="primary">下载模板</el-button>
                </el-form-item>
                <el-form-item label="选择文件">
                    <el-upload
                            ref="upload"
                            class="upload-demo"
                            :action=preUrl
                            accept="xlsx"
                            :limit="1"
                            :before-upload="beforeAvatarUpload"
                            :headers="{'Authorization': 'Bearer ' + token}"
                            :on-success="fileUpload"
                            :on-preview="handlePreview"
                            :on-remove="handleRemove"
                            :on-exceed="handleExceed"
                            :auto-upload="false">
                        <el-button type="primary">选取数据文件</el-button>
                        <el-button style="margin-left: 10px;"  type="success" @click.stop="submitUpload">确定上传</el-button>
                        <div slot="tip" style="color:red" class="el-upload__tip">1、只能上传xlsx文件，且不超过1MB</div>
                    </el-upload>
                </el-form-item>
                <el-form-item v-if="showResultMsg" label="导入结果">
                    <span>{{ resultMsg }}</span> <br/>
                    <span>{{ resultErrorMsg }}</span>
                    <a v-if="this.showErrorUrl" :href="errorFileUrl">查看失败原因</a>
                </el-form-item>
            </el-form>
        </div>
    </el-dialog>
</template>

<script>
import { getToken } from "@/utils/auth";
import {reactive} from "vue";
import axios from "axios";
import { blobValidate } from '@/utils/ruoyi'
import {ElMessage} from "element-plus";
import errorCode from '@/utils/errorCode'
const baseURL = import.meta.env.VITE_APP_BASE_API

export default {
    name: "excelUpload",
    //props的基本用法是父组件给子组件传输数据和验证
    props: {
        //父组件数据
        file: {
            type: Object,
            required: true,
            default: () => ({
                batchAddOpen:false,
                batchAddTitle:'批量新增',
                templateUrl:'',
                uploadUrl:'',
                excelName:''
            })
        },
    },
    data() {
        return {
            proxy:null,
            visible: false,
            preUrl: "",
            token: null,
            dataForm: {
                id: 0,
                nameCn: '',
                brandType: '',
                nameEn: '',
                alias: '',
                logoUrl: '',
                brandSource: '',
                description: ''
            },
            showResultMsg: false,
            resultMsg: "",
            showErrorUrl: false,
            errorFileUrl: "",
            //文件上传相关
            fileLists: [],
            "fileList": {
                "url": ''
            },
            dialogImageUrl: '',
            dialogVisible: false,
            fileUploadHeaders: {
                "Content-Type": "multipart/form-data"
            },
        }
    },
    methods: {
     closeDialog() {
        this.fileLists = [];
    },
     submitUpload() {
         this.$refs.upload.submit();
    },
     handleRemove(file, fileList) {
        this.showResultMsg = false;
        this.resultMsg = "";
    },
     handlePreview(file) {
        console.log(file);
    },
     beforeAvatarUpload(file) {
        this.token = getToken();
        this.preUrl = baseURL + this.file.uploadUrl;
        const isExcel = (file.name.indexOf(".xlsx") > 0 && file.type.indexOf("vnd") > 0);
        const isLt2M = file.size / 1024 / 1024 <= 1;

        if (!isExcel) {
            this.$modal.error('上传文件只能是 xlsx 格式!');
        }
        if (!isLt2M) {
            this.$modal.error('上传文件大小不能超过 1MB!');
        }
        return isExcel && isLt2M;
    },
     handleExceed(files, fileList) {
        this.$modal.warning(`只能上传一个文件`);
    },
     fileUpload(response, file) {
        if (response.code == '200') {
            if (response.data.status == 1) {
                this.showResultMsg = true;
                this.resultMsg = "导入失败," + response.data.msg;
            } else {
                this.showResultMsg = true;
                this.resultMsg = "导入成功";
                this.resultErrorMsg = "";
                this.$emit('refreshDataList');
            }
        } else {
            console.log("false")
            this.showResultMsg = true;
            this.resultMsg = "导入失败，请联系开发人员";
        }
    },
    frameWorkDownload() {
         let _this=this;
        let consturl = baseURL +'/excel/template/download?filePath='+this.file.templateUrl;   //下载地址url
        var xhr = new XMLHttpRequest();
        xhr.open("get", consturl, true); // get、post都可
        xhr.responseType = "blob";
        xhr.setRequestHeader("Authorization", 'Bearer ' + getToken()); //加请求头
        xhr.onload = function (e) {
            if (xhr.status == 200) {
                let blob = new Blob([this.response], {type: 'application/json;charset=utf8'});
                var a = document.createElement("a")
                var url = window.URL.createObjectURL(blob)
                a.href = url
                var farmname = _this.file.excelName;
                a.download = farmname  // 文件名
            }
            a.click()
            window.URL.revokeObjectURL(url)
        }
        xhr.send();
    },
     init() {
        let _this = this;
        _this.visible = true;
        _this.showResultMsg = false;
        _this.showErrorUrl = false;
        _this.$refs.upload.clearFiles();
    },
        closeFileUpload(){
            this.file.batchAddOpen = false;
        },
},
    created(){
    },
}


</script>

<style scoped lang="scss">
.upload-file-uploader {
    margin-bottom: 5px;
}

.upload-file-list .el-upload-list__item {
    border: 1px solid #e4e7ed;
    line-height: 2;
    margin-bottom: 10px;
    position: relative;
}

.upload-file-list .ele-upload-list__item-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: inherit;
}

.ele-upload-list__item-content-action .el-link {
    margin-right: 10px;
}
</style>
