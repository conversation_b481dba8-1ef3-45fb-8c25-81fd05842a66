<template>
  <div class="upload-file">
    <el-form  @submit.native.prevent label-width="auto">
      <el-form-item label="" >
        <el-button @click="frameWorkDownload"  type="primary">下载模板</el-button>
      </el-form-item>
      <el-form-item label="选择文件">
        <el-upload
            class="upload-demo"
            ref="upload"
            :action=data.preUrl
            accept="xlsx"
            multiple
            :limit="1"
            :before-upload="beforeAvatarUpload"
            :headers="{'Authorization': 'Bearer ' + data.token}"
            :on-success="fileUpload"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :on-exceed="handleExceed"
            :auto-upload="false">
          <el-button slot="trigger"  type="primary">选取数据文件</el-button>
          <el-button style="margin-left: 10px;"  type="success" @click.stop="submitUpload">确定上传</el-button>
          <div slot="tip" style="color:red" class="el-upload__tip">1、只能上传xlsx文件，且不超过1MB </div>
        </el-upload>
      </el-form-item>
      <el-form-item v-if="data.showResultMsg" label="导入结果" >
        <span>{{ data.resultMsg }}</span> <br/>
        <span>{{ data.resultErrorMsg }}</span>
        <a v-if="data.showErrorUrl" :href="data.errorFileUrl">查看失败原因</a>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { getToken } from "@/utils/auth";
import {reactive} from "vue";
import axios from "axios";
import { blobValidate } from '@/utils/ruoyi'
import {ElMessage} from "element-plus";
import errorCode from '@/utils/errorCode'
const { proxy } = getCurrentInstance();


const data = reactive({
  visible: false,
  preUrl:"",
  token:null,
  dataForm: {
    id: 0,
    nameCn: '',
    brandType:'',
    nameEn: '',
    alias: '',
    logoUrl: '',
    brandSource: '',
    description: ''
  },
  showResultMsg:false,
  resultMsg:"",
  showErrorUrl:false,
  errorFileUrl:"",
  //文件上传相关
  fileLists: [],
  "fileList": {
    "url": ''
  },
  dialogImageUrl: '',
  dialogVisible: false,
  fileUploadHeaders:{
    "Content-Type": "multipart/form-data"
  },
})

function closeDialog() {
  this.fileLists = [];
}
function submitUpload() {
  proxy.$refs.upload.submit();
}
function handleRemove(file, fileList) {
  data.showResultMsg=false;
  data.resultMsg="";
}
function handlePreview(file) {
  console.log(file);
}
function beforeAvatarUpload(file) {
  data.token=getToken();
  data.preUrl=import.meta.env.VITE_APP_BASE_API+"/tunnel/lining/batchAdd";
  const isExcel = (file.name.indexOf(".xlsx")>0&&file.type.indexOf("vnd")>0);
  const isLt2M = file.size / 1024 / 1024 <= 1;

  if (!isExcel) {
    proxy.$modal.error('上传文件只能是 xlsx 格式!');
  }
  if (!isLt2M) {
    proxy.$modal.error('上传文件大小不能超过 1MB!');
  }
  return isExcel && isLt2M;
}
function handleExceed(files, fileList) {
  proxy.$modal.warning(`只能上传一个文件`);
}
function fileUpload(response, file){
  if(response.code=='200'){
    if(response.data.status==1){
      data.showResultMsg=true;
      data.resultMsg="导入失败,"+response.data.msg;
    }else{
      data.showResultMsg=true;
      data.resultMsg="导入成功";
      data.resultErrorMsg="";
    }
  }else{
    console.log("false")
    data.showResultMsg=true;
    data.resultMsg="导入失败，请联系开发人员";
  }
}
function frameWorkDownload(){
  let consturl=import.meta.env.VITE_APP_BASE_API+"/excel/template/download?filePath=tunnel_inspection_lining_import_template.xlsx"   //下载地址url
  var xhr = new XMLHttpRequest();
  xhr.open("get", consturl, true); // get、post都可
  xhr.responseType = "blob";
  xhr.setRequestHeader("Authorization",'Bearer ' + getToken()); //加请求头
  xhr.onload = function() {
    if(xhr.status==200){
      let blob = new Blob([this.response], { type: 'application/json;charset=utf8' });
      var a = document.createElement("a")
      var url = window.URL.createObjectURL(blob)
      a.href = url
      var farmname='衬砌数据上传模板.xlsx'
      a.download = farmname  // 文件名
    }
    a.click()
    window.URL.revokeObjectURL(url)
  }
  xhr.send();
}
function init() {
  let _this=this;
  _this.visible = true;
  _this.showResultMsg=false;
  _this.showErrorUrl=false;
  _this.$refs.upload.clearFiles();
}
</script>

<style scoped lang="scss">
.upload-file-uploader {
  margin-bottom: 5px;
}
.upload-file-list .el-upload-list__item {
  border: 1px solid #e4e7ed;
  line-height: 2;
  margin-bottom: 10px;
  position: relative;
}
.upload-file-list .ele-upload-list__item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit;
}
.ele-upload-list__item-content-action .el-link {
  margin-right: 10px;
}
</style>
