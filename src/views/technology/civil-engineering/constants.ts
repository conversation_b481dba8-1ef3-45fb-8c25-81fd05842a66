export const tabsConfig = [
    {
        label: '洞口',
        template: 'opening'
    },
    {
        label: '洞门',
        template: 'portal'
    },
    {
        label: '衬砌',
        template: 'lining'
    },
    {
        label: '路面',
        template: 'road'
    },
    {
        label: '检修道',
        template: 'manhole'
    },
    {
        label: '排水系统',
        template: 'drainage'
    },
    {
        label: '吊顶预埋件',
        template: 'ceiling'
    },
    {
        label: '内装饰',
        template: 'interior'
    },
    {
        label: '标线轮廓标',
        template: 'outline'
    }
]
// 病害位置
export const diseaseLocations = [
    { diseaseLocation: '主洞拱顶' },
    { diseaseLocation: '主洞左拱腰' },
    { diseaseLocation: '主洞右拱腰' },
    { diseaseLocation: '主洞左边墙' },
    { diseaseLocation: '主洞右边墙' }
]
// 病害类型
export const diseaseTypes = [
    {
        label: '裂缝',
        children: [{ label: '纵向裂缝' },
        { label: '环向裂缝' },
        { label: '斜向裂缝' },
        { label: '网状裂缝' }]
    },
    {
        label: '表面损坏',
        children: [{ label: '蜂窝麻面' },
        { label: '表层起层' },
        { label: '剥落' },
        { label: '破损露筋' }]
    },
    {
        label: '积水渗水涌水',
        children: [{ label: '衬砌渗漏水' }]
    },
    {
        label: '裂缝',
        children: [{ label: '施工缝开裂' }]
    },
    {
        label: '积水渗水涌水',
        children: [{ label: '施工缝渗漏水' }]
    }
]
// 
export const roadTypes = [
    { roadType: '沥青路面' },
    { roadType: '水泥路面' }
]
export const diseaseTypesForLiQing = [
    {
        label: "裂缝",
        children: [
            { label: "横向裂缝" },
            { label: "纵向裂缝" },
            { label: "龟裂" },
            { label: "块裂" },
        ],
    },
    {
        label: "路面变形",
        children: [
            { label: "车辙" },
            { label: "沉陷" },
            { label: "波浪拥包" },
        ],
    },
    { label: "拱起", children: [{ label: "拱起" }] },
    {
        label: "表面损坏",
        children: [{ label: "坑槽" }, { label: "松散" }, { label: "泛油" }],
    },
    {
        label: "积水渗水涌水",
        children: [{ label: "渗水积水" }, { label: "结冰" }],
    },
];
export const diseaseTypesForShuiNi = [
    { label: "裂缝", children: [{ label: "裂缝" }] },
    {
        label: "表面损坏",
        children: [{ label: "露骨" }, { label: "坑洞" }, { label: "剥落" }],
    },
    { label: "错台断缝", children: [{ label: "接缝错台" }] },
    {
        label: "缺损",
        children: [{ label: "板角破损" }, { label: "破碎板" }],
    },
]

export const tableConfig = [
    {
        label: "项目名称",
        prop: 'projectName',
        width: '150',
        type:['all']
    },
    // {
    //     label: "所属路段",
    //     prop: 'affiliatedRoadSection',
    //     type:['all']
    // },
    {
        label: "隧道名称",
        prop: 'tunnelNameAndHole',
        type:['all']
    },
    // {
    //     label: "检测时间",
    //     prop: 'inspectionTime',
    //     type:['all']
    // },
    {
        label: "桩号",
        prop: 'pileNumber',
        type:['all']
    },
    {
        label: "病害位置",
        prop: 'diseaseLocation',
         type:['opening','portal','manhole','drainage','ceiling','interior','outline']
    },
    {
        label: "检测内容",
        prop: 'inspectioContent',
        type:['opening','portal']

    },
    {
        label: "状况描述",
        prop: 'judgmentDescription',
        type:['opening','portal']
    },
    {
        label: "路面类型",
        prop: 'roadType',
        type:['road']
    },
    {
        label: "病害类型",
        prop: 'diseaseType',
        type:['lining','road','manhole','drainage','ceiling','interior','outline']
    },
    {
        label: "长度(m)",
        prop: 'crackLength',
        width: '100',
        type:['lining','road']
    },
    {
        label: "宽度(mm)",
        prop: 'crackDepth',
        width: '100',
        type:['lining','road']
    },
    {
        label: "面积(m²)",
        prop: 'area',
        width: '100',
        type:['lining','road','manhole','drainage','ceiling','interior','outline']
    },
    {
        label: "状况描述",
        prop: 'conditionDiscreption',
        width: '180',
        type:['drainage','ceiling']
    },
    {
        label: "状况值",
        prop: 'conditionValue',
        width: '90',
        type:['all']
    },
    {
        label: "病害照片",
        prop: 'pictureUrl',
        type:['all']
    },
]
export const tableConfigFilter = (name: string) => {  
    return tableConfig.filter(item => {
      return item.type.includes(name) || item.type.includes('all')
    })
};