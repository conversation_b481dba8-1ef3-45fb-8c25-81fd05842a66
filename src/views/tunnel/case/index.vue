<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm"  :inline="true" v-show="showSearch" label-width="auto">
      <el-form-item label="隶属单位" prop="affiliatedUnit">
        <el-input
          v-model="queryParams.affiliatedUnit"
          placeholder="请输入隶属单位"
          clearable
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectId">
        <el-select v-model="queryParams.projectId" placeholder="请选择" clearable filterable >
          <el-option v-for="item in projectList" :key="item.value" :label="item.label"
                     :value="item.value" />
        </el-select>
      </el-form-item>
       <el-form-item label="所属路段" prop="affiliatedRoadSection">
        <el-input
          v-model="queryParams.affiliatedRoadSection"
          placeholder="请输入所属路段"
          clearable
        />
      </el-form-item>
      <el-form-item label="隧道名称" prop="tunnelId">
        <el-select v-model="queryParams.tunnelId" placeholder="请选择" clearable filterable>
          <el-option v-for="item in tunnelList" :key="item.value" :label="item.label"
                     :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="隧道幅别" prop="tunnelName">
        <el-input
          v-model="queryParams.tunnelName"
          placeholder="请输入隧道幅别"
          clearable
        />
      </el-form-item>
      <!-- <el-form-item label="三维可视化隧道编码" prop="threeViewTunnelCode" label-width="168px">
        <el-input
          v-model="queryParams.threeViewTunnelCode"
          placeholder="请输入三维可视化隧道编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in statusList"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <!--<el-form-item label="执行开始时间" prop="startTime" label-width="200">-->
        <!--<el-date-picker clearable-->
          <!--v-model="queryParams.startTime"-->
          <!--type="date"-->
          <!--value-format="yyyy-MM-dd"-->
          <!--placeholder="请选择执行开始时间">-->
        <!--</el-date-picker>-->
      <!--</el-form-item>-->
      <!--<el-form-item label="执行截至时间" prop="endTime" label-width="200">-->
        <!--<el-date-picker clearable-->
          <!--v-model="queryParams.endTime"-->
          <!--type="date"-->
          <!--value-format="yyyy-MM-dd"-->
          <!--placeholder="请选择执行截至时间">-->
        <!--</el-date-picker>-->
      <!--</el-form-item>-->
      <!--<el-form-item label="创建人名称" prop="creatorName" label-width="200">-->
        <!--<el-input-->
          <!--v-model="queryParams.creatorName"-->
          <!--placeholder="请输入创建人名称"-->
          <!--clearable-->
          <!--@keyup.enter.native="handleQuery"-->
        <!--/>-->
      <!--</el-form-item>-->
      <!--<el-form-item label="更新人名称" prop="modifierName" label-width="200">-->
        <!--<el-input-->
          <!--v-model="queryParams.modifierName"-->
          <!--placeholder="请输入更新人名称"-->
          <!--clearable-->
          <!--@keyup.enter.native="handleQuery"-->
        <!--/>-->
      <!--</el-form-item>-->
      <!-- <el-form-item label="创建时间范围" prop="onDate" label-width="auto" >
        <el-date-picker
                v-model="queryParams.onDate"
                type="daterange"
                range-separator="~"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                start-placeholder="开始日期"
                end-placeholder="结束日期" style="width: 270px">
        </el-date-picker>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary"  size="mini" @click="handleQuery">搜索</el-button>
        <el-button  size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <!--<div id="container" style="height: 800px;width: 100%"></div>-->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          @click="handleAdd"
          v-hasPermi="['tunnel:case:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['tunnel:case:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['tunnel:case:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          size="mini"
          @click="handleExport"
          v-hasPermi="['tunnel:case:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
      <el-button size="mini"
                 type="primary"
                 :disabled="multiple"
                 @click="handleSync"
                 v-hasPermi="['tunnel:case:sync']"
                 :loading="syncLoading" >数据同步</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="caseList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号" align="center" width="50" />
      <el-table-column
        label="隶属单位"
        align="center"
        prop="affiliatedUnit"
        width="120"
        show-tooltip-when-overflow="true"
      />
      <el-table-column label="项目名称" align="center"  prop="projectName" min-width="150"/>
      <!-- <el-table-column label="项目编码" align="center" prop="projectCode" width="150"/> -->
      <el-table-column label="隧道名称" align="center" prop="tunnelName" min-width="120"/>
      <el-table-column label="隧道编码" align="center" prop="tunnelCode" min-width="120"/>
      <!-- <el-table-column label="洞" align="center" prop="hole" width="150"/> -->
      <el-table-column label="三维可视化隧道编码" align="center" prop="threeViewTunnelCode" width="150"/>
      <el-table-column label="状态" align="center" prop="status" :formatter="formatStatus"/>
      <el-table-column label="执行开始时间" align="center" prop="startTime" width="180">
      </el-table-column>
      <el-table-column label="执行截至时间" align="center" prop="endTime" width="180">
      </el-table-column>
      <!-- <el-table-column label="失败原因" align="center" prop="failMsg" width="150"/>
      <el-table-column label="创建人名称" align="center" prop="creatorName" />
      <el-table-column label="更新人名称" align="center" prop="modifierName" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180"/>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180"/> -->
      <el-table-column label="操作" fixed="right" width="200" align="center" class-name="small-padding fixed-width">
        <template v-slot:default="scope">
          <el-button
            size="mini"
            type="success"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['tunnel:case:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(scope.row)"
            v-hasPermi="['tunnel:case:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
<!--    <pagination-->
<!--      v-show="total>0"-->
<!--      :total="total"-->
<!--      :page.sync="queryParams.pageNum"-->
<!--      :limit.sync="queryParams.pageSize"-->
<!--      @pagination="getList"-->
<!--    />-->

    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="queryParams.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="queryParams.pageSize"
        :total="total"
        layout="->,total, sizes, prev, pager, next, jumper"
        style="margin-top:10px;text-align: center"
    />

    <!-- 添加或修改病害数据同步对话框 -->
    <el-dialog :title="title" v-model="open" width="600px">
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="项目名称/编码" prop="projectId">
          <el-select v-model="form.projectId" placeholder="请选择" clearable filterable @change="getTunnelList">
            <el-option v-for="item in projectList" :key="item.value" :label="item.label"
                       :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="隧道名称/编码" prop="tunnelId">
          <el-select v-model="form.tunnelId" placeholder="请选择" clearable filterable>
            <el-option v-for="item in tunnelList" :key="item.value" :label="item.label"
                       :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="隧道洞别" prop="hole">
          <el-select v-model="form.hole" placeholder="请选择隧道洞别" clearable>
            <el-option v-for="item in holeList" :key="item.value" :label="item.label"
                       :value="item.value" />
          </el-select>
        </el-form-item>
        <!--<el-form-item label="状态" prop="status">-->
          <!--<el-select v-model="form.status" placeholder="请选择状态">-->
            <!--<el-option-->
              <!--v-for="dict in dict.type.sys_job_status"-->
              <!--:key="dict.value"-->
              <!--:label="dict.label"-->
<!--:value="parseInt(dict.value)"-->
            <!--&gt;</el-option>-->
          <!--</el-select>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="执行开始时间" prop="startTime">-->
          <!--<el-date-picker clearable-->
            <!--v-model="form.startTime"-->
            <!--type="date"-->
            <!--value-format="yyyy-MM-dd"-->
            <!--placeholder="请选择执行开始时间">-->
          <!--</el-date-picker>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="执行截至时间" prop="endTime">-->
          <!--<el-date-picker clearable-->
            <!--v-model="form.endTime"-->
            <!--type="date"-->
            <!--value-format="yyyy-MM-dd"-->
            <!--placeholder="请选择执行截至时间">-->
          <!--</el-date-picker>-->
        <!--</el-form-item>-->
        <!--<el-form-item label="失败原因" prop="failMsg">-->
          <!--<el-input v-model="form.failMsg" type="textarea" placeholder="请输入内容" />-->
        <!--</el-form-item>-->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCase, getCase, delCase, addCase, updateCase,sync } from "@/api/tunnel/case";
import { listProjectAll} from "@/api/system/project";
import { listTunnelAll} from "@/api/system/tunnlel";
export default {
  name: "Case",
  dicts: ['sys_job_status'],
  data() {
    return {
      holeList: [
          {
              label: '左洞',
              value: '左洞'
          }, {
              label: '右洞',
              value: '右洞'
          }
          , {
              label: '单洞',
              value: '单洞'
          }
      ],
      statusList: [
          {value: 0, label: '未执行'},
          {value: 1, label: '执行中'},
          {value: 2, label: '已完成'},
          {value: 3, label: '执行失败'}
      ],
      syncLoading:false,
      projectList:[],
      tunnelList:[],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 病害数据同步表格数据
      caseList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: null,
        tunnelId: null,
        hole: null,
        threeViewTunnelCode: null,
        status: null,
        startTime: null,
        endTime: null,
        failMsg: null,
        creator: null,
        creatorName: null,
        modifier: null,
        modifierName: null,
        createTime: null,
        updateTime: null,
        tenantId: null,
        appId: null,
        merchantId: null,
        isAvailable: null,
        isDeleted: null,
        onDate:[],
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        projectId: [
          { required: true, message: "项目ID不能为空", trigger: "blur" }
        ],
        tunnelId: [
          { required: true, message: "隧道ID不能为空", trigger: "blur" }
        ],
        hole: [
          { required: true, message: "洞：不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ],
        isAvailable: [
          { required: true, message: "是否可用：0-否，1-是不能为空", trigger: "blur" }
        ],
        isDeleted: [
          { required: true, message: "是否逻辑删除：0-否，1-是不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
      // this.$nextTick(()=>{
      //     this.initMap();
      // })
    this.getList();
    this.getTunnelList();
    this.getProjectList();

  },
  methods: {
    currentChangeHandle: function currentChangeHandle(val) {
      this.queryParams.pageNum = val;
      this.getList();
    },
    sizeChangeHandle(val) {
      this.queryParams.pageSize = val;
      this.queryParams.pageNum = 1;
      this.getList();
    },
      formatStatus(row, column, cellValue) {
          var _this = this;
          var columnValue = "";
          Object.keys(_this.statusList).forEach(function (key) {
              if (_this.statusList[key].value == row.status) {
                  columnValue = _this.statusList[key].label
              }
          });
          return columnValue
      },
      getProjectList() {
          let _this=this;
          listProjectAll().then(response => {
              response.forEach(function (e) {
                  let arr1 = {};
                  arr1.value = e.id;
                  arr1.label = e.projectName + "/" + e.projectCode;
                  _this.projectList.push(arr1);
              });
          });
      },
      getTunnelList() {
          let _this=this;
          let data={};
          _this.tunnelList=[];
          if(null != _this.form.projectId && '' != _this.form.projectId){
              data.projectId=_this.form.projectId;
          }
          listTunnelAll(data).then(response => {
              response.forEach(function (e) {
                  let arr1 = {};
                  arr1.value = e.id;
                  arr1.label = e.tunnelName + "/" + e.tunnelCode;
                  _this.tunnelList.push(arr1);
              });
          });
      },
    /** 查询病害数据同步列表 */
    getList() {
      this.loading = true;
        if(this.queryParams.onDate!=null&&this.queryParams.onDate.length!=0){
            this.queryParams.createBeginTime=this.queryParams.onDate[0];
            this.queryParams.createEndTime=this.queryParams.onDate[1];
        }
      listCase(this.queryParams).then(response => {
        this.caseList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectId: null,
        tunnelId: null,
        hole: null,
        threeViewTunnelCode: null,
        status: null,
        startTime: null,
        endTime: null,
        failMsg: null,
        creator: null,
        creatorName: null,
        modifier: null,
        modifierName: null,
        createTime: null,
        updateTime: null,
        tenantId: null,
        appId: null,
        merchantId: null,
        isAvailable: null,
        isDeleted: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
        this.queryParams.projectId=null;
        this.queryParams.tunnelId=null;
        this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加病害数据同步";
      this.getTunnelList();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getCase(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改病害数据同步";
        this.getTunnelList();
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateCase(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
              this.form;
            addCase(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除病害数据同步编号为"' + ids + '"的数据项？').then(function() {
        return delCase(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 同步按钮操作 */
    handleSync(row) {
        const ids = row.id || this.ids;
        let _this=this;
        this.$modal.confirm('是否确认同步病害数据同步编号为"' + ids + '"的数据项？').then(function() {
            _this.syncLoading=true;
            return sync(ids);
        }).then(() => {
            this.getList();
            this.$modal.msgSuccess("同步成功");
            _this.syncLoading=false;
            this.getList();
        }).catch(() => {
            _this.syncLoading=false;
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('tunnel/case/export', {
        ...this.queryParams
      }, `case_${new Date().getTime()}.xlsx`)
    }
  }
};

</script>
<style lang="scss" scoped>

  .myInput {width: 215px}
  /* 必须设定高度 */
  .bm-view{
    width: 100%;
    height: 300px;
  }
  :deep(.anchorBL){
    display: none;
  }
</style>
