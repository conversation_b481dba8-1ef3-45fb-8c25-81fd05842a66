<template>
  <div class="card">
    <div class="card-header">
      {{ name }}
    </div>
    <div class="card-body">
      <slot />
    </div>
  </div>
</template>
<script lang="ts" setup>
const props = defineProps({
  name: {
    type: String,
    default: "NAME",
  },
});
</script>
<style lang="scss" scoped>
.card {
  display: flex;
  flex-direction: column;
  min-height: 12vh;
  margin-bottom: 4vh;
  .card-header {
    height: 32px;
    width: 100%;
    background-image: url("@/assets/home/<USER>");
    background-size: cover;
    box-sizing: border-box;
    padding-left: 60px;
    line-height: 32px;
    font-size: 16px;
    letter-spacing: 2px;
    color: #fff;
  }
  .card-body {
    flex: 1;
    background-color: rgba($color: #33313a, $alpha: 0.1);
    padding: 16px;
  }
}
</style>