<template>
  <card-view :name="'管理中心隧道统计'">
    <div class="demo-progress">
      <div
        class="demo-progress-view"
        v-for="(item, index) in configData"
        :key="item.manageName"
      >
        <div class="demo-progress-title">
          <div>
            <span class="demo-progress-index">{{ index + 1 }}</span>
            <span :class="['demo-progress-name', colorFilter(item)]">{{
              item.manageName
            }}</span>
          </div>
          <div>
            <span :class="['demo-progress-name', colorFilter(item)]">{{
              `${item.count}`
            }}</span>
            <span class="demo-progress-index">{{ "座" }}</span>
          </div>
        </div>
        <div class="demo-progress-content">
          <el-progress
            :percentage="item.count"
            :show-text="false"
            :color="customColors"
          />
        </div>
      </div>
    </div>
  </card-view>
</template>
<script lang="ts" setup>
import {
  ref,
  computed,
  defineAsyncComponent,
} from "vue";
const cardView = defineAsyncComponent(() => import("../card/index.vue"));
const props = defineProps({
  manageTunnelDataList: {
    type: Array,
    default: [],
  },
});
const configData = computed(() => {
  return props.manageTunnelDataList
})
const percentage = ref(20);
const customColor = ref("#409eff");

const customColors = [
  { color: "#f56c6c", percentage: 20 },
  { color: "#e6a23c", percentage: 40 },
  { color: "#5cb87a", percentage: 60 },
  { color: "#1989fa", percentage: 80 },
  { color: "#6f7ad3", percentage: 100 },
];

const colorFilter = () => (row: any) => {
  let color = "";
  if (row.count < 20) {
    color = "percentage20";
  } else if (row.count >= 20 && row.count < 40) {
    color = "percentage40";
  } else if (row.count >= 40 && row.count < 60) {
    color = "percentage60";
  } else if (row.count >= 60 && row.count < 80) {
    color = "percentage80";
  } else if (row.count >= 80 && row.count < 100) {
    color = "percentage100";
  }
  console.log(color);
  return color;
};

const customColorMethod = (percentage: number) => {
  if (percentage < 30) {
    return "#909399";
  }
  if (percentage < 70) {
    return "#e6a23c";
  }
  return "#67c23a";
};
const increase = () => {
  percentage.value += 10;
  if (percentage.value > 100) {
    percentage.value = 100;
  }
};
const decrease = () => {
  percentage.value -= 10;
  if (percentage.value < 0) {
    percentage.value = 0;
  }
};
</script>
<style lang="scss" scoped>
.demo-progress-view {
  height: 4.2vh;
  padding: 0 10px;
  .demo-progress-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 36px;
    .demo-progress-name {
      width: 80px;
      font-size: 14px;
      // color: var(--el-text-color-regular);
      // color: rgba(255, 209, 92, 1);
    }
  }
}
.percentage20 {
  color: #f56c6c;
}
.percentage40 {
  color: #e6a23c;
}
.percentage60 {
  color: #5cb87a;
}
.percentage80 {
  color: #5cb87a;
}
.percentage100 {
  color: #1989fa;
}
.percentage100-text {
  color: #6f7ad3;
}
.demo-progress-index {
  padding: 0 10px;
}
:deep(.el-progress__text) {
  color: #fff;
}
</style>