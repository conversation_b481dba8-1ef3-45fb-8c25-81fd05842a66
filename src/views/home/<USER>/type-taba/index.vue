<template>
  <div class="type-tabs">
    <span
      v-for="item in typeTabs"
      :key="item.value"
      :class="['type-tab', activeTab === item.value && 'activeTab']"
      @click="handeActiveTab(item)"
      >{{ item.name }}</span
    >
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
const typeTabs = ref([
  {
    name: "土建",
    value: "1",
  },
  {
    name: "其他工程",
    value: "2",
  },
]);
const activeTab = ref("1");
const handeActiveTab = (row: any) => {
  activeTab.value = row.value;
  emit("change", row.value);
};
const emit = defineEmits(["change"]);
</script>
<style lang="scss" scoped>
.type-tabs {
//   width: 140px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: end;
//   padding: 0 20px;

  color: #fff;
  .type-tab {
    padding: 0 10px;
    // height: 40px;
    line-height: 26px;
    border: 1px solid rgba(34, 95, 146, 1);
    cursor: pointer;
  }
  .activeTab {
    background: rgba(34, 95, 146, 1);
  }
}
</style>