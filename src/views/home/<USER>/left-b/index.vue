<template>
  <card-view :name="'隧道类型统计'">
    <div class="demo-progress">
      <div
        class="demo-progress-view"
        v-for="item in configData"
        :key="item.typeName"
      >
        <span class="demo-progress-name">{{ item.typeName }}</span>
        <div class="demo-progress-content">
          <el-progress :percentage="item.proportion" :color="customColors" />
        </div>
        <span class="demo-progress-name">{{ `${item.count}座` }}</span>
      </div>
    </div>
  </card-view>
</template>
<script lang="ts" setup>
import {
  ref,
  defineAsyncComponent,
  computed
} from "vue";
const cardView = defineAsyncComponent(() => import("../card/index.vue"));
import { Minus, Plus } from "@element-plus/icons-vue";
const props = defineProps({
  tunnelTypeList: {
    type: Array,
    default: [],
  }
});
const configData = computed(() => {
  return props.tunnelTypeList
}) 
// ref([
//   {
//     name: "短隧道",
//     value: "600",
//     percentage: 60,
//   },
//   {
//     name: "中隧道",
//     value: "200",
//     percentage: 20,
//   },
//   {
//     name: "长隧道",
//     value: "150",
//     percentage: 15,
//   },
//   {
//     name: "特长隧道",
//     value: "50",
//     percentage: 5,
//   },
// ]);
const percentage = ref(20);
const customColor = ref("#409eff");

const customColors = [
  { color: "#f56c6c", percentage: 20 },
  { color: "#e6a23c", percentage: 40 },
  { color: "#5cb87a", percentage: 60 },
  { color: "#1989fa", percentage: 80 },
  { color: "#6f7ad3", percentage: 100 },
];

const customColorMethod = (percentage: number) => {
  if (percentage < 30) {
    return "#909399";
  }
  if (percentage < 70) {
    return "#e6a23c";
  }
  return "#67c23a";
};
const increase = () => {
  percentage.value += 10;
  if (percentage.value > 100) {
    percentage.value = 100;
  }
};
const decrease = () => {
  percentage.value -= 10;
  if (percentage.value < 0) {
    percentage.value = 0;
  }
};
</script>
<style lang="scss" scoped>
.demo-progress-view {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 4vh;
  padding: 0 10px;
  .demo-progress-content {
    flex: 1;
  }
  .demo-progress-name {
    width: 80px;
    font-size: 14px;
    // color: var(--el-text-color-regular);
  }
}
:deep(.el-progress__text) {
  color: #fff;
}
</style>