<template>
  <card-view :name="'隧道统计'">
    <div class="card-left-a">
      <div
        class="card-view-content"
        v-for="item in configData"
        :key="item.name"
      >
        <p class="card-view-content-title">{{ item.name }}</p>
        <p class="card-view-content-value">{{ item.value }}</p>
      </div>
    </div>
  </card-view>
</template>
<script lang="ts" setup>
import { ref, defineAsyncComponent, computed } from "vue";
const cardView = defineAsyncComponent(() => import("../card/index.vue"));
const props = defineProps({
  totalMiles: {
    type: Number,
    default: 0,
  },
  totalTunnel: {
    type: Number,
    default: 0,
  },
});
const configData = computed(() => {
  return [
    {
      name: "隧道统计（座）",
      value: props.totalTunnel,
    },
    {
      name: "总里程（km）",
      value: props.totalMiles,
    },
  ];
});
</script>
<style lang="scss" scoped>
.card-left-a {
  display: flex;
  // justify-content: space-around;
  align-items: center;
  .card-view-content {
    flex: 1;
    text-align: center;
    p {
      margin: 0;
      padding: 0;
    }
    .card-view-content-title {
      /** 文本1 */
      font-size: 18px;
      font-weight: 400;
      letter-spacing: 0px;
      line-height: 26.06px;
      color: rgba(255, 255, 255, 1);
      vertical-align: top;
      margin-bottom: 10px;
    }
    .card-view-content-value {
      /** 文本1 */
      font-size: 32px;
      font-weight: 400;
      letter-spacing: 0px;
      line-height: 41.6px;
      color: rgba(255, 255, 255, 1);
      vertical-align: top;
    }
  }
}
</style>