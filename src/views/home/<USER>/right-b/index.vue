<template>
  <card-view :name="'病害类型统计'">
    <div>
      <type-taba @change="handelChange" />
      <EchartsComp
        ref="refsEchartsComp"
        :option="option"
        :height="'25vh'"
      ></EchartsComp>
    </div>
  </card-view>
</template>
<script lang="ts" setup>
import { defineAsyncComponent, onMounted, ref, watch } from "vue";
import { options } from "./constants";
const cardView = defineAsyncComponent(() => import("../card/index.vue"));
const EchartsComp = defineAsyncComponent(
  () => import("../echarts-comp/index.vue")
);
const typeTaba = defineAsyncComponent(() => import("../type-taba/index.vue"));
const props = defineProps({
  civilStructurePieList: {
    type: Array,
    default: [],
  },
  otherPieList: {
    type: Array,
    default: [],
  },
});
const option = ref<any>(options(props.civilStructurePieList));
const refsEchartsComp = ref<any>(null);
const handelChange = (val) => {
  option.value =
    Number(val) === 1
      ? options(props.civilStructurePieList)
      : options(props.otherPieList);
  refsEchartsComp?.value.updataMap(option.value);
};
onMounted(() => {
  setTimeout(() => {
    refsEchartsComp?.value.updataMap(option.value);
  }, 2000);
});
watch(
  () => props.civilStructurePieList,
  (val) => {
    option.value =
      Number(val) === 1
        ? options(props.civilStructurePieList)
        : options(props.otherPieList);
    refsEchartsComp?.value.updataMap(option.value);
  }
);
</script>