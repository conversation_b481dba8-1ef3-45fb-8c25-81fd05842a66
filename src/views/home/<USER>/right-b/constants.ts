export const options = (data: Array<any> = []) => {
    return {
        grid: {
            top: '10%',
            left: '3%',
            right: '5%',
            bottom: '3%',
            containLabel: true
        },
        tooltip: {
            trigger: 'item',
            formatter: '{b}: {c} ({d}%)' // 这里显示百分比
            // label: {
            //     // show: false,
            //     // position: 'center',
            //     formatter: '{b}{d}%'  // {b}表示名称，{d}表示百分比
            // },
        },
        legend: {
            type: 'scroll',
            orient: 'vertical',
            right: '10%',
            top: 'center',
            width: '40%',
            align: 'auto',
            itemWidth: 14,
            itemHeight: 14,
            textStyle: {
                color: '#fff', // 设置红色作为默认文字颜色
                fontSize: 14
            },
        },
        series: [
            {
                name: '',
                type: 'pie',
                radius: ['60%', '80%'],
                center: ['30%', '50%'],
                avoidLabelOverlap: false,
                padAngle: 5,
                itemStyle: {
                    borderRadius: 5
                },
                label: {
                    show: false,
                    position: 'center',
                    formatter: '{b} {d}%'  // {b}表示名称，{d}表示百分比
                },
                // emphasis: {
                //     label: {
                //         show: true,
                //         fontSize: 40,
                //         fontWeight: 'bold'
                //     }
                // },
                labelLine: {
                    show: false
                },
                data,
                // data: [
                //     { value: 1048, name: '环向裂缝' },
                //     { value: 735, name: '纵向裂缝' },
                //     { value: 580, name: '惨漏水' },
                //     { value: 484, name: '剥落' },
                // ],
            },
            {
                type: 'pie',
                radius: ['0%', '0%'],
                center: ['30%', '50%'],
                label: {
                    show: true,
                    position: 'center',
                    formatter: function () {
                        return `${totalQtyFilter(data)}\n问题总数`;
                    },
                    fontSize: 18,
                    fontWeight: 'bold',
                    color: '#fff'
                },
                data: [{
                    value: totalQtyFilter(data), name: '总数'
                }]
            }
        ]
    }
};
const totalQtyFilter = (data) => {
    return data.reduce((total, current) => Number(total) + Number(current.value), 0);
};