<template>
  <card-view :name="'隧道技术评定等级统计'">
    <div>
      <type-taba @change="handelChange" />
    </div>
    <EchartsComp
      ref="refEchartsComp"
      :option="option"
      :height="'18vh'"
    ></EchartsComp>
  </card-view>
</template>
<script lang="ts" setup>
import { defineAsyncComponent, onMounted, ref, watch } from "vue";
import { options, dataOptions } from "./constants";
const cardView = defineAsyncComponent(() => import("../card/index.vue"));
const EchartsComp = defineAsyncComponent(
  () => import("../echarts-comp/index.vue")
);
const typeTaba = defineAsyncComponent(() => import("../type-taba/index.vue"));
const props = defineProps({
  structureTechnicalScoreDataList: {
    type: Array,
    default: [],
  },
});
const refEchartsComp = ref<any>(null);
const option = ref<any>(options([]));
const typeIndex = ref(1);
const handelChange = (val) => {
  typeIndex.value = Number(val);
  option.value = options(dataFilter());
  refEchartsComp?.value?.updataMap(option.value);
};
const dataFilter = (): any => {
  let data: any = [];
  props.structureTechnicalScoreDataList.forEach((item: any) => {
    if (typeIndex.value === 1) data.push(Number(item.civilCount));
    if (typeIndex.value === 2) data.push(Number(item.otherCount));
  });
  return data;
};
watch(
  () => props.structureTechnicalScoreDataList,
  (val) => {
    option.value = options(dataFilter());
    refEchartsComp?.value?.updataMap(option.value);
  }
);
</script>