export const options = (data:Array<any>) => {
    return {
        grid: {
            top: '10%',
            left: '3%',
            right: '5%',
            bottom: '3%',
            containLabel: true
        },
         tooltip: {
            trigger: 'item'
        },
        xAxis: {
            type: 'category',
            data: ['一类', '二类', '三类', '四类', '五类']
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
                data,
                type: 'bar',
                barWidth: 30 // 固定宽度（像素）
            }
        ]
    }
};
export let dataOptions = [
    [120, 200, 150, 80, 70, 110],
    [100, 120, 190, 220, 150, 130]
];