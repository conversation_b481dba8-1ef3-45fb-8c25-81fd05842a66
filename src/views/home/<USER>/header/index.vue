<template>
  <div class="home-header">
    <img class="home-header-img" src="@/assets/home/<USER>" alt="" />
    <el-row :gutter="20">
      <el-col :span="6"
        ><div class="grid-content ep-bg-purple">
          <span class="date">{{ formattedDate }}</span>
          <span>{{ timeString }}</span>
        </div></el-col
      >
      <el-col :span="12">
        <h1 class="title">隧道检测数字化平台</h1>
      </el-col>
      <el-col :span="6"><div class="grid-content ep-bg-purple" /></el-col>
    </el-row>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";

const formattedDate = ref();
const today = () => {
  // 获取当前日期
  const now = new Date();

  // 获取年月日
  const year = now.getFullYear();
  const month = now.getMonth() + 1; // 月份从0开始，所以要加1
  const day = now.getDate();

  // 获取星期几（0-6，0表示星期日）
  const dayOfWeek = now.getDay();

  // 将数字转换为星期几名称
  const weekdays = [
    "星期日",
    "星期一",
    "星期二",
    "星期三",
    "星期四",
    "星期五",
    "星期六",
  ];
  const weekdayName = weekdays[dayOfWeek];

  // 格式化为"YYYY年MM月DD日 星期X"
  return `${year}年${month}月${day}日 ${weekdayName}`;
};
const timeString = ref("");
function displayCurrentTime() {
  const now = new Date();
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");
  const seconds = String(now.getSeconds()).padStart(2, "0");

  timeString.value = `${hours}:${minutes}:${seconds}`;
  // 或者更新DOM元素
  // document.getElementById('clock').textContent = timeString;

  // 每秒更新一次
  setTimeout(displayCurrentTime, 1000);
}

onMounted(() => {
  formattedDate.value = today();
  // 启动时钟
  displayCurrentTime();
});
</script>
<style lang="scss" scoped>
.home-header {
  width: 100vw;
  height: 8vh;
  //   background-image: url("@/assets/home/<USER>");
  background-size: cover;
  z-index: 2;
  position: relative;
  .home-header-img {
    position: absolute;
    top: 2.5vh;
    left: 50%;
    width: 80vw;
    margin-left: -40vw;
  }
  .title {
    line-height: 40px;
    text-align: center;
    letter-spacing: 5px;
  }
  .grid-content{
    margin-top: 6px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    span {
        font-size: 16px;
        margin-right: 10px;
    }
    .date {
        color: aqua;
    }
  }
}
</style>