<template>
  <div
    class="home"
    element-loading-background="rgba(122, 122, 122, 0.2)"
    element-loading-text="Loading..."
  >
    <div id="container"></div>

    <div class="home-header">
      <HeaderView />
      <!-- <div class="home-header__left-title">隧道检测项目今日新增6个</div> -->
    </div>
    <div class="home-view home-view__left">
      <el-scrollbar height="100%">
        <div @click="toPath" class="home-view__left-title">主菜单</div>
        <lefta :totalMiles="totalMiles" :totalTunnel="totalTunnel" />
        <leftb :tunnelTypeList="tunnelTypeList" />
        <leftc
          :structureTechnicalScoreDataList="structureTechnicalScoreDataList"
        />
      </el-scrollbar>
    </div>
    <div class="home-view home-view__right">
      <el-scrollbar height="100%">
        <righta :manageTunnelDataList="manageTunnelDataList" />
        <rightb
          :civilStructurePieList="civilStructurePieList"
          :otherPieList="otherPieList"
        />
      </el-scrollbar>
    </div>
  </div>
</template>
<script setup>
import {
  ref,
  onMounted,
  onUnmounted,
  provide,
  nextTick,
  onActivated,
  defineAsyncComponent,
} from "vue";
import { useRouter } from "vue-router";
import { queryIndexData } from "@/api/home";
import mapdingIcon from "@/assets/home/<USER>";
const router = useRouter();
const HeaderView = defineAsyncComponent(() =>
  import("./components/header/index.vue")
);
const lefta = defineAsyncComponent(() =>
  import("./components/left-a/index.vue")
);
const leftb = defineAsyncComponent(() =>
  import("./components/left-b/index.vue")
);
const leftc = defineAsyncComponent(() =>
  import("./components/left-c/index.vue")
);
const righta = defineAsyncComponent(() =>
  import("./components/right-a/index.vue")
);
const rightb = defineAsyncComponent(() =>
  import("./components/right-b/index.vue")
);
import { mapType, anhuiCities } from "./constants";
import { styleJson } from "./test/style-json";
const map = ref();

const initMap = async (lat = 117.014855, lng = 31.859474, zoom = 16) => {
  map.value = new BMapGL.Map("container"); //创建地图实例
  let point = new BMapGL.Point(lat, lng); //地图中心点
  map.value.centerAndZoom(point, zoom); //地图初始化，同时设置地图展示级别
  map.value.enableScrollWheelZoom(true); //使用鼠标滚轮控制缩放
  let bdary = new BMapGL.Boundary();

  map.value.setHeading(45); //设置地图旋转角度
  map.value.setTilt(45); //设置地图的倾斜角度
  let marker = new BMapGL.Marker(new BMapGL.Point(117.014855, 31.859474));
  map.value.addOverlay(marker);
  let marker2 = new BMapGL.Marker(new BMapGL.Point(lng, lng));
  // 创建点标记
  let localImage = new BMapGL.Icon(mapdingIcon, new BMapGL.Size(20, 30));
  marker.value = new BMapGL.Marker(point, {
    icon: localImage,
  });
  map.value.addOverlay(marker2);
  map.value.setMapStyleV2({ styleJson });

  // setTimeout(async () => {
  bdary.get("安徽省", function (rs) {
    // 绘制行政区
    for (var i = 0; i < rs.boundaries.length; i++) {
      var path = [];
      var xyArr = rs.boundaries[i].split(";");
      var ptArr = [];
      for (var j = 0; j < xyArr.length; j++) {
        var tmp = xyArr[j].split(",");
        var pt = new BMapGL.Point(tmp[0], tmp[1]);
        ptArr.push(pt);
      }
      var mapmask = new BMapGL.MapMask(ptArr, {
        isBuildingMask: true,
        isPoiMask: true,
        isMapMask: true,
        showRegion: "inside",
        topFillColor: "#ccc",
        topFillOpacity: 1,
        sideFillColor: "#000E3E",
        sideFillOpacity: 1,
      });
      map.value.addOverlay(mapmask);
      var border = new BMapGL.Polyline(ptArr, {
        strokeColor: "#42495a",
        strokeWeight: 1,
        strokeOpacity: 1,
      });
      map.value.addOverlay(border);
    }
  });
  map.value.setHeading(0); //设置地图旋转角度
  map.value.setTilt(50); //设置地图的倾斜角度
  map.value.centerAndZoom(point, 8);
  // }, 2000);
};

//
// 地图接口数据
let loading = ref(false);
// 标记和清除icon
let mapData = ref([]);

const handelMapIcon = (data, type) => {
  // 遍历坐标点，添加标注
  data.forEach(function (coord) {
    let point = new BMapGL.Point(coord.lng, coord.lat);
    let marker = new BMapGL.Marker(point, {
      icon: new BMapGL.Icon(
        mapdingIcon, // 图标地址
        new BMapGL.Size(30, 36), // 图标大小
        {
          anchor: new BMapGL.Size(13, 32), // 图标的锚点
          imageOffset: new BMapGL.Size(0, 0), // 图片偏移
        }
      ),
    });
    marker.addEventListener("click", function () {
      // 这里可以添加点击后执行的代码，比如弹出信息窗口
      var infoWindow = new BMapGL.InfoWindow(
        ` <div class="prop-view">
          <div class="title">信息</div>
          <div class="item-views"><span class='span-view'>名  称：</span>${coord.name}</div>
          <div class="item-views"><span class='span-view'>隧道数量：</span>${coord.quantity}（座）</div>
          <div class="item-views"><span class='span-view'>隧道里程：</span>${coord.mileage}（km）</div>
        </div>
        `,
        {
          width: 200,
          height: 100,
        }
      );
      map.value.openInfoWindow(infoWindow, point);
      // setNewCenter(coord.lng, coord.lat);
    });
    mapData.value.push(marker);
    map.value.addOverlay(marker);
  });
};
// 设置中心点
const setNewCenter = (lng, lat) => {
  let point = new BMapGL.Point(lng, lat);
  map.value.setCenter(point); // 设置地图中心点
  map.value.centerAndZoom(point, 10);
  map.value.addOverlay(marker.value); // 设置地图中心点
};
const handelDeleteIcon = () => {
  for (var i = 0; i < mapData.value.length; i++) {
    map.value.removeOverlay(mapData.value[i]);
  }
  // 清空数组
  mapData.value = [];
};
const toPath = () => {
  router.push({ path: "/basic/project" });
};
const totalMiles = ref(0);
const totalTunnel = ref(0);
const tunnelTypeList = ref([]);
const structureTechnicalScoreDataList = ref([]);
const manageTunnelDataList = ref([]);
const civilStructurePieList = ref([]);
const otherPieList = ref([]);
const init = async () => {
  try {
    const res = await queryIndexData();
    if (res.code == 200) {
      totalMiles.value = res.data.totalMiles;
      totalTunnel.value = res.data.totalTunnel;
      tunnelTypeList.value = res.data.tunnelTypeList;
      structureTechnicalScoreDataList.value =
        res.data.structureTechnicalScoreDataList;
      manageTunnelDataList.value = res.data.manageTunnelDataList;
      civilStructurePieList.value = res.data.civilStructurePieList;
      otherPieList.value = res.data.otherPieList;
      civilStructurePieList.value.forEach((item) => {
        item.value = item.typeNum;
        item.name = item.typeName;
      });
      otherPieList.value.forEach((item) => {
        item.value = item.typeNum;
        item.name = item.typeName;
      });
    }
  } catch (error) {
    console.log(error);
  }
  initMap();
};
onMounted(() => {
  init();
  nextTick(() => {
    setTimeout(() => {
      handelMapIcon(anhuiCities);
    }, 5 * 1000);
  });
});
</script>
<style lang="scss" scoped>
.home {
  height: 100vh;
  width: 100vw;
  color: #fff;
  position: relative;
  background: #ccc;
  overflow: hidden;
  #container {
    position: absolute;
    left: 0;
    height: 100%;
    width: 100%;
    z-index: 1;
  }
  .home-header {
    position: absolute;
    left: 0;
    height: 12vh;
    width: 100%;
    // background: url("../../assets/home/<USER>") no-repeat;
    background-size: 100% 100%;
    z-index: 2;
  }
  .home-header__left-title {
    position: absolute;
    top: 12vh;
    left: 50%;
    width: 42vw;
    height: 32px;
    box-sizing: border-box;
    padding-left: 50px;
    line-height: 32px;
    margin-left: -21vw;
    background-image: url("@/assets/home/<USER>");
    background-size: cover;
    font-size: 14px;
  }
  .home-view {
    position: absolute;
    top: 11.5vh;
    height: calc(100vh - 13vh);
    width: 25vw;

    z-index: 4;
  }
  .home-view__left {
    left: 1vw;
  }
  .home-view__right {
    right: 1vw;
  }
  .left-top-protion {
    position: absolute;
    left: 1vw;
    top: 8vh;
    height: 3vh;
    width: 18vw;
    z-index: 3;
    display: flex;
    align-items: center;
    justify-content: space-around;
    color: #fff;
    span {
      height: 3vh;
      border: 1px solid #5b9efb;
      line-height: 3vh;
      padding: 0 12px;
      border-radius: 3px;
      cursor: pointer;
    }
    .span-active {
      color: #61caff;
      font-weight: 600;
    }
  }
}
:deep(.anchorBL) {
  display: none !important;
}
// @media screen and (max-width: 1550px) {
//   .home-view {
//     min-width: 300px;
//   }
// }
.prop-view {
  // width: 300px;
  background: #1c2c42;
  .title {
    color: #fff;
    text-align: center;
    line-height: 40px;
    font-size: 16px;
    font-weight: 600;
  }
  .item-views {
    height: 30px;
    padding: 0 16px;
    color: #bccde6;
    font-size: 15px;
    span {
      color: #88b9e4;
      font-size: 13px;
    }
  }
}
.home-view__left-title {
  height: 32px;
  width: 50%;
  background-image: url("@/assets/home/<USER>");
  background-size: cover;
  box-sizing: border-box;
  padding-left: 50px;
  line-height: 30px;
  font-size: 16px;
  letter-spacing: 2px;
  color: #fff;
  margin-bottom: 2.5vh;
  cursor: pointer;
}
</style>
