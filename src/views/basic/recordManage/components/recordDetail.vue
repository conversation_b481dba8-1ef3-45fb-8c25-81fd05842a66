<template>
    <div class="record-detail-container">
        <el-form :model="state.detailForm" :rules="state.rules" ref="queryRef">
            <el-row>
                <el-col :span="8">
                    <el-form-item label="隧道编码" prop="name" label-width="auto">
                        <span>{{ state.detailForm.code }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="隧道名称" prop="name" label-width="auto">
                        <span>{{ state.detailForm.name }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="隶属单位" prop="name" label-width="auto">
                        <span>{{ state.detailForm.company }}</span>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="所属路段" prop="name" label-width="auto">
                        <span>{{ state.detailForm.way }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="隧道洞别" prop="name" label-width="auto">
                        <span>{{ state.detailForm.hole }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="结构形式" prop="name" label-width="auto">
                        <span>{{ state.detailForm.structural }}</span>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="隧道交工时间" prop="name" label-width="auto">
                        <span>{{ state.detailForm.startDate }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="隧道竣工时间" prop="name" label-width="auto">
                        <span>{{ state.detailForm.endDate }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="经度" prop="name" label-width="auto">
                        <span>{{ state.detailForm.longitude }}</span>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="纬度" prop="name" label-width="auto">
                        <span>{{ state.detailForm.latitude }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="起始桩号" prop="name" label-width="auto">
                        <span>{{ state.detailForm.startPile }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="截止桩号" prop="name" label-width="auto">
                        <span>{{ state.detailForm.endPile }}</span>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="埋深起" prop="name" label-width="auto">
                        <span>{{ state.detailForm.startDepth }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="埋深止" prop="name" label-width="auto">
                        <span>{{ state.detailForm.endDepth }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="施工期中心桩号" prop="name" label-width="auto">
                        <span>{{ state.detailForm.buildPile }}</span>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="运营期中心桩号" prop="name" label-width="auto">
                        <span>{{ state.detailForm.runPile }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="图片" prop="name" label-width="auto">
                        <el-upload v-model:file-list="fileList" class="upload-demo"
                            action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
                            :on-preview="handlePreview" :on-remove="handleRemove" list-type="picture">
                            <!-- <el-button type="primary">上传图片</el-button> -->
                        </el-upload>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div class="btn-section">
            <el-button @click="btnAction('cancel')" class="btn">取消</el-button>
            <el-button @click="btnAction('sure')" type="primary" class="btn sure">确定</el-button>
        </div>
    </div>
</template>
    
<script setup>
/**
 *  code: 'N1255',
            name: '武汉市黄家湖隧道',
            company: '中建三局武汉公司',
            way: '黄家湖路',
            hole: '单洞',
            structural: '锲形结构',
            startDate: '2024 05-26',
            endDate: '2026 05-26',
            longitude: '113.41',
            latitude: '31.11',
            startPile: 'N01',
            endPile: 'N125',
            tunnelLength: '4.5',
            startDepth: '200',
            endDepth: '20',
            buildPile: 'N55',
            runPile: 'N105',
            imgs:
*/
import { ref, reactive, onMounted } from 'vue';
const props = defineProps({
    tunnelObj: {
        type: Object,
        default: () => { }
    }
});
const state = reactive({
    detailForm: {},
    rules: [],
    companyList: [],
    structureList: []
})

const emit = defineEmits(['btnTap']);

const btnAction = (type) => {
    emit('btnTap', type);
}

const fileList = ref([
    {
        name: 'food.jpeg',
        url: 'https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100',
    },
    {
        name: 'food2.jpeg',
        url: 'https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100',
    },
])

onMounted(() => {
    state.detailForm = props.tunnelObj;
})
</script>
    
<style lang="scss" scoped>
.record-detail-container {

    .btn-section {
        text-align: right;
    }
}
</style>