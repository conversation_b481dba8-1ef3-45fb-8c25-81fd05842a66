<template>
    <div class="batch-container">
        <el-form>
            <el-form-item label="模板文件" prop="name">
                <span @click="downloadAction" class="download">项目模板.xlsx</span>
            </el-form-item>
            <el-form-item label="上传文件" prop="name">
                <el-upload ref="uploadRef" class="upload-demo"
                    action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15" :auto-upload="false">
                    <template #trigger>
                        <el-button type="primary">选择文件</el-button>
                    </template>
                    <template #tip>
                        <div class="desc-section">
                            <span class="desc">1、只能上传xlsx文件，文件大小不能超过2mb</span>
                            <span class="desc">2、文件表单格式要和模板文件相同</span>
                        </div>
                    </template>
                </el-upload>
            </el-form-item>
        </el-form>
    </div>
</template>
    
<script setup name="projectDetail">
const downloadAction = () => {
    window.open('https://picnew2.photophoto.cn/20080512/huangsediaodengguangdesuidaotupian-14899124_1.jpg', '__blank');
}
</script>
    
<style lang="scss" scoped>
.batch-container {
    .download {
        color: #409eff;
        cursor: pointer;
    }

    .desc-section {
        display: flex;
        flex-direction: column;
    }
}
</style>