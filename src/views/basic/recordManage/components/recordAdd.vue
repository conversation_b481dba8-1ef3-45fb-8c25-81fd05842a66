<template>
    <div class="record-add-container">
        <el-form :model="state.addForm" :rules="state.rules" ref="queryRef">
            <el-row>
                <el-col :span="8">
                    <el-form-item label="隧道编码" prop="name" label-width="auto">
                        <el-input v-model="state.addForm.code" placeholder="请输入隧道编码" clearable @keyup.enter="handleSearch"
                            @clear="clearName" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="隧道名称" prop="name" label-width="auto">
                        <el-input v-model="state.addForm.name" placeholder="请输入隧道名称" clearable @keyup.enter="handleSearch"
                            @clear="clearName" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="隶属单位" prop="name" label-width="auto">
                        <el-select v-model="state.addForm.company" class="m-2" placeholder="请选择隶属单位" size="large">
                            <el-option v-for="item in state.companyList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="所属路段" prop="name" label-width="auto">
                        <el-select v-model="state.addForm.way" class="m-2" placeholder="请选择所属路段" size="large">
                            <el-option v-for="item in state.wayList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="隧道洞别" prop="name" label-width="auto">
                        <el-select v-model="state.addForm.hole" class="m-2" placeholder="请选择隧道洞别" size="large">
                            <el-option v-for="item in state.holeList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="结构形式" prop="name" label-width="auto">
                        <el-select v-model="state.addForm.structure" class="m-2" placeholder="请选择结构形式" size="large">
                            <el-option v-for="item in state.structureList" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="隧道交工时间" prop="name" label-width="auto">
                        <el-date-picker v-model="state.addForm.startDate" type="date" placeholder="选择隧道交工时间" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="隧道竣工时间" prop="name" label-width="auto">
                        <el-date-picker v-model="state.addForm.startDate" type="date" placeholder="选择隧道竣工时间" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="经度" prop="name" label-width="auto">
                        <el-input v-model="state.addForm.name" placeholder="请输入经度" clearable @keyup.enter="handleSearch"
                            @clear="clearName" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="纬度" prop="name" label-width="auto">
                        <el-input v-model="state.addForm.name" placeholder="请输入纬度" clearable @keyup.enter="handleSearch"
                            @clear="clearName" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="起始桩号" prop="name" label-width="auto">
                        <el-input type="textarea" v-model="state.addForm.name" placeholder="请输入起始桩号" clearable
                            @keyup.enter="handleSearch" @clear="clearName" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="截止桩号" prop="name" label-width="auto">
                        <el-input type="textarea" v-model="state.addForm.name" placeholder="请输入截止桩号" clearable
                            @keyup.enter="handleSearch" @clear="clearName" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="埋深起" prop="name" label-width="auto">
                        <el-input v-model="state.addForm.code" placeholder="请输入埋深起" clearable @keyup.enter="handleSearch"
                            @clear="clearName" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="埋深止" prop="name" label-width="auto">
                        <el-input type="textarea" v-model="state.addForm.name" placeholder="请输入埋深止" clearable
                            @keyup.enter="handleSearch" @clear="clearName" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="施工期中心桩号" prop="name" label-width="auto">
                        <el-input type="textarea" v-model="state.addForm.name" placeholder="请输入施工期中心桩号" clearable
                            @keyup.enter="handleSearch" @clear="clearName" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="运营期中心桩号" prop="name" label-width="auto">
                        <el-input v-model="state.addForm.code" placeholder="请输入运营期中心桩号" clearable
                            @keyup.enter="handleSearch" @clear="clearName" />
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="图片" prop="name" label-width="auto">
                        <el-upload v-model:file-list="fileList" class="upload-demo"
                            action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
                            :on-preview="handlePreview" :on-remove="handleRemove" list-type="picture">
                            <el-button type="primary">上传图片</el-button>
                        </el-upload>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div class="btn-section">
            <el-button @click="btnAction('cancel')" class="btn">取消</el-button>
            <el-button @click="btnAction('sure')" type="primary" class="btn sure">确定</el-button>
        </div>
    </div>
</template>
    
<script setup>
/**
 *  code: 'N1255',
            name: '武汉市黄家湖隧道',
            company: '中建三局武汉公司',
            way: '黄家湖路',
            hole: '单洞',
            structural: '锲形结构',
            startDate: '2024 05-26',
            endDate: '2026 05-26',
            longitude: '113.41',
            latitude: '31.11',
            startPile: 'N01',
            endPile: 'N125',
            tunnelLength: '4.5',
            startDepth: '200',
            endDepth: '20',
            buildPile: 'N55',
            runPile: 'N105',
            imgs:
*/
import { ref, reactive } from 'vue';
const state = reactive({
    addForm: {},
    rules: [],
    companyList: [
        {
            label: '中国交建',
            value: 'zgjj'
        }, {
            label: '中交三局',
            value: 'zjsj'
        }, {
            label: '中铁三局',
            value: 'ztsj'
        }
    ],
    structureList: [
        {
            label: '楔形结构',
            value: 'xxjg'
        }, {
            label: '倒三角',
            value: 'dsj'
        }, {
            label: '梯形结构',
            value: 'txjg'
        }
    ],
    holeList: [
        {
            label: '左洞',
            value: 'zd'
        }, {
            label: '右洞',
            value: 'yd'
        }, {
            label: '贯穿',
            value: 'gc'
        }
    ],
    wayList: [
        {
            label: '武汉市东湖路',
            value: 'wh'
        }, {
            label: '杭州湖滨路',
            value: 'hz'
        }, {
            label: '上海黄浦路',
            value: 'sh'
        }
    ]
})

const emit = defineEmits(['btnTap']);

const btnAction = (type) => {
    emit('btnTap', type);
}

const fileList = ref([
    {
        name: 'food.jpeg',
        url: 'https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100',
    },
    {
        name: 'food2.jpeg',
        url: 'https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100',
    },
])
</script>
    
<style lang="scss" scoped>
.record-add-container {

    :deep(.el-input) {
        width: 250px;
    }

    :deep(.el-textarea__inner) {
        width: 250px;
    }

    .btn-section {
        text-align: right;
    }
}
</style>