<template>
    <div class="app-container">
        <div class="search-section">
            <el-form :model="searchParams" ref="queryRef" label-width="auto" :inline="true">
                <el-form-item label="隧道编码" prop="name">
                    <el-input v-model="searchParams.code" placeholder="请输入项目编码" clearable />
                </el-form-item>
                <el-form-item label="隧道名称" prop="name">
                    <el-input v-model="searchParams.name" placeholder="请输入项目名称" clearable />
                </el-form-item>
                <el-form-item label="隶属单位" prop="name">
                    <el-select v-model="searchParams.company" placeholder="请选择">
                        <el-option v-for="item in state.companyList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="所属路段" prop="name">
                    <el-select v-model="searchParams.way" placeholder="请选择">
                        <el-option v-for="item in state.wayList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="隧道洞别" prop="name">
                    <el-select v-model="searchParams.hole" placeholder="请选择">
                        <el-option v-for="item in state.holeList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="结构形式" prop="name">
                    <el-select v-model="searchParams.structure" placeholder="请选择">
                        <el-option v-for="item in state.structureList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="Search" @click="searchAction">搜索</el-button>
                    <el-button icon="Refresh" @click="resetAction">重置</el-button>
                    <el-button icon="Plus" @click="addProjectAction" v-hasPermi="['project:info:add']">新增</el-button>
                    <el-button icon="Plus" @click="batchProjectAction">批量新增</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="table-section">
            <el-table :data="state.recordList" style="width: 100%">
                <el-table-column prop="code" label="隧道编码" />
                <el-table-column prop="name" label="隧道名称" width="200" />
                <el-table-column prop="company" label="隶属单位" width="200" />
                <el-table-column prop="way" label="所属路段" />
                <el-table-column prop="hole" label="隧道洞别" width="120" />
                <el-table-column prop="structural" label="结构形式" />
                <el-table-column prop="startDate" label="隧道交工时间" width="120" />
                <el-table-column prop="endDate" label="隧道竣工时间" width="120" />
                <el-table-column prop="longitude" label="经度" />
                <el-table-column prop="latitude" label="纬度" />
                <el-table-column prop="startPile" label="起始桩号" />
                <el-table-column prop="endPile" label="截止桩号" />
                <el-table-column prop="tunnelLength" label="隧道长度" />
                <el-table-column prop="startDepth" label="埋深起" />
                <el-table-column prop="endDepth" label="埋深止" />
                <el-table-column prop="buildPile" label="施工期中心桩号" width="120" />
                <el-table-column prop="runPile" label="运营期中心桩号" width="120" />
                <el-table-column prop="imgs" label="图片" width="150">
                    <template #default="scope">
                        <div class="img-list">
                            <el-image preview-teleported class="img-item" v-for="(item, index) in scope.row.imgs"
                                :src="item" :key="index" :zoom-rate="1.2" :preview-src-list="scope.row.imgs" fit="cover" />
                        </div>
                    </template>
                </el-table-column>
                <el-table-column fixed="right" prop="operation" label="操作" width="250">
                    <template #default="scope">
                        <div class="operate-section">
                            <el-button @click="btnAction('detail', scope.row)" type="primary" class="btn">查看</el-button>
                            <!-- <el-button @click="btnAction('relate')" class="btn">关联</el-button> -->
                            <el-button @click="btnAction('modify', scope.row)" class="btn">编辑</el-button>
                            <el-button @click="btnAction('delete', scope.row)" class="btn">删除</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="page-section">
            <el-pagination background layout="prev, pager, next" :total="1000" />
        </div>
        <el-dialog v-model="state.addModal" title="隧道新增" width="1200px">
            <RecordAdd @btnTap="addBtnTapHandler"></RecordAdd>
        </el-dialog>
        <el-dialog v-model="state.modifyModal" title="隧道修改" width="1200px">
            <RecordModify :tunnelObj="state.currentTunnel" @btnTap="modifyBtnTapHandler"></RecordModify>
        </el-dialog>
        <el-dialog v-model="state.detailModal" title="隧道详情" width="1200px">
            <RecordDetail :tunnelObj="state.currentTunnel"></RecordDetail>
        </el-dialog>
        <el-dialog v-model="state.batchModal" title="批量新增" width="40%">
            <RecordAddBatch></RecordAddBatch>
        </el-dialog>
    </div>
</template>
    
<script setup name="RecordManage">
import { reactive, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import RecordAdd from './components/recordAdd.vue';
import RecordDetail from './components/recordDetail.vue';
import RecordModify from './components/recordModify.vue';
import RecordAddBatch from './components/recordAddBatch.vue'

const searchParams = reactive({
    name: '',
    code: '',
    company: '',
    status: '',
    structure: '',
    hole: ''
});
const state = reactive({
    relateModal: false,
    detailModal: false,
    addModal: false,
    modifyModal: false,
    batchModal: false,
    currentTunnel: {},
    statusList: [],
    companyList: [
        {
            label: '中国交建',
            value: 'zgjj'
        }, {
            label: '中交三局',
            value: 'zjsj'
        }, {
            label: '中铁三局',
            value: 'ztsj'
        }
    ],
    holeList: [
        {
            label: '左洞',
            value: 'zd'
        }, {
            label: '右洞',
            value: 'yd'
        }, {
            label: '贯穿',
            value: 'gc'
        }
    ],
    structureList: [
        {
            label: '楔形结构',
            value: 'xxjg'
        }, {
            label: '倒三角',
            value: 'dsj'
        }, {
            label: '梯形结构',
            value: 'txjg'
        }
    ],
    wayList: [
        {
            label: '武汉市东湖路',
            value: 'wh'
        }, {
            label: '杭州湖滨路',
            value: 'hz'
        }, {
            label: '上海黄浦路',
            value: 'sh'
        }
    ],
    recordList: [ // <!--code name company way hole structural startDate endDate longitude latitude startPile endPile tunnelLength startDepth endDepth buildPile runPile imgs-->
        {
            code: 'N1255',
            name: '武汉市黄家湖隧道',
            company: '中建三局武汉公司',
            way: '黄家湖路',
            hole: '单洞',
            structural: '锲形结构',
            startDate: '2024 05-26',
            endDate: '2026 05-26',
            longitude: '113.41',
            latitude: '31.11',
            startPile: 'N01',
            endPile: 'N125',
            tunnelLength: '4.5',
            startDepth: '200',
            endDepth: '20',
            buildPile: 'N55',
            runPile: 'N105',
            imgs: ['https://img0.baidu.com/it/u=*********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333', 'https://img0.baidu.com/it/u=*********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333']
        }, {
            code: 'N1255',
            name: '武汉市黄家湖隧道',
            company: '中建三局武汉公司',
            way: '黄家湖路',
            hole: '单洞',
            structural: '锲形结构',
            startDate: '2024 05-26',
            endDate: '2026 05-26',
            longitude: '113.41',
            latitude: '31.11',
            startPile: 'N01',
            endPile: 'N125',
            tunnelLength: '4.5',
            startDepth: '200',
            endDepth: '20',
            buildPile: 'N55',
            runPile: 'N105',
            imgs: ['https://img0.baidu.com/it/u=*********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333', 'https://img0.baidu.com/it/u=*********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333']
        }, {
            code: 'N1255',
            name: '武汉市黄家湖隧道',
            company: '中建三局武汉公司',
            way: '黄家湖路',
            hole: '单洞',
            structural: '锲形结构',
            startDate: '2024 05-26',
            endDate: '2026 05-26',
            longitude: '113.41',
            latitude: '31.11',
            startPile: 'N01',
            endPile: 'N125',
            tunnelLength: '4.5',
            startDepth: '200',
            endDepth: '20',
            buildPile: 'N55',
            runPile: 'N105',
            imgs: ['https://img0.baidu.com/it/u=*********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333', 'https://img0.baidu.com/it/u=*********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333']
        }, {
            code: 'N1255',
            name: '武汉市黄家湖隧道',
            company: '中建三局武汉公司',
            way: '黄家湖路',
            hole: '单洞',
            structural: '锲形结构',
            startDate: '2024 05-26',
            endDate: '2026 05-26',
            longitude: '113.41',
            latitude: '31.11',
            startPile: 'N01',
            endPile: 'N125',
            tunnelLength: '4.5',
            startDepth: '200',
            endDepth: '20',
            buildPile: 'N55',
            runPile: 'N105',
            imgs: ['https://img0.baidu.com/it/u=*********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333', 'https://img0.baidu.com/it/u=*********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333']
        }, {
            code: 'N1255',
            name: '武汉市黄家湖隧道',
            company: '中建三局武汉公司',
            way: '黄家湖路',
            hole: '单洞',
            structural: '锲形结构',
            startDate: '2024 05-26',
            endDate: '2026 05-26',
            longitude: '113.41',
            latitude: '31.11',
            startPile: 'N01',
            endPile: 'N125',
            tunnelLength: '4.5',
            startDepth: '200',
            endDepth: '20',
            buildPile: 'N55',
            runPile: 'N105',
            imgs: ['https://img0.baidu.com/it/u=*********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333', 'https://img0.baidu.com/it/u=*********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333']
        }, {
            code: 'N1255',
            name: '武汉市黄家湖隧道',
            company: '中建三局武汉公司',
            way: '黄家湖路',
            hole: '单洞',
            structural: '锲形结构',
            startDate: '2024 05-26',
            endDate: '2026 05-26',
            longitude: '113.41',
            latitude: '31.11',
            startPile: 'N01',
            endPile: 'N125',
            tunnelLength: '4.5',
            startDepth: '200',
            endDepth: '20',
            buildPile: 'N55',
            runPile: 'N105',
            imgs: ['https://img0.baidu.com/it/u=*********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333', 'https://img0.baidu.com/it/u=*********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333']
        }, {
            code: 'N1255',
            name: '武汉市黄家湖隧道',
            company: '中建三局武汉公司',
            way: '黄家湖路',
            hole: '单洞',
            structural: '锲形结构',
            startDate: '2024 05-26',
            endDate: '2026 05-26',
            longitude: '113.41',
            latitude: '31.11',
            startPile: 'N01',
            endPile: 'N125',
            tunnelLength: '4.5',
            startDepth: '200',
            endDepth: '20',
            buildPile: 'N55',
            runPile: 'N105',
            imgs: ['https://img0.baidu.com/it/u=*********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333', 'https://img0.baidu.com/it/u=*********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333']
        }, {
            code: 'N1255',
            name: '武汉市黄家湖隧道',
            company: '中建三局武汉公司',
            way: '黄家湖路',
            hole: '单洞',
            structural: '锲形结构',
            startDate: '2024 05-26',
            endDate: '2026 05-26',
            longitude: '113.41',
            latitude: '31.11',
            startPile: 'N01',
            endPile: 'N125',
            tunnelLength: '4.5',
            startDepth: '200',
            endDepth: '20',
            buildPile: 'N55',
            runPile: 'N105',
            imgs: ['https://img0.baidu.com/it/u=*********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333', 'https://img0.baidu.com/it/u=*********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333']
        }
    ]
})

const searchAction = () => {

};
const resetAction = () => {

};

const addBtnTapHandler = (type) => {
    switch (type) {
        case 'cancel':
            state.addModal = false;
            break;
        case 'sure':

            break;
    }
}

const modifyBtnTapHandler = (type) => {
    switch (type) {
        case 'cancel':
            state.modifyModal = false;
            break;
        case 'sure':

            break;
    }
}

const addProjectAction = () => {
    state.addModal = true;
}

const batchProjectAction = () => {
    state.batchModal = true;
}

const btnAction = (type, tunnel) => {
    state.currentTunnel = tunnel;
    switch (type) {
        case 'detail':
            state.detailModal = true;
            break;
        case 'relate':
            state.relateModal = true;
            break;
        case 'modify':
            state.modifyModal = true;
            break;
        case 'delete':
            ElMessageBox.confirm(
                '确定要删除该隧道档案信息?',
                '温馨提示',
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }
            ).then(() => {
            }).catch(() => {
            })
            break;
    }
}
</script>
    
<style lang="scss" scoped>
.app-container {
    height: 100%;
    padding: 0 20px;

    .search-section {
        padding: 20px 0;
    }

    .page-section {
        padding-top: 20px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }

    .img-list {
        display: flex;

        .img-item {
            width: 50px;
            height: 50px;
            margin-left: 5px;

            &:first-child {
                margin-left: 0;
            }
        }
    }
}
</style>