<template>
    <div class="relate-container">
        <div class="search-section">
            <el-form :model="searchParams" ref="queryRef" :inline="true">
                <el-form-item label="隧道编码" prop="name">
                    <el-input v-model="searchParams.code" placeholder="请输入隧道编码" clearable />
                </el-form-item>
                <el-form-item label="隧道名称" prop="name">
                    <el-input v-model="searchParams.name" placeholder="请输入隧道名称" clearable />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="Search" @click="searchAction">搜索</el-button>
                    <el-button icon="Refresh" @click="resetAction">重置</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="table-section">
            <el-table @selection-change="selectTunnelListChange" :data="state.tunnelList" style="width: 100%">
                <el-table-column type="selection" width="55" />
                <el-table-column prop="code" label="隧道编码" />
                <el-table-column prop="name" label="隧道名称" width="200" />
                <el-table-column prop="company" label="隶属单位" width="200" />
                <el-table-column prop="way" label="所属路段" />
                <el-table-column prop="hole" label="隧道洞别" width="120" />
                <el-table-column prop="structural" label="结构形式" />
                <el-table-column prop="startDate" label="隧道交工时间" width="120" />
                <el-table-column prop="endDate" label="隧道竣工时间" width="120" />
                <el-table-column prop="startPile" label="起始桩号" />
                <el-table-column prop="endPile" label="截止桩号" />
                <el-table-column prop="tunnelLength" label="隧道长度" />
                <el-table-column prop="startDepth" label="埋深起" />
                <el-table-column prop="endDepth" label="埋深止" />
                <el-table-column prop="buildPile" label="施工期中心桩号" width="120" />
                <el-table-column prop="runPile" label="运营期中心桩号" width="120" />
            </el-table>
        </div>
        <div class="page-section">
            <el-pagination background layout="prev, pager, next" :total="1000" />
        </div>
        <div class="btn-section">
            <el-button @click="btnAction('cancel')" class="btn">取消</el-button>
            <el-button @click="btnAction('sure')" type="primary" class="btn sure">确定</el-button>
        </div>
    </div>
</template>
    
<script setup name="projectRelate">
import {ref, reactive} from 'vue';
const searchParams = reactive({
    code: '',
    name: ''
})
const state = reactive({
    tunnelList: [
    {
            code: 'N1255',
            name: '武汉市黄家湖隧道',
            company: '中建三局武汉公司',
            way: '黄家湖路',
            hole: '单洞',
            structural: '锲形结构',
            startDate: '2024 05-26',
            endDate: '2026 05-26',
            longitude: '113.41',
            latitude: '31.11',
            startPile: 'N01',
            endPile: 'N125',
            tunnelLength: '4.5',
            startDepth: '200',
            endDepth: '20',
            buildPile: 'N55',
            runPile: 'N105',
            imgs: ['https://img0.baidu.com/it/u=*********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333', 'https://img0.baidu.com/it/u=*********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333']
        }, {
            code: 'N1255',
            name: '武汉市黄家湖隧道',
            company: '中建三局武汉公司',
            way: '黄家湖路',
            hole: '单洞',
            structural: '锲形结构',
            startDate: '2024 05-26',
            endDate: '2026 05-26',
            longitude: '113.41',
            latitude: '31.11',
            startPile: 'N01',
            endPile: 'N125',
            tunnelLength: '4.5',
            startDepth: '200',
            endDepth: '20',
            buildPile: 'N55',
            runPile: 'N105',
            imgs: ['https://img0.baidu.com/it/u=*********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333', 'https://img0.baidu.com/it/u=*********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333']
        }, {
            code: 'N1255',
            name: '武汉市黄家湖隧道',
            company: '中建三局武汉公司',
            way: '黄家湖路',
            hole: '单洞',
            structural: '锲形结构',
            startDate: '2024 05-26',
            endDate: '2026 05-26',
            longitude: '113.41',
            latitude: '31.11',
            startPile: 'N01',
            endPile: 'N125',
            tunnelLength: '4.5',
            startDepth: '200',
            endDepth: '20',
            buildPile: 'N55',
            runPile: 'N105',
            imgs: ['https://img0.baidu.com/it/u=*********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333', 'https://img0.baidu.com/it/u=*********,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333']
        }
    ],
    selectTunnelList: []
})
const emit = defineEmits(['btnTap']);
const btnAction = (type) => {
    emit('btnTap', type, state.selectTunnelList);
}
const selectTunnelListChange = (arr) => {
    state.selectTunnelList = arr;
}

const searchAction = () => {

}

const resetAction = () => {
    
}
</script>
    
<style lang="scss" scoped>

.page-section {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
}
.btn-section {
    text-align: right;
}
</style>