<template>
    <div class="project-detail-container">
        <el-form :model="state.detailForm" :rules="state.rules" ref="queryRef">
            <el-row>
                <el-col :span="8">
                    <el-form-item label="项目编码" prop="code" label-width="auto">
                        <span>{{ state.detailForm.code }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="项目名称" prop="name" label-width="auto">
                        <span>{{ state.detailForm.name }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="总里程" prop="km" label-width="auto">
                        <span>{{ state.detailForm.km }}</span>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="开始时间" prop="startDate" label-width="auto">
                        <span>{{ state.detailForm.startDate }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="截止时间" prop="endDate" label-width="auto">
                        <span>{{ state.detailForm.endDate }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="状态" prop="status" label-width="auto">
                        <span>{{ state.detailForm.status }}</span>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="甲方负责人" prop="aPerson" label-width="auto">
                        <span>{{ state.detailForm.aPerson }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="乙方负责人" prop="bPerson" label-width="auto">
                        <span>{{ state.detailForm.bPerson }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="隶属单位" prop="company" label-width="auto">
                        <span>{{ state.detailForm.company }}</span>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="8">
                    <el-form-item label="隧道检测数量" prop="checkNum" label-width="auto">
                        <span>{{ state.detailForm.checkNum }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="备注" prop="remark" label-width="auto">
                        <span>{{ state.detailForm.remark }}</span>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </div>
</template>
    
<script setup name="projectDetail">
import { ref, reactive, onMounted } from 'vue';
const emit = defineEmits(['btnTap']);
const props = defineProps({
    projectObj: {
        type: Object,
        default: () => {}
    }
});
/**
 *  code: 'N125589', name: '中建五局武汉市黄家湖隧道工程',
            km: '25',
            startDate: '2023 05-25',
            endDate: '2025 05-25',
            status: '施工中',
            aPerson: '赵小虎',
            bPerson: '李峰',
            company: '中建五局',
            checkNum: 10,
            remark: '项目正在施工中'
*/
const state = reactive({
    detailForm: {
        startDate: '',
        endDate: ''
    },
    rules: [],
    statusList: [],
    companyList: []
})

const btnAction = (type) => {
    emit('btnTap', type);
}

onMounted(() => {
    state.detailForm = props.projectObj;
})
</script>
    
<style lang="scss" scoped>
:deep(.el-input) {
    width: 250px;
}

.btn-section {
    text-align: right;
}
</style>