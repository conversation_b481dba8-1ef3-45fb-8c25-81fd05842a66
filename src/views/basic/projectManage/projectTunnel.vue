<template>
    <div class="app-container">
        <div class="search-section">
            <el-form :model="searchParams" ref="queryRef" :inline="true">
                <el-form-item label="隧道编码" prop="name">
                    <el-input v-model="searchParams.code" placeholder="请输入隧道编码" clearable />
                </el-form-item>
                <el-form-item label="隧道名称" prop="name">
                    <el-input v-model="searchParams.name" placeholder="请输入隧道名称" clearable />
                </el-form-item>
                <el-form-item label="隶属单位" prop="name">
                    <el-select v-model="searchParams.company" placeholder="请选择隶属单位">
                        <el-option v-for="item in state.companyList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="状态" prop="name">
                    <el-select v-model="searchParams.status" placeholder="请选择状态">
                        <el-option v-for="item in state.statusList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="Search" @click="searchAction">搜索</el-button>
                    <el-button icon="Refresh" @click="resetAction">重置</el-button>
                    <el-button type="primary" @click="relateTunnelAction" class="btn">关联隧道</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="table-section">
            <el-table :data="state.tunnelList" style="width: 100%">
                <el-table-column prop="id" label="项目隧道关联ID" width="130" />
                <el-table-column prop="code" label="隧道编码" width="140" />
                <el-table-column prop="name" label="隧道名称" width="140" />
                <el-table-column prop="project" label="所属项目" width="200" />
                <el-table-column prop="km" label="检测里程" width="120" />
                <el-table-column prop="startDate" label="开始时间" width="120" />
                <el-table-column prop="endDate" label="截止时间" width="120" />
                <el-table-column prop="status" label="状态" width="120" />
                <el-table-column prop="aPerson" label="甲方项目负责人" width="120" />
                <el-table-column prop="bPerson" label="乙方项目负责人" width="120" />
                <el-table-column prop="company" label="隶属单位" width="120" />
                <el-table-column prop="remark" label="备注" width="120" />
                <el-table-column fixed="right" prop="operation" label="操作" width="180">
                    <template #default="scope">
                        <div class="operate-section">
                            <el-button @click="btnAction('modify', scope.row)" class="btn">编辑</el-button>
                            <el-button @click="btnAction('delete', scope.row)" class="btn">删除</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="page-section">
            <el-pagination background layout="prev, pager, next" :total="1000" />
        </div>
        <el-dialog v-model="state.relateModal" title="关联隧道" width="95%">
            <ProjectRelate @btnTap="relateBtnTapHandler"></ProjectRelate>
        </el-dialog>
        <el-dialog v-model="state.modifyModal" title="关联编辑" width="1200px">
            <ProjectTunnelModify @btnTap="modifyBtnTapHandler" :projectObj="state.currentTunnel"></ProjectTunnelModify>
        </el-dialog>
    </div>
</template>
    
<script setup name="ProjectTunnel">
import { reactive, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter, useRoute } from 'vue-router';
import ProjectRelate from './components/projectRelate.vue';
import ProjectTunnelModify from './components/projectTunnelModify.vue';

const searchParams = reactive({
    name: '',
    code: '',
    company: '',
    status: ''
});
const state = reactive({
    relateModal: false,
    currentTunnel: {},
    tunnelList: [
        {
            id: '2153',
            code: 'N1052',
            name: '武汉市东湖隧道',
            project: '武汉市东湖市政工程',
            km: '11km',
            startDate: '2023-02-25',
            endDate: '2026-05-25',
            status: '施工中',
            aPerson: '张小明',
            bPerson: '王小虎',
            company: '中交四局',
            remark: '备注信息'
        }, {
            id: '2153',
            code: 'N1052',
            name: '武汉市东湖隧道',
            project: '武汉市东湖市政工程',
            km: '11km',
            startDate: '2023-02-25',
            endDate: '2026-05-25',
            status: '施工中',
            aPerson: '张小明',
            bPerson: '王小虎',
            company: '中交四局',
            remark: '备注信息'
        }, {
            id: '2153',
            code: 'N1052',
            name: '武汉市东湖隧道',
            project: '武汉市东湖市政工程',
            km: '11km',
            startDate: '2023-02-25',
            endDate: '2026-05-25',
            status: '施工中',
            aPerson: '张小明',
            bPerson: '王小虎',
            company: '中交四局',
            remark: '备注信息'
        }, {
            id: '2153',
            code: 'N1052',
            name: '武汉市东湖隧道',
            project: '武汉市东湖市政工程',
            km: '11km',
            startDate: '2023-02-25',
            endDate: '2026-05-25',
            status: '施工中',
            aPerson: '张小明',
            bPerson: '王小虎',
            company: '中交四局',
            remark: '备注信息'
        }
    ],
    statusList: [
        {
            label: '施工中',
            value: 'sgz'
        }, {
            label: '未开始',
            value: 'wks'
        }, {
            label: '已竣工',
            value: 'yjg'
        }, {
            label: '停工',
            value: 'tg'
        }
    ],
    companyList: [
        {
            label: '中国交建',
            value: 'zgjj'
        }, {
            label: '中交三局',
            value: 'zjsj'
        }, {
            label: '中铁三局',
            value: 'ztsj'
        }
    ]
})

const relateBtnTapHandler = (type) => {
    switch (type) {
        case 'cancel':
            state.relateModal = false;
            break;
        case 'sure':
            break;
    }
}

const btnAction = (type, project) => {
    state.currentTunnel = project;
    switch (type) {
        case 'detail':
            state.detailModal = true;
            break;
        case 'relate':
            state.relateModal = true;
            break;
        case 'modify':
            state.modifyModal = true;
            break;
        case 'delete':
            ElMessageBox.confirm(
                '确定要删除该关联信息?',
                '温馨提示',
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }
            ).then(() => {
            }).catch(() => {
            })
            break;
    }
}

const relateTunnelAction = () => {
    state.relateModal = true;
}

const modifyBtnTapHandler = (type) => {
    switch (type) {
        case 'cancel':
            state.modifyModal = false;
            break;
        case 'sure':

            break;
    }
}

</script>
    
<style lang="scss" scoped>
.app-container {
    height: 100%;
    padding: 0 20px;

    .search-section {
        padding: 20px 0;
    }

    .page-section {
        padding-top: 20px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }
}
</style>