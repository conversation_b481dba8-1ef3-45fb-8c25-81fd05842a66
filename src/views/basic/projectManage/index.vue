<template>
    <div class="app-container">
        <div class="search-section">
            <el-form :model="searchParams" ref="queryRef" :inline="true">
                <el-form-item label="项目编码" prop="name">
                    <el-input v-model="searchParams.code" placeholder="请输入项目编码" clearable />
                </el-form-item>
                <el-form-item label="项目名称" prop="name">
                    <el-input v-model="searchParams.name" placeholder="请输入项目名称" clearable />
                </el-form-item>
                <el-form-item label="隶属单位" prop="name">
                    <el-select v-model="searchParams.company" placeholder="请选择隶属单位">
                        <el-option v-for="item in state.companyList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="状态" prop="name">
                    <el-select v-model="searchParams.status" placeholder="请选择状态">
                        <el-option v-for="item in state.statusList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="Search" @click="searchAction">搜索</el-button>
                    <el-button icon="Refresh" @click="resetAction">重置</el-button>
                    <el-button icon="Plus" @click="addProjectAction" v-hasPermi="['project:info:add']">新增</el-button>
                    <el-button icon="Plus" @click="batchProjectAction" v-hasPermi="['project:info:add']">批量新增</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="table-section">
            <el-table :data="state.projectList" style="width: 100%">
                <el-table-column prop="code" label="项目编码" width="100" />
                <el-table-column prop="name" label="项目名称" width="140" />
                <el-table-column prop="km" label="总里程" />
                <el-table-column prop="startDate" label="开始时间" width="120" />
                <el-table-column prop="endDate" label="截止时间" width="120" />
                <el-table-column prop="status" label="状态" />
                <el-table-column prop="aPerson" label="甲方项目负责人" width="120" />
                <el-table-column prop="bPerson" label="乙方项目负责人" width="120" />
                <el-table-column prop="company" label="隶属单位" />
                <el-table-column prop="checkNum" label="检测隧道数量" width="120" />
                <el-table-column prop="remark" label="备注" width="120" />
                <el-table-column fixed="right" prop="operation" label="操作" width="300">
                    <template #default="scope">
                        <div class="operate-section">
                            <el-button @click="btnAction('detail', scope.row)" type="primary" class="btn">查看</el-button>
                            <el-button @click="btnAction('relate', scope.row)" class="btn">关联</el-button>
                            <el-button @click="btnAction('modify', scope.row)" class="btn">编辑</el-button>
                            <el-button @click="btnAction('delete', scope.row)" class="btn">删除</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="page-section">
            <el-pagination background layout="prev, pager, next" :total="1000" />
        </div>
        <el-dialog v-model="state.detailModal" title="项目详情" width="1200px">
            <ProjectDetail :projectObj="state.currentProject"></ProjectDetail>
        </el-dialog>
        <el-dialog v-model="state.relateModal" title="项目关联" width="95%">
            <ProjectRelate @btnTap="relateBtnTapHandler"></ProjectRelate>
        </el-dialog>
        <el-dialog v-model="state.addModal" title="项目新增" width="1200px">
            <ProjectAdd @btnTap="btnTapHandler"></ProjectAdd>
        </el-dialog>
        <el-dialog v-model="state.batchModal" title="批量新增" width="40%">
            <ProjectAddBatch></ProjectAddBatch>
        </el-dialog>
        <el-dialog v-model="state.modifyModal" title="项目编辑" width="1200px">
            <ProjectModify @btnTap="modifyBtnTapHandler" :projectObj="state.currentProject"></ProjectModify>
        </el-dialog>
    </div>
</template>
    
<script setup name="ProjectManage">
import {onMounted, reactive, ref} from 'vue';
import ProjectDetail from './components/projectDetail.vue';
import ProjectRelate from './components/projectRelate.vue';
import ProjectAdd from './components/projectAdd.vue';
import ProjectAddBatch from './components/projectAddBatch.vue';
import ProjectModify from './components/projectModify.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter, useRoute } from 'vue-router';
import request from '@/utils/request'

const router = useRouter();
const route = useRoute();

const searchParams = reactive({
    name: '',
    code: '',
    company: '',
    status: ''
});
const state = reactive({
    batchModal: false,
    relateModal: false,
    detailModal: false,
    addModal: false,
    modifyModal: false,
    statusList: [
        {
            label: '施工中',
            value: 'sgz'
        }, {
            label: '未开始',
            value: 'wks'
        }, {
            label: '已竣工',
            value: 'yjg'
        }, {
            label: '停工',
            value: 'tg'
        }
    ],
    companyList: [
        {
            label: '中国交建',
            value: 'zgjj'
        }, {
            label: '中交三局',
            value: 'zjsj'
        }, {
            label: '中铁三局',
            value: 'ztsj'
        }
    ],
    currentProject: {},
    projectList: [
        {
            code: 'N125589',
            name: '中建五局武汉市黄家湖隧道工程',
            km: '25',
            startDate: '2023 05-25',
            endDate: '2025 05-25',
            status: '施工中',
            aPerson: '赵小虎',
            bPerson: '李峰',
            company: '中建五局',
            checkNum: 10,
            remark: '项目正在施工中'
        }, {
            code: 'N125589',
            name: '中建五局武汉市黄家湖隧道工程',
            km: '25',
            startDate: '2023 05-25',
            endDate: '2025 05-25',
            status: '施工中',
            aPerson: '赵小虎',
            bPerson: '李峰',
            company: '中建五局',
            checkNum: 10,
            remark: '项目正在施工中'
        }, {
            code: 'N125589',
            name: '中建五局武汉市黄家湖隧道工程',
            km: '25',
            startDate: '2023 05-25',
            endDate: '2025 05-25',
            status: '施工中',
            aPerson: '赵小虎',
            bPerson: '李峰',
            company: '中建五局',
            checkNum: 10,
            remark: '项目正在施工中'
        }, {
            code: 'N125589',
            name: '中建五局武汉市黄家湖隧道工程',
            km: '25',
            startDate: '2023 05-25',
            endDate: '2025 05-25',
            status: '施工中',
            aPerson: '赵小虎',
            bPerson: '李峰',
            company: '中建五局',
            checkNum: 10,
            remark: '项目正在施工中'
        }, {
            code: 'N125589',
            name: '中建五局武汉市黄家湖隧道工程',
            km: '25',
            startDate: '2023 05-25',
            endDate: '2025 05-25',
            status: '施工中',
            aPerson: '赵小虎',
            bPerson: '李峰',
            company: '中建五局',
            checkNum: 10,
            remark: '项目正在施工中'
        }, {
            code: 'N125589',
            name: '中建五局武汉市黄家湖隧道工程',
            km: '25',
            startDate: '2023 05-25',
            endDate: '2025 05-25',
            status: '施工中',
            aPerson: '赵小虎',
            bPerson: '李峰',
            company: '中建五局',
            checkNum: 10,
            remark: '项目正在施工中'
        }, {
            code: 'N125589',
            name: '中建五局武汉市黄家湖隧道工程',
            km: '25',
            startDate: '2023 05-25',
            endDate: '2025 05-25',
            status: '施工中',
            aPerson: '赵小虎',
            bPerson: '李峰',
            company: '中建五局',
            checkNum: 10,
            remark: '项目正在施工中'
        }, {
            code: 'N125589',
            name: '中建五局武汉市黄家湖隧道工程',
            km: '25',
            startDate: '2023 05-25',
            endDate: '2025 05-25',
            status: '施工中',
            aPerson: '赵小虎',
            bPerson: '李峰',
            company: '中建五局',
            checkNum: 10,
            remark: '项目正在施工中'
        }, {
            code: 'N125589',
            name: '中建五局武汉市黄家湖隧道工程',
            km: '25',
            startDate: '2023 05-25',
            endDate: '2025 05-25',
            status: '施工中',
            aPerson: '赵小虎',
            bPerson: '李峰',
            company: '中建五局',
            checkNum: 10,
            remark: '项目正在施工中'
        }
    ]
})

onMounted(() => {
  searchAction()

  // this.$http({
  //   url: '/item-admin-web/tag/getTagByTagName',
  //   method: 'post',
  //   data: this.$http.adornData({
  //     'pageNum': this.pageIndex,
  //     'pageSize': this.pageSize,
  //     'tagName': this.dataForm.tagName,
  //     'tagCode': this.dataForm.tagCode,
  //     'isAvailable': this.dataForm.isAvailable,
  //     'tagBusinessTypeId':this.dataForm.tagBusinessTypeId
  //   })
  // }).then(function (_ref) {
  //   var data = _ref.data;
  //   if (data.data.list) {
  //     _this.dataList = data.data.list;
  //     _this.totalPage = data.data.total;
  //   } else {
  //     _this.dataList = [];
  //     _this.totalPage = 0;
  //   }
  //   _this.dataListLoading = false;
  // });
})
const searchAction = () => {
  // this.dataListLoading = true;
  request({
    url: '/tunnel/opening/list',
    method: 'get',
    params: searchParams
  }).then(response => {
    this.projectList = response.rows;
    this.total = response.total;
  });
};
const resetAction = () => {

};

const btnTapHandler = (type) => {
    switch (type) {
        case 'cancel':
            state.addModal = false;
            break;
        case 'sure':

            break;
    }
}

const relateBtnTapHandler = (type) => {
    switch (type) {
        case 'cancel':
            state.relateModal = false;
            break;
        case 'sure':

            break;
    }
}

const modifyBtnTapHandler = (type) => {
    switch (type) {
        case 'cancel':
            state.modifyModal = false;
            break;
        case 'sure':

            break;
    }
}

const addProjectAction = () => {
    state.addModal = true;
}

const batchProjectAction = () => {
    state.batchModal = true;
}

const btnAction = (type, project) => {
    state.currentProject = project;
    switch (type) {
        case 'detail':
            state.detailModal = true;
            break;
        case 'relate':
            // state.relateModal = true;
            router.push('projectTunnel');
            break;
        case 'modify':
            state.modifyModal = true;
            break;
        case 'delete':
            ElMessageBox.confirm(
                '确定要删除该项目信息?',
                '温馨提示',
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }
            ).then(() => {
            }).catch(() => {
            })
            break;
    }
}
</script>
    
<style lang="scss" scoped>
.app-container {
    height: 100%;
    padding: 0 20px;

    .search-section {
        padding: 20px 0;
    }

    .page-section {
        padding-top: 20px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }
}
</style>