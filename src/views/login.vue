<template>
  <div class="login">
    <!-- 视频背景 -->
    <video autoplay muted loop class="background-video">
      <source src="/bg.mp4" type="video/mp4">
    </video>

    <!-- 左侧内容区域 -->
    <div class="left-content">
      <!-- 左上角logo和公司名 -->
      <div class="logo-section">
        <div class="logo-text">
          <div class="company-logo">🏗️ LAB</div>
          <div class="company-name">智能决策</div>
        </div>
      </div>

      <!-- 主标题 -->
      <div class="main-title">
        <h1>隧道数字化平台</h1>
      </div>
    </div>

    <!-- 右侧登录区域 -->
    <div class="right-content">
      <div class="login-container">
        <div class="login-header">
          <div class="header-logo">
            <span class="logo-icon">🏗️</span>
            <span class="header-text">智能决策</span>
          </div>
          <div class="divider-line"></div>
        </div>

        <h2 class="login-title">账号登录</h2>

        <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
          <el-form-item prop="username">
            <el-input
                v-model="loginForm.username"
                type="text"
                size="default"
                auto-complete="off"
                placeholder="账号"
            >
              <template #prefix><svg-icon icon-class="user" class="el-input__icon input-icon" /></template>
            </el-input>
          </el-form-item>

          <el-form-item prop="password">
            <el-input
                v-model="loginForm.password"
                type="password"
                size="default"
                auto-complete="off"
                placeholder="密码"
                @keyup.enter="handleLogin"
            >
              <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template>
            </el-input>
          </el-form-item>

          <el-checkbox v-model="loginForm.rememberMe" class="remember-checkbox">
            <span>记住密码</span>
          </el-checkbox>

          <el-form-item style="width:100%;">
            <el-button
                :loading="loading"
                size="default"
                type="primary"
                class="login-button"
                @click.prevent="handleLogin"
            >
              <span v-if="!loading">登录</span>
              <span v-else>登录中...</span>
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 底部链接 -->
        <div class="login-footer">
          <div class="company-info">武汉精视遥测科技有限公司</div>
          <div class="browser-tip">
            <span>推荐使用</span>
            <span class="browser-link">Chrome浏览器</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router'
import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()
const router = useRouter();
const { proxy } = getCurrentInstance();

const loginForm = ref({
  username: "admin",
  password: "admin123",
  rememberMe: false,
  code: "",
  uuid: ""
});

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: false, trigger: "change", message: "请输入验证码" }]
};

const codeUrl = ref("");
const loading = ref(false);
// 验证码开关
const captchaEnabled = ref(false);
// 注册开关
const register = ref(false);
const redirect = ref(undefined);

function handleLogin() {
  proxy.$refs.loginRef.validate(valid => {
    if (valid) {
      loading.value = true;
      // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        Cookies.set("username", loginForm.value.username, { expires: 30 });
        Cookies.set("password", encrypt(loginForm.value.password), { expires: 30 });
        Cookies.set("rememberMe", loginForm.value.rememberMe, { expires: 30 });
      } else {
        // 否则移除
        Cookies.remove("username");
        Cookies.remove("password");
        Cookies.remove("rememberMe");
      }
      // 调用action的登录方法
      userStore.login(loginForm.value).then(() => {
        router.push({ path: redirect.value || "/" });
      }).catch(() => {
        loading.value = false;
        // 重新获取验证码
        if (captchaEnabled.value) {
          getCode();
        }
      });
    }
  });
}

function getCode() {
  getCodeImg().then(res => {
    captchaEnabled.value = res.captchaEnabled === undefined ?  false: res.captchaEnabled;
    if (captchaEnabled.value) {
      codeUrl.value = "data:image/gif;base64," + res.img;
      loginForm.value.uuid = res.uuid;
    }
  });
}

function getCookie() {
  const username = Cookies.get("username");
  const password = Cookies.get("password");
  const rememberMe = Cookies.get("rememberMe");
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password: password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
  };
}

//getCode();
getCookie();
</script>

<style lang='scss' scoped>
.login {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  display: flex;
}

/* 视频背景 */
.background-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: -1;
}

/* 左侧内容区域 */
.left-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 40px 60px;
  color: white;
  position: relative;
  z-index: 1;
}

.logo-section {
  display: flex;
  align-items: center;
}

.logo-text {
  display: flex;
  align-items: center;
  gap: 15px;
}

.company-logo {
  font-size: 24px;
  font-weight: bold;
  color: #00BFFF;
}

.company-name {
  font-size: 16px;
  color: white;
}

.main-title {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.main-title h1 {
  font-size: 48px;
  font-weight: 300;
  color: white;
  margin: 0 0 20px 0;
  letter-spacing: 2px;
  line-height: 1.2;
}

.subtitle-decoration {
  display: flex;
  align-items: center;
  gap: 15px;
}

.blue-line {
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #00BFFF, #1890ff);
  border-radius: 2px;
}

.blue-dots {
  display: flex;
  gap: 8px;
}

.dot {
  width: 8px;
  height: 8px;
  background: #00BFFF;
  border-radius: 50%;
  display: block;
}

/* 右侧登录区域 */
.right-content {
  width: 380px;
  background: rgba(30, 50, 80, 0.92);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
  border-left: 1px solid rgba(64, 169, 255, 0.2);
}

.login-container {
  width: 280px;
  padding: 50px 0;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.header-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 15px;
}

.logo-icon {
  font-size: 20px;
  color: #00BFFF;
}

.header-text {
  font-size: 16px;
  color: white;
}

.divider-line {
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, #00BFFF, #1890ff);
  margin: 0 auto;
  border-radius: 1px;
}

.login-title {
  color: white;
  font-size: 20px;
  font-weight: 400;
  text-align: center;
  margin: 0 0 30px 0;
}

.login-form {
  .el-form-item {
    margin-bottom: 20px;
  }
}

.remember-checkbox {
  margin: 20px 0;

  :deep(.el-checkbox__label) {
    color: #999;
    font-size: 14px;
  }

  :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
    background-color: #1890ff;
    border-color: #1890ff;
  }
}

.login-button {
  width: 100%;
  height: 36px;
  background: linear-gradient(90deg, #1890ff, #40a9ff);
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(90deg, #40a9ff, #69c0ff);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  }
}

.login-footer {
  text-align: center;
  margin-top: 30px;
  color: #999;
  font-size: 12px;

  .company-info {
    margin-bottom: 8px;
    color: #ccc;
  }

  .browser-tip {
    .browser-link {
      color: #1890ff;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

/* 输入框样式 */
:deep(.el-input__wrapper) {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  box-shadow: none;
  height: 36px;

  &:hover {
    border-color: #1890ff;
  }

  &.is-focus {
    border-color: #1890ff;
  }
}

:deep(.el-input) {
  height: 36px;

  .el-input__inner {
    height: 34px;
    line-height: 34px;
  }
}

:deep(.el-input__inner) {
  color: white;

  &::placeholder {
    color: rgba(255, 255, 255, 0.6);
  }
}

:deep(.el-input__prefix) {
  color: rgba(255, 255, 255, 0.6);
}
</style>
