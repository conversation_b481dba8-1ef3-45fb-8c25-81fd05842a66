<template>
  <div class="app-container" v-loading="loading">
    <el-form
      :inline="true"
      ref="ruleFormRef"
      :model="formInline"
      class="demo-form-inline"
    >
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="formInline.projectName"
          placeholder="项目名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="月份" prop="month">
        <el-date-picker
          v-model="formInline.month"
          value-format="YYYY-MM"
          type="month"
          placeholder="月份"
        />
      </el-form-item>
      <el-form-item label="隶属单位" prop="managementUnit">
        <el-input
          v-model="formInline.managementUnit"
          placeholder="隶属单位"
          clearable
        />
      </el-form-item>
      <el-form-item label="汇报状态" prop="reportStatus">
        <el-select
          v-model="formInline.reportStatus"
          placeholder="汇报状态"
          style="width: 240px"
        >
          <el-option
            v-for="item in reportStatusOption"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">查 询</el-button>
        <el-button @click="resetForm(ruleFormRef)">重 置</el-button>
      </el-form-item>
    </el-form>
    <div>
      <el-button type="primary" @click="addProject">新增项目</el-button>
    </div>
    <div class="table-container">
      <el-scrollbar :height="'calc(100vh - 260px)'">
        <div class="table-box">
          <el-card v-for="(item, index) in dataArr" :key="index" shadow="hover">
            <div class="demo-progress">
              <div class="demo-progress-view">
                <p class="demo-progress-text">项目名称:</p>
                <p class="demo-progress-projectName">{{ item.projectName }}</p>
              </div>
              <div class="demo-progress-view">
                <span class="demo-progress-text">隶属单位:</span>
                <span>{{ item.affiliatedUnit || "--" }}</span>
              </div>
              <div class="demo-progress-view">
                <span class="demo-progress-text">汇报时间:</span>
                <span>{{ item.reportTime || item?.creatorTime }}</span>
              </div>
              <div class="demo-progress-progress">
                <el-button link size="large" type="primary">{{
                  item.reportStatusName
                }}</el-button>
                <br />
                <el-icon size="20" color="#E6A23C"><Setting /></el-icon>
              </div>
              <div class="demo-progress-buts">
                <el-button type="primary" @click="handleStart(item)"
                  >开始汇报</el-button
                >
                <el-button type="danger" @click="deleteAction(item)"
                  >删除项目</el-button
                >
              </div>
            </div>
          </el-card>
        </div>
      </el-scrollbar>
      <div class="page-view">
        <el-pagination
          v-model:current-page="currentPage4"
          v-model:page-size="pageSize4"
          :page-sizes="[100, 200, 300, 400]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="400"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogVisible" title="新增项目" width="500">
      <el-form-item label="项目">
        <el-select
          v-model="form.projectCode"
          placeholder="汇报状态"
          style="width: 240px"
        >
          <el-option
            v-for="item in projectAllList"
            :key="item.projectCode"
            :label="item.projectName"
            :value="item.projectCode"
          />
        </el-select>
      </el-form-item>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handelSubmint"> 保存 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import {
  projectDeliveryList,
  projectDeliveryAdd,
  deliveryRemove,
} from "@/api/project-delivery/index.js";
import { dictLabelFinter } from "@/utils/dictFilter.ts";
import { listProjectAll } from "@/api/system/project";
import {
  generateRandomProjects,
  tableArr,
  reportStatusOption,
} from "./constants.ts";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
const router = useRouter();
const formInline = ref<any>({
  projectName: "",
  managementUnit: "",
  month: "",
  reportStatus: "",
});
const form = ref<any>({
  projectCode: "",
});
let dataArr = ref<any[]>([]);
const pageSize4 = ref(100);
const currentPage4 = ref(4);
const loading = ref(false);
const getList = async () => {
  try {
    loading.value = true;
    const res = await projectDeliveryList({ ...formInline.value });
    dataArr.value = res.rows;
    dataArr.value.forEach((item) => {
      item.reportStatusName = dictLabelFinter(
        reportStatusOption,
        item?.reportStatus || "0"
      );
    });
    loading.value = false;
  } catch (error) {
    loading.value = false;
  }
};
const projectAllList = ref<any[]>([]);
const getListProjectAll = async () => {
  try {
    const res = await listProjectAll();
    projectAllList.value = res;
  } catch (error) {
    console.log(error);
  }
};
// 新增保存
const handelSubmint = async () => {
  try {
    let objs = projectAllList.value.filter(
      (item) => item.projectCode === form.value.projectCode
    )[0];
    const res = await projectDeliveryAdd(objs);
    if (res.code === 200) {
      ElMessage.success("保存成功");
      dialogVisible.value = false;
      getList();
    }
  } catch (error) {
    loading.value = false;
  }
};
const onSubmit = () => {
  getList();
};
const ruleFormRef = ref<any>(null);
const resetForm = (formEl: any | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  getList();
};
const handleSizeChange = (val: number) => {
  console.log(`${val} items per page`);
};
const handleCurrentChange = (val: number) => {
  console.log(`current page: ${val}`);
};
const deleteAction = (row: any) => {
  ElMessageBox.confirm("确定要删除吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    try {
      await deliveryRemove(row?.id);
      getList();
      ElMessage({
        type: "success",
        message: "删除成功",
      });
    } catch (error) {
      console.log(error);
    }
  });
};
const handleStart = (row) => {
  router.push({ path: "/project-delivery/delivery-report",query: { id: row?.id } });
};
const dialogVisible = ref<boolean>(false);
const addProject = () => {
  dialogVisible.value = true;
};
onMounted(() => {
  getList();
  getListProjectAll();
});
</script>

<style lang="scss" scoped>
.demo-form-inline .el-input {
  --el-input-width: 220px;
}

.demo-form-inline .el-select {
  --el-select-width: 220px;
}
.table-container {
  height: calc(100vh - 260px);
  padding: 16px;
}
.table-box {
  width: 100%;
  //   display: flex;
  //   flex-wrap: wrap;
  //   justify-content: space-between; /* 两端对齐 */
  display: grid;
  grid-template-columns: repeat(
    auto-fill,
    minmax(300px, 1fr)
  ); /* 自动填充，最小200px */
  gap: 20px; /* 项目间距 */
  justify-content: space-between;
}
.demo-progress {
  width: 100%;
  height: 180px;
  box-sizing: border-box;
  padding: 16px;
  position: relative;
  .demo-progress-view {
    height: 32px;
    line-height: 32px;
    font-size: 16px;
    display: flex;
    align-items: center;
    .demo-progress-text {
      text-align: justify;
      text-justify: inter-word; /* 单词间调整间距 */
      width: 80px;
      color: #2c2a2a;
    }
    .demo-progress-projectName {
      margin: 0;
      padding: 0;
      width: 150px;
      line-height: 20px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .demo-progress-progress {
    position: absolute;
    right: 10px;
    top: 16px;
    width: 40px;
    height: 60px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .demo-progress-buts {
    height: 60px;
    width: 100%;
    position: absolute;
    left: 0;
    bottom: -60px;
    padding-bottom: 16px;
    // background: rgba($color: #000000, $alpha: 0.1);
    display: flex;
    justify-content: space-around;
    align-items: end;
  }
}
.demo-progress:hover {
  background: rgba($color: #000000, $alpha: 0.1);
  .demo-progress-buts {
    bottom: 0;
  }
}
.page-view {
  width: 100%;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: end;
}
:deep(.el-card__body) {
  padding: 0 !important;
}
</style>