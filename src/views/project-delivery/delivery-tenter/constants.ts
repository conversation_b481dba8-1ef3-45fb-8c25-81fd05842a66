export const generateRandomProjects = (count) => {
    // 可能的取值范围
    const projectNames = [
        '智慧城市建设项目',
        '数据中心升级',
        '网络安全加固',
        'OA系统迁移',
        '客户关系管理系统',
        '财务结算平台',
        '移动办公APP',
        '大数据分析平台'
    ];

    const departments = [
        '信息技术部',
        '项目管理办公室',
        '研发中心',
        '运维支持部',
        '质量保障部',
        '产品管理部'
    ];

    const statuses = ['未汇报', '汇报中', '汇报完'];

    // 生成随机日期（最近30天内）
    function getRandomDate() {
        const now = new Date();
        const past = new Date();
        past.setDate(now.getDate() - 30);

        const randomTime = past.getTime() + Math.random() * (now.getTime() - past.getTime());
        const randomDate = new Date(randomTime);

        return randomDate.toISOString().split('T')[0]; // 返回YYYY-MM-DD格式
    }

    // 生成指定数量的随机项目
    const projects: any = [];
    for (let i = 0; i < count; i++) {
        projects.push({
            id: i + 1,
            projectName: projectNames[Math.floor(Math.random() * projectNames.length)],
            department: departments[Math.floor(Math.random() * departments.length)],
            reportDate: getRandomDate(),
            status: statuses[Math.floor(Math.random() * statuses.length)],
            progress: Math.floor(Math.random() * 100) // 额外添加的进度字段
        });
    }

    return projects;
}

export const tableArr = [
    {
        projectName: "2023安徽池州定检",
        department: '池州高速公路管理中心',
        reportDate: "2025-03-07",
        status: '未汇报'
    },
    {
        projectName: "湖北交投养护检测1标段",
        department: '湖北交投',
        reportDate: "2025-04-09",
        status: '汇报中'
    },
    {
        projectName: "2022芜湖定检项目",
        department: '安徽交控',
        reportDate: "2025-06-12",
        status: '汇报完'
    }
]
export const reportStatusOption = [
    {
        value: 0,
        label: '未汇报'
    },
    {
        value: 1,
        label: '汇报中'
    },
    {
        value: 2,
        label: '汇报完'
    }
]

