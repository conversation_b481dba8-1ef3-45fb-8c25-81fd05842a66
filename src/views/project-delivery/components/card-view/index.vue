<template>
  <div class="card-view">
    <div class="ep-card">{{ titleText }}</div>
    <div class="ep-card-content">
      <slot></slot>
    </div>
  </div>
</template>
<script lang="ts" setup>
const props = defineProps({
  titleText: {
    type: String,
    default: "",
  },
});
</script>
<style lang="scss" scoped>
.card-view {
  box-sizing: border-box;
  padding: 12px 0;
  border-radius: 8px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  &:hover {
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
  }
  .ep-card {
    height: 24px;
    line-height: 24px;
    border-left: 6px solid #409eff;
    padding-left: 12px;
    font-size: 16px;
  }
}
</style>