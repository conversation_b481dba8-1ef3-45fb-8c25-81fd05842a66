<template>
  <div class="project-introduction">
    <div id="gisMapProjectIntroduction"></div>
    <div class="project-view">
      <div class="project-title">项目结论</div>
      <div class="project-text">
        针对本次检查结果，建议按养护等级一级对隧道1进行日常养护，应坚持进行日常巡查，定期进行洞内清洁和结构物经常性检查，此外，依据病害的类型、规模和特点，提出的处置建议如下：
        （1）设立观测标记加强对衬砌裂缝的观测，若裂缝存在发展建议根据检查结果，对未出现渗水的裂缝进行封闭处理，裂缝的封闭可以参考以下建议：对缝宽小于0.15mm的，建议采用树脂封闭胶进行涂刷封闭处理；对缝宽介于0.15mm-0.30mm时，建议采用树脂灌缝处理；当裂缝大于0.30mm时，应将裂缝凿开，刷净缝隙中的松块或粉尘，然后立模填补环氧砂浆或高强度水泥砂浆；
        （2）根据检查结果加强对存在渗水痕迹部位的日常观测，并及时进行堵漏防水处理；
        （3）对防火涂料脱落部位进行修补。
      </div>
    </div>
  </div>
</template>
<script  setup>
import { ref, onMounted, onUnmounted, watch } from "vue";
const map = ref();
const initMap = async (lat = 117.014855, lng = 31.859474, zoom = 10) => {
  map.value = new BMapGL.Map("gisMapProjectIntroduction"); //创建地图实例
  let point = new BMapGL.Point(lat, lng); //地图中心点
  map.value.centerAndZoom(point, zoom); //地图初始化，同时设置地图展示级别
  //   map.value.enableScrollWheelZoom(true); //使用鼠标滚轮控制缩放
};

onMounted(() => {
  nextTick(() => {
    initMap();
  });
});
</script>
<style lang="scss" scoped>
#gisMapProjectIntroduction {
  width: calc(100vw - 180px);
  height: calc(100vh - 80px);
}
.project-introduction {
  position: relative;
  .project-view {
    width: calc(100vw - 180px);
    height: calc(100vh - 80px);
    position: absolute;
    left: 0;
    top: 0;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding: 100px 200px;
  }
  .project-title {
    // left: 289px;
    // top: 169px;
    // width: 1584px;
    height: 80px;
    opacity: 1;
    border-radius: 30px;
    line-height: 80px;
    padding-left: 34px;
    font-size: 34px;
    background: linear-gradient(
      90deg,
      rgba(166, 166, 166, 1) 0%,
      rgba(229, 229, 229, 0) 100%
    );
    margin-bottom: 60px;
  }
  .project-text {
    height: 434px;
    opacity: 1;
    border-radius: 30px;
    font-size: 28px;
    padding: 20px;
    line-height: 50px;
    background: linear-gradient(
      90deg,
      rgba(204, 204, 204, 1) 0%,
      rgba(229, 229, 229, 0) 100%
    );
  }
}
</style>