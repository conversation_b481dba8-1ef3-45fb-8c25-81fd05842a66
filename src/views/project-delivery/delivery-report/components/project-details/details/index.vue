<template>
  <el-dialog width="1200" v-model="open" @close="handelClose" append-to-body>
    <template #header>
      <div class="tabs-view">
        <span
          v-for="item in tabsConfig"
          :key="item.templateName"
          :class="[item.templateName === stores.templateName && 'tabs-active']"
          @click="tabsChange(item)"
          >{{ item.label }}</span
        >
      </div>
    </template>
    <!-- <keep-alive> -->
      <component :is="layouts[stores.templateName]" :tunnelId="tunnelId"></component>
    <!-- </keep-alive> -->
  </el-dialog>
</template>
  
  <script lang="ts" setup>
import { ref, provide, defineAsyncComponent } from "vue";

import { tabsConfig } from "./constants";
// 引入组件
const layouts: any = {
  // 隧道概况================
  tunnelOverview: defineAsyncComponent(
    () => import("./components/tunnel-overview/index.vue")
  ),
  // 检测评定
  testingEvaluation: defineAsyncComponent(
    () => import("./components/testing-evaluation/index.vue")
  ),
  // 技术状况历史
  technicalHistory: defineAsyncComponent(
    () => import("./components/technical-history/index.vue")
  ),
  // 主要病害历史
  majorDiseaseHistory: defineAsyncComponent(
    () => import("./components/major-disease-history/index.vue")
  ),
  // 专项检测
  specialTesting: defineAsyncComponent(
    () => import("./components/special-testing/index.vue")
  ),
};
const tunnelId = ref<string>("");
provide("tunnelId", tunnelId.value);
const stores = ref<any>({
  label: "隧道概况",
  templateName: "tunnelOverview",
});
const open = ref<boolean>(false);
// 打开弹窗

const openDialog = async (row: any) => {
  tunnelId.value = row.tunnelId;
  open.value = true;
};

const handelClose = () => {};
const tabsChange = (val) => {
  stores.value = val;
};

// 暴露变量
defineExpose({
  openDialog,
});
</script>
<style lang="scss" scoped>
.tabs-view {
  display: flex;
  justify-content: center;
  span {
    font-size: 18px;
    padding: 6px 12px;
    cursor: pointer;

    border-bottom: 1px solid #409eff;
  }
  .tabs-active {
    font-weight: 600;
    border-bottom: 2px solid #409eff;
    background: linear-gradient(#fff 0%, #2a79d4 100%);
  }
}
</style>
  