<template>
  <div class="technical-history">
    <div class="th-tabs">
      <TabsType :typeTabs="tabsTypes" @change="tabsChange" />
      &nbsp;&nbsp;&nbsp;
      <TabsType :typeTabs="viewTypes" @change="tabsViewChange" />
    </div>
    <br/>
    <el-table
      :data="tableData"
      :style="{ height: '45vh' }"
    >
      <el-table-column
        v-for="(item, index) in tableConfig"
        :key="index"
        :prop="item.prop"
        :label="item.label"
        :min-width="item?.width || 120"
      >
      </el-table-column>
    </el-table>
  </div>
</template>
<script lang="ts" setup>
import { ref, defineAsyncComponent } from "vue";
import { tabsTypes, viewTypes,tableConfig } from "./constants.ts";
const TabsType = defineAsyncComponent(
  () => import("@/components/TabsType/index.vue")
);
const tableData = ref<any[]>([]);
const tabsChange = (val: any) => {
  console.log(val);
};
const tabsViewChange = (val: any) => {
  console.log(val);
};
</script>
<style lang="scss" scoped>
.technical-history {
  .th-tabs {
    display: flex;
    justify-content: right;
  }
}
</style>