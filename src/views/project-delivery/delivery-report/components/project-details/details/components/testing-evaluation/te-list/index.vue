<template>
  <div class="te-list" v-loading="loading">
    <!-- <div>
      <el-button type="primary">新增</el-button>
      <el-button type="success">修改</el-button>
      <el-button type="danger">删除</el-button>
    </div> -->
    <br />
    <el-table
      :data="tableData"
      @selection-change="handleSelectionChange"
      :style="{ height: '45vh' }"
    >
      <el-table-column type="selection" fixed="left" width="55" />
      <el-table-column type="index" fixed="left" label="序号" width="60" />
      <el-table-column
        v-for="(item, index) in tableConfig"
        :key="index"
        :prop="item.prop"
        :label="item.label"
        :min-width="item?.width || 120"
      >
      </el-table-column>
    </el-table>

    <br />
    <div class="pagination-section">
      <el-pagination
        v-model:current-page="pages"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="getList"
        @current-change="getList"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { tableConfig } from "./constants";
import { getDeliveryCheckInfoDiseaseList } from "@/api/project-delivery/index";
import { useRoute } from "vue-router";
const route = useRoute(); // 获取路由对象:cite[2]:cite[5]:cite[6]
const props = defineProps({
  enumType: {
    type: Number,
    default: 1,
  },
});
const total = ref(0);
const pages = ref(1);
const pageSize = ref(10);
const tableData = ref<any>([]);
const selectionData = ref([]);

const handleSelectionChange = (val: any) => {
  selectionData.value = val;
};
const tunnelId = ref<any>("");
const diseaseTypeCode = ref<any>("");
const loading = ref(false);
const getList = async (tunnel = "", typeCode = "") => {
  try {
    diseaseTypeCode.value = typeCode;
    tunnelId.value = tunnel;
    loading.value = true;
    const res = await getDeliveryCheckInfoDiseaseList({
      deliveryId: Number(route.query.id), //汇报id
      diseaseTypeCode: diseaseTypeCode.value, // 病害枚举编码
      tunnelId: tunnelId.value, // 隧道id
    });
    console.log(res);
    tableData.value = res.rows;
    total.value = res.total;
    tableData.value.forEach((item) => {
      item.starting = `${item.startingStation}-${item.endingStation}`;
    });
    loading.value = false;
  } catch (err) {
    loading.value = false;
    console.log(err);
  }
};
defineExpose({
  getList,
});
</script>