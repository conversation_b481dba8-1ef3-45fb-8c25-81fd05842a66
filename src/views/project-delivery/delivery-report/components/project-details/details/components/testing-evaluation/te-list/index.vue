<template>
  <div class="te-list">
    <!-- <div>
      <el-button type="primary">新增</el-button>
      <el-button type="success">修改</el-button>
      <el-button type="danger">删除</el-button>
    </div> -->
    <br />
    <el-table
      :data="tableData"
      @selection-change="handleSelectionChange"
      :style="{ height: '45vh' }"
    >
      <el-table-column type="selection" fixed="left" width="55" />
      <el-table-column type="index" fixed="left" label="序号" width="60" />
      <el-table-column
        v-for="(item, index) in tableConfig"
        :key="index"
        :prop="item.prop"
        :label="item.label"
        :min-width="item?.width || 120"
      >
      </el-table-column>
    </el-table>

    <br />
    <div class="pagination-section">
      <el-pagination
        v-model:current-page="pages"
        v-model:page-size="pageSize"
        :page-sizes="[100, 200, 300, 400]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="getList"
        @current-change="getList"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { tableConfig } from "./constants";
const total = ref(1);
const pages = ref(1);
const pageSize = ref(10);
const tableData = ref([{}]);
const selectionData = ref([]);
const handleSelectionChange = (val: any) => {
  selectionData.value = val;
};
const getList = () => {};
</script>