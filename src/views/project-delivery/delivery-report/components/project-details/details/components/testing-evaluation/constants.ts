export const peiOtopnsFilter = (data) => {
    return {
        tooltip: {
            trigger: 'item'
        },
        legend: {
            top: '5%',
            left: 'center'
        },
        series: [
            {
                name: '',
                type: 'pie',
                radius: '50%',
                data,
                // data: [
                //     { value: 1048, name: '环向裂缝', },
                //     { value: 735, name: '纵向裂缝' },
                //     { value: 580, name: '斜向裂缝' },
                //     { value: 484, name: '泛碱' },
                // ],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                label: {
                    show: true,
                    // position: 'inside',  // 关键设置，标签显示在扇形内部
                    formatter: '{b}: {c} {d}%'  // {b}表示名称，{d}表示百分比
                }
            }
        ]
    }

}
export const tabsTypes = [
    {
        name: "土建设施",
        value: "1",
    },
    {
        name: "其他工程设施",
        value: "2",
    },
]