<template>
  <el-dialog
    width="600"
    :title="'专项检测详情'"
    v-model="open"
    @close="handelClose"
    append-to-body
  >
    <el-form ref="ruleFormRef" :model="form" label-width="auto">
      <el-form-item label="隧道名称" prop="name">
        <el-input
          v-model="form.name"
          :style="{ width: '200px' }"
          placeholder="隧道名称"
        />
      </el-form-item>
      <el-form-item label="隧道幅别" prop="tunnelType">
        <el-select
          v-model="form.tunnelType"
          :style="{ width: '200px' }"
          placeholder="隧道幅别"
        >
          <el-option label="左幅" value="shanghai" />
          <el-option label="右幅" value="beijing" />
        </el-select>
      </el-form-item>
      <el-form-item label="专项检查项目" prop="projrct">
        <el-select
          v-model="form.projrct"
          :style="{ width: '200px' }"
          placeholder="专项检查项目"
        >
          <el-option label="项目1" value="shanghai" />
          <el-option label="项目2" value="beijing" />
        </el-select>
      </el-form-item>
      <el-form-item label="检查内容" prop="projrct">
        <el-input v-model="form.desc" type="textarea" />
      </el-form-item>
      <el-form-item label="起点桩号" prop="projrct">
        <div>
          <el-input v-model="form.desc" :style="{ width: '50px' }" />&nbsp;
          <el-input v-model="form.desc" :style="{ width: '100px' }" type="" />&nbsp;+&nbsp;
          <el-input v-model="form.desc" :style="{ width: '100px' }" type="" />
        </div>
      </el-form-item>
      <el-form-item label="讫点桩号" prop="projrct">
        <div>
          <el-input v-model="form.desc" :style="{ width: '50px' }" />&nbsp;
          <el-input v-model="form.desc" :style="{ width: '100px' }" type="" />&nbsp;+&nbsp;
          <el-input v-model="form.desc" :style="{ width: '100px' }" type="" />
        </div>
      </el-form-item>
      <el-form-item label="建议措施" prop="projrct">
        <el-input v-model="form.desc" type="textarea" />
      </el-form-item>
    </el-form>
  </el-dialog>
</template>
  
  <script lang="ts" setup>
import { ref } from "vue";
// import { tabsConfig } from "./constants";
const open = ref<boolean>(false);
const form = ref<any>({});
// 打开弹窗
const openDialog = async (row: any) => {
  console.log(row);
  open.value = true;
};
const loading = ref<boolean>(false);

const handelClose = () => {};

// 暴露变量
defineExpose({
  openDialog,
});
</script>
<style lang="scss" scoped>
.tabs-view {
  display: flex;
  justify-content: center;
  span {
    font-size: 18px;
    padding: 6px 12px;
    cursor: pointer;

    border-bottom: 1px solid #409eff;
  }
  .tabs-active {
    font-weight: 600;
    border-bottom: 2px solid #409eff;
    background: linear-gradient(#fff 0%, #2a79d4 100%);
  }
}
</style>
  