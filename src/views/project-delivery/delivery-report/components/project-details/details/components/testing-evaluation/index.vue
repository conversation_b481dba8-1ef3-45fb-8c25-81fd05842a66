<template>
  <div class="testing-evaluation">
    <div class="to-title-view">
      <h3 class="to-title" @click="teListShow = false">
        大邀岭隧道评定结果-右幅
      </h3>
      <TabsType
        v-show="teListShow"
        :typeTabs="tabsTypes"
        @change="tabsChange"
      />
    </div>
    <div v-show="!teListShow" class="te-content">
      <div class="te-content-view">
        <cardView :titleText="'整体等级'">
          <div class="te-content-table">
            <div class="te-content-table-view">
              <span
                class="te-content-table-view-title te-content-table-view-border"
                @click="handleClick('土建结构')"
                >{{ "土建结构" }}</span
              >
              <span
                class="te-content-table-view-title"
                @click="handleClick('其他工程设施')"
                >{{ "其他工程设施" }}</span
              >
            </div>
            <div class="te-content-table-view">
              <span>{{ "评分" }}</span>
              <span class="te-content-table-view-border">{{
                "技术状况等级"
              }}</span>
              <span>{{ "评分" }}</span>
              <span>{{ "技术状况等级" }}</span>
            </div>
            <div class="te-content-table-view">
              <span>{{ form?.civilStructureScore || "-" }}</span>
              <span
                class="te-content-table-view-title te-content-table-view-border"
                >{{ form?.civilStructureLevel || "-" }}</span
              >
              <span>{{ form?.appurtenanceScore || "-" }}</span>
              <span class="te-content-table-view-title">{{
                form?.otherLevel || "-"
              }}</span>
            </div>
          </div>
        </cardView>
        <cardView :titleText="'衬砌病害统计'">
          <EchartsComp
            ref="refEchartsCompPei"
            :option="peiOtopns"
            :height="'300px'"
          ></EchartsComp>
        </cardView>
      </div>
      <div class="te-content-view">
        <cardView :titleText="'分项状况值'">
          <div class="te-content-fx">
            <el-select
              v-model="value"
              placeholder="请选择"
              style="width: 200px"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <div class="tabs-view">
              <span
                v-for="item in [1, 2, 3, 4]"
                :key="item"
                :class="[conditionValue === item && 'tabs-active']"
                @click="tabsValueChange(item)"
                >{{ item }}</span
              >
            </div>
          </div>
        </cardView>
        <cardView :titleText="'病害图示'">
          <template #right>
            <!-- <el-select
              v-model="diseaseSmallType"
              placeholder="请选择"
              style="width: 200px"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select> -->
            <el-tree-select
              v-model="diseaseSmallType"
              :data="diseaseSmallTypeOptions"
              :render-after-expand="false"
              style="width: 200px"
            />
          </template>
          <div class="te-content-tl">
            <div class="te-content-tl-view">
              <div
                class="te-content-tl-view-item"
                v-for="item in new Array(6)"
                :key="item"
              >
                <img
                  class="te-icon"
                  src="@/assets/images/delivery-report/bihai.png"
                  alt=""
                />
                <p class="te-content-tl-view-item-text">
                  {{ "k0+100 环向裂缝" }}
                </p>
              </div>
            </div>
            <div class="pagination-section">
              <el-pagination
                v-model:current-page="pages"
                v-model:page-size="pageSize"
                :page-sizes="[6, 12, 24, 48]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                @size-change="getList"
                @current-change="getList"
              />
            </div>
          </div>
        </cardView>
      </div>
    </div>
    <teList v-show="teListShow" ref="refTeList" />
  </div>
</template>
<script lang="ts" setup>
import { ref, defineAsyncComponent, onMounted } from "vue";
import {
  getDeliveryCheckInfo,
  getDiseaseTypeTree,
  getDeliveryCheckInfoRightPic,
} from "@/api/project-delivery";
import { peiOtopnsFilter, tabsTypes } from "./constants";
const cardView = defineAsyncComponent(() => import("../card/index.vue"));
const teList = defineAsyncComponent(() => import("./te-list/index.vue"));
const EchartsComp = defineAsyncComponent(
  () => import("@/views/home/<USER>/echarts-comp/index.vue")
);
const TabsType = defineAsyncComponent(
  () => import("@/components/TabsType/index.vue")
);
const props = defineProps({
  tunnelId: {
    type: String,
    default: "",
  },
});
const getImageUrl = (name) => {
  return new URL(
    `../../../../../../assets/images/delivery-report/${name}`,
    import.meta.url
  ).href;
};
const form = ref<any>({
  name: "",
});
const value = ref("");
const options = ref<any[]>([]);
const tabsValue = ref<any>(1);
const total = ref(1);
const pages = ref(1);
const pageSize = ref(10);
const teListShow = ref<boolean>(false);
const peiOtopns = ref<any>({});
const refEchartsCompPei = ref<any>(null);
const getList = async () => {
  try {
    const res = await getDeliveryCheckInfo(props.tunnelId);
    form.value = res.data;
    let liningPieList = res.data?.liningPieList;
    liningPieList.map((item) => {
      item.value = item.typeNum;
      item.name = item.typeName;
    });
    peiOtopns.value = peiOtopnsFilter(liningPieList);
    refEchartsCompPei.value.updataMap(peiOtopns.value);
  } catch (err) {
    console.log(err);
  }
};
const tabsChange = (val) => {
  console.log(val);
};
const handleClick = (val) => {
  teListShow.value = true;
  console.log(val);
};
// 小类结构树
const diseaseSmallTypeOptions = ref<any>([]);
const _getDiseaseTypeTree = async () => {
  try {
    const res = await getDiseaseTypeTree();
    console.log(res);
    diseaseSmallTypeOptions.value = convertTreeData(res.data);
  } catch (err) {
    console.log(err);
  }
};
const diseaseSmallType = ref<string>("");
const conditionValue = ref<any>(1);
const diseaseTypeCode = ref<string>("");
// 获取项目明细-检测评定数据-图片列表数据
const _getDeliveryCheckInfoRightPic = async () => {
  try {
    const res = await getDeliveryCheckInfoRightPic({
      conditionValue: conditionValue.value,
      diseaseSmallType: diseaseSmallType.value,
      diseaseTypeCode: diseaseTypeCode.value,
    });
    console.log(res);
  } catch (err) {
    console.log(err);
  }
};
function convertTreeData(treeData: any) {
  return treeData.map((item: any) => {
    const newNode = {
      value: item.code,
      label: item.name,
    };

    // 保留其他属性（可选）
    Object.keys(item).forEach((key) => {
      if (key !== "code" && key !== "name") {
        newNode[key] = item[key];
      }
    });

    // 递归处理子节点
    if (item.children && Array.isArray(item.children)) {
      newNode.children = convertTreeData(item.children);
    }

    return newNode;
  });
}
const tabsValueChange = (val) => {
  conditionValue.value = val;
  _getDeliveryCheckInfoRightPic();
};

onMounted(() => {
  getList();
  _getDiseaseTypeTree();
  _getDeliveryCheckInfoRightPic();
});
</script>
<style lang="scss" scoped>
.testing-evaluation {
  .to-title-view {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .to-title {
    font-size: 20px;
    color: #006fed;
  }
  .te-content {
    display: flex;
    .te-content-view {
      flex: 1;
    }
  }
  .te-content-table {
    padding-bottom: 16px;
    padding-right: 16px;

    .te-content-table-view {
      display: flex;
      justify-content: space-between;
      border-left: 1px solid #ccc;
      border-top: 1px solid #ccc;
      text-align: center;
      span {
        flex: 1;
        line-height: 32px;
        border-bottom: 1px solid #ccc;
        border-right: 1px solid #ccc;
        color: #000;
        font-weight: bold;
      }
      .te-content-table-view-title {
        color: #006fed;
      }
      .te-content-table-view-border {
        border-right: 2px solid #ccc;
      }
    }
  }
  .te-content-fx {
    display: flex;
    margin-bottom: 16px;
    align-items: center;
    .tabs-view {
      min-width: 240px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-left: 24px;
      border-radius: 5px;
      border: 1px solid #006fed;
      background: #e0eaf5;
      color: #006fed;
      span {
        line-height: 30px;
        padding: 0 24px;
        border-radius: 4px;
        cursor: pointer;
      }
      .tabs-active {
        border: 1px solid #006fed;
        background: #006fed;
        color: #fff;
      }
    }
  }
  .te-content-tl {
    .te-content-tl-view {
      display: flex;
      flex-wrap: wrap;
      .te-content-tl-view-item {
        padding: 16px;
        .te-icon {
          height: 160px;
          width: 180px;
        }
        .te-content-tl-view-item-text {
          padding: 0;
          margin: 0;
          background: #c1c1c1;
          padding: 3px 12px;
        }
      }
    }
  }
}
</style>