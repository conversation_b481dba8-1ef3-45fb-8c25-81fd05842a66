<template>
  <div class="special-testing">
    <el-form :model="form" ref="ruleFormRef" label-width="auto" inline>
      <el-form-item label="隧道名称" prop="name">
        <el-input v-model="form.name" placeholder="隧道名称" />
      </el-form-item>
      <el-form-item label="隧道幅别" prop="tunnelType">
        <el-select v-model="form.tunnelType" placeholder="隧道幅别">
          <el-option label="左幅" value="shanghai" />
          <el-option label="右幅" value="beijing" />
        </el-select>
      </el-form-item>
      <el-form-item label="专项检查项目" prop="projrct">
        <el-select v-model="form.projrct" placeholder="专项检查项目">
          <el-option label="项目1" value="shanghai" />
          <el-option label="项目2" value="beijing" />
        </el-select>
      </el-form-item>
      <el-form-item label="">
        <el-button type="primary" @click="onSubmit">搜索</el-button>
        <el-button @click="resetForm(ruleFormRef)">重置</el-button>
      </el-form-item>
    </el-form>
    <br />
    <el-button type="primary">批量导入</el-button>
    <br />
    <el-table :data="tableData" :style="{ height: '45vh' }">
      <el-table-column
        v-for="(item, index) in tableConfig"
        :key="index"
        :prop="item.prop"
        :label="item.label"
        :min-width="item?.width || 120"
      >
      </el-table-column>
      <el-table-column fixed="right" label="操作">
        <template #default="{ row }">
          <el-button type="primary" @click="showDetails(row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <br />
    <div class="pagination-section">
      <el-pagination
        v-model:current-page="pages"
        v-model:page-size="pageSize"
        :page-sizes="[100, 200, 300, 400]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="getList"
        @current-change="getList"
      />
    </div>
    <stDetails ref="refStDetails" />
  </div>
</template>
<script lang="ts" setup>
import { ref, defineAsyncComponent } from "vue";
import { tableConfig } from "./constants";
const stDetails = defineAsyncComponent(() => import("./st-details/index.vue"));
const form = ref<any>({});
const total = ref(1);
const pages = ref(1);
const pageSize = ref(10);
const tableData = ref<any[]>([{}]);
const onSubmit = () => {
  console.log(form.value);
};
const ruleFormRef = ref<any>();
const resetForm = (formEl: any | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
};
const refStDetails = ref<any>();
const showDetails = (row) => {
  refStDetails.value.openDialog(row);
};
const getList = () => {};
</script>
<style lang="scss" scoped>
.special-testing {
    .pagination-section {
        display: flex;
        justify-content: flex-end;
    }
}
</style>