<template>
  <div class="tunnel-overview">
    <h3 class="to-title">{{ `${form.tunnelName}详情-${form.hole}` }}</h3>
    <div class="to-content">
      <div class="to-left">
        <card-view :titleText="'隧道卡片'">
          <el-form :model="form" disabled :size="'small'" label-width="auto">
            <el-form-item label="路线编号">
              <el-input
                v-model="form.tunnelRoadNumber"
                clearable
                placeholder="请输入路线编号"
              />
            </el-form-item>
            <el-form-item label="路段名称">
              <el-input
                v-model="form.affiliatedRoadSection"
                clearable
                placeholder="请输入路线编号"
              />
            </el-form-item>
            <el-form-item label="起点桩号">
              <div class="to-input">
                <el-input :style="{ width: '50px' }" v-model="edit" />
                &nbsp;
                <el-input
                  :style="{ width: '100px' }"
                  v-model="form.startingStation"
                />
                <!-- &nbsp; + &nbsp;
                <el-input :style="{ width: '100px' }" v-model="form.endingStation" /> -->
              </div>
            </el-form-item>
            <el-form-item label="讫点桩号">
              <div class="to-input">
                <el-input :style="{ width: '50px' }" v-model="edit" />
                &nbsp;
                <el-input
                  :style="{ width: '100px' }"
                  v-model="form.endingStation"
                />
                <!-- &nbsp; + &nbsp;
                <el-input :style="{ width: '100px' }" v-model="form.name" /> -->
              </div>
            </el-form-item>
            <el-form-item label="隶属单位">
              <el-input v-model="form.affiliatedUnit" />
            </el-form-item>
            <el-form-item label="通车时间">
              <el-input v-model="form.modifierTime" />
            </el-form-item>
          </el-form>
        </card-view>
        <card-view :titleText="'结构技术数据'">
          <el-form :model="form" disabled :size="'small'" label-width="auto">
            <el-form-item label="净度(m)">
              <el-input
                v-model="form.netWidth"
                :style="{ width: '140px' }"
                clearable
                placeholder="请输入净度"
              />
            </el-form-item>
            <el-form-item label="净高(m)">
              <el-input
                v-model="form.netHeight"
                :style="{ width: '140px' }"
                clearable
                placeholder="请输入净高"
              />
            </el-form-item>
            <el-form-item label="初砌类型">
              <el-input
                v-model="form.initialLiningType"
                :style="{ width: '140px' }"
                clearable
                placeholder=""
              />
            </el-form-item>
            <el-form-item label="路面荷载">
              <el-input
                v-model="form.pavementLoad"
                :style="{ width: '140px' }"
                clearable
                placeholder=""
              />
            </el-form-item>
            <el-form-item label="设计时速(km/h)">
              <el-input
                v-model="form.designSpeed"
                :style="{ width: '140px' }"
                clearable
                placeholder=""
              />
            </el-form-item>
            <el-form-item label="通风方式">
              <el-input
                v-model="form.ventilationMethod"
                :style="{ width: '140px' }"
                clearable
                placeholder=""
              />
            </el-form-item>
            <el-form-item label="洞门类型">
              <div class="select-box">
                <span>进口</span>
                &nbsp;
                <el-input
                  v-model="form.entranceType"
                  :style="{ width: '120px' }"
                  clearable
                  placeholder=""
                />
                &nbsp; &nbsp;
                <span>出口</span>
                &nbsp;
                <el-input
                  v-model="form.exitType"
                  :style="{ width: '120px' }"
                  clearable
                  placeholder=""
                />
              </div>
            </el-form-item>
          </el-form>
        </card-view>
      </div>
      <div class="to-right">
        <div class="to-right-img">
          <img
            v-for="(item, index) in 4"
            :key="index"
            src="@/assets/images/delivery-report/suidaoimage.png"
            alt=""
          />
        </div>
        <!-- <div class="to-right-btn">
          <el-button type="primary">编辑</el-button>
          <el-button type="success">保存</el-button>
        </div> -->
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  ref,
  inject,
  defineAsyncComponent,
  onMounted,
  watch,
  nextTick,
} from "vue";
import { getProjectDetailTunnelInfo } from "@/api/project-delivery";
const cardView = defineAsyncComponent(() => import("../card/index.vue"));
const edit = ref("YK");
const props = defineProps({
  tunnelId: {
    type: String,
    default: "",
  },
});
const form = ref<any>({
  name: "",
  tunnelRoadNumber: "",
  affiliatedRoadSection: "",
});

const tunnelId = inject("tunnelId");
const loading = ref<boolean>(false);
const getTunnelInfo = async () => {
  try {
    loading.value = true;
    const res = await getProjectDetailTunnelInfo(props.tunnelId);
    if (res.code === 200) {
      console.log(res);
      form.value = res.data;
      loading.value = false;
    }
  } catch (err) {
    console.log(err);
  }
};
onMounted(() => {
  nextTick(() => {
    getTunnelInfo();
  });
});
watch(
  () => props.tunnelId,
  () => {
    nextTick(() => {
      getTunnelInfo();
    });
  }
);
</script>
<style lang="scss" scoped>
.to-title {
  font-size: 20px;
  color: #006fed;
}
.to-content {
  display: flex;
  .to-left {
    width: 40%;
  }
  .to-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    .to-right-img {
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      padding: 16px;
      img {
        width: 50%;
        height: 260px;
        padding: 12px;
      }
    }
    .to-right-btn {
      text-align: right;
    }
  }
}
.to-input {
  display: flex;
}
.select-box {
  display: flex;
}
</style>