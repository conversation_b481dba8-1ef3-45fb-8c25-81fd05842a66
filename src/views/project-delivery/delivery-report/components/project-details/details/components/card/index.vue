<template>
  <div class="pd-card">
    <div class="pd-card-title">
      <span class="pd-card-title-text">{{ titleText }}</span>
      <slot name="right" />
    </div>
    <br />
    <div class="pd-card-content">
      <slot />
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  titleText: {
    type: String,
    default: "---",
  },
});
</script>
<style lang="scss" scoped>
.pd-card {
  .pd-card-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .pd-card-title-text {
      font-size: 18px;
      padding: 4px 12px;
      padding-right: 30px;
      background: linear-gradient(to right, #c1c1c1, #fff);
      color: #006FED;
      border-radius: 2px;
      font-weight: 600;
    }
  }
}
</style>