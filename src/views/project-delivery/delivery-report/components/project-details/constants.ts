import { number } from "echarts";

export const optionJgxs = (data) => {
    return {
        grid: {
            top: '3%',
            left: '3%',
            right: '5%',
            bottom: '3%',
            containLabel: true
        },
        legend: {
            type: 'scroll',
            orient: 'vertical',
            right: '10%',
            top: 'center',
            width: '40%',
            align: 'auto',
            itemWidth: 14,
            itemHeight: 14,
        },
        toolbox: {
            show: true
        },
        tooltip: {
            trigger: 'item',
            formatter: '{b}: {c} ({d}%)' // {b}名称, {c}值, {d}百分比
        },
        label: {
            alignTo: 'edge',
            formatter: '{name|{b}}\n{time|{c}    {d}%}',
            minMargin: 5,
            edgeDistance: 10,
            lineHeight: 15,
            rich: {
                time: {
                    fontSize: 10,
                    color: '#999'
                }
            }
        },
        series: [
            {
                name: '',
                type: 'pie',
                radius: [30, 80],
                center: ['50%', '50%'],
                roseType: 'area',
                itemStyle: {
                    borderRadius: 8
                },
                data,
                // data: [
                //     { value: 40, name: '分离式' },
                //     { value: 38, name: '联共式' },
                //     { value: 32, name: '整体式' },
                // ]
            }
        ]
    };
}

export const optionSdgm = (yAxisData,seriesData) => {
    return {
        grid: {
            top: '5%',
            left: '3%',
            right: '5%',
            bottom: '3%',
            containLabel: true
        },
        tooltip: {
            trigger: 'item'
        },
        xAxis: {
            type: 'value',
        },
        yAxis: {
            type: 'category',
            data:yAxisData
            // data: ['短隧道', '中隧道', '长随', '特长隧道']
        },
        series: [
            {
                data:seriesData,
                // data: [200, 150, 120, 70],
                type: 'bar',
                showBackground: true,
                barWidth: 30,// 固定宽度（像素）
                backgroundStyle: {
                    color: 'rgba(180, 180, 180, 0.2)'
                }
            }
        ]
    };

};
export const optionYear = {
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
        }
    },
    legend: {},
    grid: {
        top: '15%',
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
    },
    xAxis: {
        type: 'category',
        data: ['2021', '2022', '2023', '2024', '2025']
    },
    yAxis: {
        type: 'value',
    },
    series: [
        {
            name: '1类',
            type: 'bar',
            stack: 'total',
            label: {
                show: true
            },
            emphasis: {
                focus: 'series'
            },
            data: [320, 302, 301, 334, 390, 330, 320],
            barWidth: 40,// 固定宽度（像素）
        },
        {
            name: '2类',
            type: 'bar',
            stack: 'total',
            label: {
                show: true
            },
            emphasis: {
                focus: 'series'
            },
            data: [120, 132, 101, 134, 90, 230, 210],
            barWidth: 40,// 固定宽度（像素）
        },
        {
            name: '3类',
            type: 'bar',
            stack: 'total',
            label: {
                show: true
            },
            emphasis: {
                focus: 'series'
            },
            data: [220, 182, 191, 234, 290, 330, 310],
            barWidth: 40,// 固定宽度（像素）
        },
        {
            name: '4类',
            type: 'bar',
            stack: 'total',
            label: {
                show: true
            },
            emphasis: {
                focus: 'series'
            },
            data: [150, 212, 201, 154, 190, 330, 410],
            barWidth: 40,// 固定宽度（像素）
        },
        {
            name: '5类',
            type: 'bar',
            stack: 'total',
            label: {
                show: true
            },
            emphasis: {
                focus: 'series'
            },
            data: [820, 832, 901, 934, 1290, 1330, 1320],
            barWidth: 40,// 固定宽度（像素）
        }
    ]
};
export const leimData =  [
    {
        broderColor: '#43CF7C',
        bgColor: '#E3FFEE',
        text1Coloe: "#248F4F",
        text2Color: "#248F4F",
        name: '1类',
        number: 820,
        leib: 25
    },
    {
        broderColor: '#006AE3',
        bgColor: '#C2DEFF',
        text1Coloe: "#0058BD",
        text2Color: "#0058BD",
        name: '2类',
        number: 820,
        leib: 25
    },
    {
        broderColor: '#FF8D1A',
        bgColor: '#FFF2C7',
        text1Coloe: "#FF8000",
        text2Color: "#FF8000",
        name: '3类',
        number: 820,
        leib: 25
    },
    {
        broderColor: '#D43030',
        bgColor: '#FFD9D9',
        text1Coloe: "#B50707",
        text2Color: "#B50707",
        name: '4类',
        number: 820,
        leib: 25
    },
    {
        broderColor: '#7948EA',
        bgColor: '#EAE0FF',
        text1Coloe: "#400ABF",
        text2Color: "#400ABF",
        name: '5类',
        number: 820,
        leib: 25
    },

]
export const tableConfig = [
    {
        label: '路段名称',
        prop: 'affiliatedRoadSection'
    },
    {
        label: '隧道名称',
        prop: 'tunnelName'
    },
    {
        label: '幅别',
        prop: 'hole'
    },
    {
        label: '隧道长度(m)',
        prop: 'tunnelLength'
    },
    {
        label: '起讫点桩号',
        prop: 'startingStation',
        width: '140'
    }
]