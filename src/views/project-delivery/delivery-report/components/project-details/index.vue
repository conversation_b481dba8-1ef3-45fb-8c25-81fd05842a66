<template>
  <div class="app-container project-details">
    <el-row :gutter="20" v-loading="loadingEcharts">
      <el-col :span="8">
        <div class="grid-content ep-bg-purple">
          <div class="ep-card">结构形式</div>
          <EchartsComp
            ref="refEchartsCompJg"
            :option="structureTypeStats"
            :height="'25vh'"
          ></EchartsComp>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="grid-content ep-bg-purple">
          <div class="ep-card">隧道规模</div>
          <EchartsComp
            ref="refEchartsCompSd"
            :option="tunnelScaleStats"
            :height="'25vh'"
          ></EchartsComp>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="grid-content ep-bg-purple">
          <div class="ep-card">近5年总体技术状况评定结果(单洞评)</div>
          <EchartsComp :option="optionYear" :height="'25vh'"></EchartsComp>
        </div>
      </el-col>
    </el-row>
    <br />
    <div class="grid-content" v-loading="loadingTable">
      <div class="ep-card">隧道评定等级分布</div>
      <br />
      <el-form :model="form" label-width="100" ref="ruleFormRef" :inline="true">
        <el-form-item label="路段名称" prop="affiliatedRoadSection">
          <el-input
            v-model="form.affiliatedRoadSection"
            placeholder="请输入路段名称"
          />
        </el-form-item>
        <el-form-item label="隧道名称" prop="tunnelName">
          <el-input v-model="form.tunnelName" placeholder="请输入隧道名称" />
        </el-form-item>
        <el-form-item label="隧道幅别" prop="hole">
          <el-select v-model="form.hole" placeholder="请选择隧道幅别">
            <el-option label="左幅" value="左幅" />
            <el-option label="右幅" value="右幅" />
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <el-button type="primary" @click="onSubmit">搜索</el-button>
          <el-button @click="resetForm(ruleFormRef)">重置</el-button>
        </el-form-item>
      </el-form>
      <br />
      <div class="leib-box">
        <div
          class="leib-item"
          v-for="(item, index) in leimData"
          :key="index"
          @click="handleClick(item, index)"
          :style="{
            background: item.bgColor,
            borderColor: item.broderColor,
            color: item.text1Coloe,
          }"
        >
          <div class="leib-item-title">
            <img
              class="icon"
              :src="getImageUrl(`${index + 1}icon.png`)"
              alt=""
            />
            <span class="item-title">单洞总体</span>
            <div class="item-number">
              {{ diseaseGradeStats?.[item.name] || 0 }}
            </div>
          </div>
          <p>
            {{
              `总占比：${formatPercentage(
                diseaseGradeStats?.[item.name],
                leimDataTotal
              )}`
            }}
          </p>
          <img
            class="gou-icon"
            v-show="index === leiIndex"
            :src="getImageUrl(`${index + 1}gou.png`)"
            alt=""
          />
        </div>
      </div>
      <br />
      <el-table :data="tableData" :style="{ height: '45vh' }">
        <!-- <el-table-column type="selection" fixed="left" width="55" /> -->
        <el-table-column type="index" fixed="left" label="序号" width="60" />
        <el-table-column
          v-for="(item, index) in tableConfig"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          :min-width="item?.width || 120"
        >
        </el-table-column>
        <el-table-column fixed="right" label="操作">
          <template #default="{ row }">
            <el-button type="primary" @click="showDetails(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <br />
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pages"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="_getProjectDetailTunnel"
          @current-change="_getProjectDetailTunnel"
        />
      </div>
    </div>
    <detailsComp ref="refDetailsComp" />
  </div>
</template> 
<script lang="ts" setup>
import { ref, defineAsyncComponent, onMounted } from "vue";
import {
  getProjectDetail,
  getProjectDetailTunnel,
} from "@/api/project-delivery";
import { useRoute } from "vue-router";
const route = useRoute(); // 获取路由对象:cite[2]:cite[5]:cite[6]
import {
  optionJgxs,
  optionSdgm,
  optionYear,
  tableConfig,
  leimData,
} from "./constants";
const EchartsComp = defineAsyncComponent(
  () => import("@/views/home/<USER>/echarts-comp/index.vue")
);
const detailsComp = defineAsyncComponent(() => import("./details/index.vue"));
const getImageUrl = (name) => {
  return new URL(
    `../../../../../assets/images/delivery-report/${name}`,
    import.meta.url
  ).href;
};
const form = ref({
  affiliatedRoadSection: "",
  tunnelName: "",
  hole: "",
});
const onSubmit = () => {
  _getProjectDetailTunnel();
};
const ruleFormRef = ref<any>();
const resetForm = (formEl: any | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  _getProjectDetailTunnel();
};
const total = ref(0);
const pages = ref(1);
const pageSize = ref(10);
const tableData = ref([{}]);
const selectionData = ref([]);
const handleSelectionChange = (val: any) => {
  selectionData.value = val;
};
const diseaseGradeStats = ref<any>({});
const structureTypeStats = ref<any>({});
const refEchartsCompJg = ref<any>(null);
const tunnelScaleStats = ref<any>({});
const refEchartsCompSd = ref<any>(null);
const leimDataTotal = ref<any>(0);
const loadingEcharts = ref(false);
const getList = async () => {
  loadingEcharts.value = true;
  const res = await getProjectDetail(route.query.id);
  //五级分类
  diseaseGradeStats.value = res?.data?.diseaseGradeStats;
  leimDataTotal.value = Object.values(diseaseGradeStats.value).reduce(
    (sum: any, value: any) => Number(sum) + Number(value),
    0
  );
  let structureTypeStatsOpions = Object.entries(
    res?.data?.structureTypeStats
  ).map(([key, value]) => ({
    name: key,
    value: value,
  }));
  // 结构形式
  structureTypeStats.value = optionJgxs(structureTypeStatsOpions);
  refEchartsCompJg.value.updataMap(structureTypeStats.value);
  let yAxisData: any = [];
  let seriesData: any = [];
  for (const key in res?.data?.tunnelScaleStats) {
    yAxisData.push(key);
    seriesData.push(res?.data?.tunnelScaleStats[key]);
  }
  // 隧道规模
  tunnelScaleStats.value = optionSdgm(yAxisData, seriesData);
  refEchartsCompSd.value.updataMap(tunnelScaleStats.value);

  loadingEcharts.value = false;
};
const loadingTable = ref(false);
const _getProjectDetailTunnel = async () => {
  loadingTable.value = true;
  const res = await getProjectDetailTunnel(route.query.id, {
    ...form.value,
    civilStructureLevel:civilStructureLevel.value || '1类',
    pages: pages.value,
    pageSize: pageSize.value,
  });
  tableData.value = res.rows;
  total.value = res.total;
  loadingTable.value = false;
};

const refDetailsComp = ref<any>(null);
const showDetails = (row: any) => {
  refDetailsComp.value.openDialog(row);
};
const leiIndex = ref(0);
const civilStructureLevel = ref("");
const handleClick = (row: any, index: any) => {
  leiIndex.value = index;
  civilStructureLevel.value = row.name
  _getProjectDetailTunnel();
};
function formatPercentage(part, total, decimalPlaces = 1) {
  if (!part || !total || part === 0 || total === 0) return "0%";
  const percentage = (part / total) * 100;
  return percentage.toFixed(decimalPlaces) + "%";
}
onMounted(() => {
  getList();
  _getProjectDetailTunnel();
});
</script>
<style lang="scss" scoped>
.project-details {
  height: calc(100vh - 120px);
  box-sizing: border-box;
}
.grid-content {
  box-sizing: border-box;
  padding: 12px 0;
  border-radius: 8px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  &:hover {
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
  }
  .ep-card {
    height: 24px;
    line-height: 24px;
    border-left: 6px solid #409eff;
    padding-left: 12px;
    font-size: 16px;
  }
}
.leib-box {
  display: flex;
  .leib-item {
    flex: 1;
    margin: 0 12px;
    border: 1px solid;
    border-radius: 6px;
    box-sizing: border-box;
    padding: 12px 24px;
    font-size: 16px;
    position: relative;
    .leib-item-title {
      display: flex;
      justify-content: space-around;
      align-items: center;
    }
    .gou-icon {
      position: absolute;
      right: 0px;
      bottom: 0px;
      height: 40px;
    }
  }
  .item-name,
  .item-number {
    font-size: 28px;
    font-weight: 600;
  }
  .icon {
    height: 50px;
    width: 50px;
  }
}
.pagination-section {
  display: flex;
  justify-content: flex-end;
}
</style>