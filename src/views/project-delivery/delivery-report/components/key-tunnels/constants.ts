/**
 * 生成随机隧道数据数组
 * @param {number} count - 要生成的隧道数量
 * @returns {Array} 隧道对象数组
 */
export function generateTunnelData(count) {
  // 隧道名称前缀
  const tunnelPrefixes = ['青龙', '白虎', '朱雀', '玄武', '昆仑', '秦岭', '太行', '大别', '峨眉', '武夷'];
  const tunnelSuffixes = ['山隧道', '隧道', '一号隧道', '二号隧道', '特长隧道', '高速隧道'];
  
  // 幅别选项
  const directions = ['左幅', '右幅'];
  
  // 图片地址基础路径
  const imageBaseUrl = 'https://example.com/tunnels/';
  
  // 生成随机隧道数据
  const tunnels:any = [];
  
  for (let i = 0; i < count; i++) {
    // 随机生成隧道名称
    const prefix = tunnelPrefixes[Math.floor(Math.random() * tunnelPrefixes.length)];
    const suffix = tunnelSuffixes[Math.floor(Math.random() * tunnelSuffixes.length)];
    const tunnelName = prefix + suffix;
    
    // 随机选择幅别
    const direction = directions[Math.floor(Math.random() * directions.length)];
    
    // 生成随机图片地址
    const imageUrl = `${imageBaseUrl}${encodeURIComponent(tunnelName)}_${direction}.jpg`;
    
    // 创建隧道对象并添加到数组
    tunnels.push({
      id: i + 1, // 添加一个ID字段
      name: tunnelName,
      direction: direction,
      imageUrl: imageUrl
    });
  }
  
  return tunnels;
}

// // 使用示例：生成5条随机隧道数据
// const tunnelData = generateTunnelData(5);
// console.log(tunnelData);