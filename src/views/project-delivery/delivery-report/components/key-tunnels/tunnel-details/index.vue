<template>
  <el-dialog
    width="800"
    :title="titleName"
    v-model="open"
    @close="handelClose"
    append-to-body
  >
    <div class="form-box">
      <div class="to-title">
        <div class="to-title-view">
          <span>起讫桩号</span>&nbsp;
          <el-input
            v-model="from.startingStation"
            :style="{ width: '140px' }"
            placeholder="隧道名称"
          />
          &nbsp;
          <span>——</span>
          &nbsp;
          <el-input
            v-model="from.endingStation"
            :style="{ width: '140px' }"
            placeholder="隧道名称"
          />
        </div>
        <TabsType :typeTabs="tabsTypes" @change="tabsChange" />
      </div>
      <br />
      <div v-show="tabsValue === '1'">
        <div class="search-box">
          <div>
            <span>{{ `土建结构评分` }}</span>
            <span class="search-box-value">{{
              `${from?.civilStructureScore || "/"}分`
            }}</span>
          </div>
          <div>
            <span>{{ `土建结构等级` }}</span>
            <span class="search-box-value">{{
              `${from?.civilStructureLevel || "/"}类`
            }}</span>
          </div>
        </div>
        <br />
        <table border="1" cellspacing="0" width="100%" class="tableStyle">
          <tr>
            <td class="td-label">洞口</td>
            <td class="td-label">洞门</td>
            <td class="td-label">衬砌破损</td>
            <td class="td-label">渗漏水</td>
            <td class="td-label">内装饰</td>
            <td class="td-label">检修道</td>
          </tr>
          <tr>
            <td class="td-label">{{ from?.openingConditionValue || "/" }}</td>
            <td class="td-label">{{ from?.portalConditionValue || "/" }}</td>
            <td class="td-label">{{ from?.liningsPsConditionValue || "/" }}</td>
            <td class="td-label">
              {{ from?.liningsSlsConditionValue || "/" }}
            </td>
            <td class="td-label">
              {{ from?.manholeInteriorConditionValue || "/" }}
            </td>
            <td class="td-label">{{ from?.manholeConditionValue || "/" }}</td>
          </tr>
          <tr>
            <td class="td-label">排水设施</td>
            <td class="td-label">标志标线</td>
            <td class="td-label">路面</td>
            <td class="td-label" colspan="2">吊顶及预埋件</td>
            <td class="td-label"></td>
          </tr>
          <tr>
            <td class="td-label">{{ from?.drainageConditionValue || "/" }}</td>
            <td class="td-label">{{ from?.outlineConditionValue || "/" }}</td>
            <td class="td-label">{{ from?.roadConditionValue || "/" }}</td>
            <td class="td-label" colspan="2">
              {{ from?.ceilingConditionValue || "/" }}
            </td>
            <td class="td-label"></td>
          </tr>
        </table>
        <br />
        <br />
        <div class="search-box">
          <div>
            <span>{{ `其他工程设施评分` }}</span>
            <span class="search-box-value">{{
              `${from?.otherFacilitiesScore || "/ "}分`
            }}</span>
          </div>
          <div>
            <span>{{ `其他工程设施等级` }}</span>
            <span class="search-box-value">{{
              `${from?.otherFacilitiesLevel || "/ "}类`
            }}</span>
          </div>
        </div>
        <br />
        <table border="1" cellspacing="0" width="100%" class="tableStyle">
          <tr>
            <td class="td-label">电缆沟</td>
            <td class="td-label">设备洞室</td>
            <td class="td-label">洞口联络通道</td>
            <td class="td-label">洞口限高门架</td>
            <td class="td-label">洞口绿化</td>
          </tr>
          <tr>
            <td class="td-label">{{ from?.cableGouConditionValue || "/" }}</td>
            <td class="td-label">
              {{ from?.deviceHoleConditionValue || "/" }}
            </td>
            <td class="td-label">
              {{ from?.passageAccessConditionValue || "/" }}
            </td>
            <td class="td-label">
              {{ from?.caveLimitFrameConditionValue || "/" }}
            </td>
            <td class="td-label">
              {{ from?.caveGreeningConditionValue || "/" }}
            </td>
          </tr>
          <tr>
            <td class="td-label">消音设施</td>
            <td class="td-label">减光设施</td>
            <td class="td-label">污水处理设施</td>
            <td class="td-label">洞口雕塑铭牌</td>
            <td class="td-label">房屋设施</td>
          </tr>
          <tr>
            <td class="td-label">
              {{ from?.noiseReductionConditionValue || "/" }}
            </td>
            <td class="td-label">
              {{ from?.lightReductionConditionValue || "/" }}
            </td>
            <td class="td-label">
              {{ from?.wastewaterTreatmentConditionValue || "/" }}
            </td>
            <td class="td-label">
              {{ from?.caveTunnelNameplateConditionValue || "/" }}
            </td>
            <td class="td-label">
              {{ from?.housingTreatementConditionValue || "/" }}
            </td>
          </tr>
        </table>
      </div>
      <div v-show="tabsValue === '2'">
        <el-table :data="tableData" :style="{ height: '45vh' }">
          <el-table-column type="index" fixed="left" label="序号" width="60" />
          <el-table-column
            v-for="(item, index) in tableConfig"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            :min-width="item?.width || 80"
          >
          </el-table-column>
        </el-table>

        <br />
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="pages"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="_getTunnelDisease"
            @current-change="_getTunnelDisease"
          />
        </div>
      </div>
    </div>
  </el-dialog>
</template>
  
  <script lang="ts" setup>
import { ref, defineAsyncComponent } from "vue";
import {
  getTunnelDisease,
  getTunnelTechnicalAssessment,
} from "@/api/project-delivery";
import { tableConfigTj, tableConfigQt, tableConfig } from "./constants.ts";
const TabsType = defineAsyncComponent(
  () => import("@/components/TabsType/index.vue")
);
const open = ref<boolean>(false);
const tabsTypes = [
  {
    name: "技术状况评定",
    value: "1",
  },
  {
    name: "病害详情",
    value: "2",
  },
];
const tabsActive = ref<string>("1");
const titleName = ref<string>("");
const loading = ref<boolean>(false);
const diseaseTypeCode = ref<string>("");
const tunnelId = ref<string>("");
const deliveryId = ref<string>("");
// 打开弹窗
const openDialog = (name, diseaseType, delivery, row) => {
  tunnelId.value = row.tunnelId;
  deliveryId.value = delivery;
  diseaseTypeCode.value = diseaseType;
  titleName.value = `${row?.tunnelName}-${row?.hole}`;
  open.value = true;
  _getTunnelTechnicalAssessment();
  //   name === "技术状况评定" &&
  //   name === "病害详情" && _getTunnelDisease();
};

// 技术状况评定
const from = ref<any>({});
const _getTunnelTechnicalAssessment = async () => {
  try {
    const res = await getTunnelTechnicalAssessment(
      tunnelId.value,
      deliveryId.value
    );
    from.value = res.data;
  } catch (error) {
    console.log(error);
  }
};
// 病害详情
const tableData = ref<any>([]);
const pages = ref<any>(1);
const pageSize = ref<any>(10);
const total = ref<any>(0);
const _getTunnelDisease = async () => {
  try {
    loading.value = true;
    const res = await getTunnelDisease(deliveryId.value, {
      pageNum: pages.value,
      pageSize: pageSize.value,
      diseaseTypeCode: diseaseTypeCode.value,
      tunnelId: tunnelId.value,
    });
    tableData.value = res.data;
    total.value = res.total;
    loading.value = false;
  } catch (error) {
    loading.value = false;
    console.log(error);
  }
};
const tabsValue = ref("1");
const tabsChange = (val) => {
  tabsValue.value = val + "";
  val == "1" && _getTunnelTechnicalAssessment();
  val == "2" && _getTunnelDisease();
};

const handelClose = () => {};

// 暴露变量
defineExpose({
  openDialog,
});
</script>
<style lang="scss" scoped>
.form-box {
  display: flex;
  flex-direction: column;
  .to-title-view {
    display: flex;
    align-items: center;
  }
}
.search-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .search-box-value {
    color: #409eff;
    font-weight: 600;
    padding-left: 12px;
  }
}
.td-label {
  text-align: center;
}
.to-title {
  display: flex;
  justify-content: space-between;
}
</style>
  