<template>
  <div class="app-container key-tunnels" v-loading="loading">
    <!-- <el-select
      v-model="typeCode"
      placeholder="类型"
      style="width: 200px"
      @change="getList()"
    >
      <el-option
        v-for="item in typeCodeOptions"
        :key="item.code"
        :label="item.name"
        :value="item.code"
      />
    </el-select> -->
    <br />
    <el-scrollbar :height="'calc(100vh - 180px)'">
      <div class="key-tunnels-content" v-if="cardData.length > 0">
        <div class="key-tunnels-card" v-for="item in cardData" :key="item.id">
          <div class="key-tunnels-card-title">
            {{ `${item?.tunnelName}-${item?.hole}` }}
          </div>
          <img
            class="key-tunnels-card-img"
            :src="item?.entranceImage || getImageUrl('suidaoimage.png')"
            alt=""
          />
          <div class="key-tunnels-card-text">
            <span @click="showTunnelDetails('技术状况评定', item)"
              >技术状况评定</span
            >
            <span @click="showTunnelDetails('病害详情', item)"
              >病害详情</span
            >
          </div>
        </div>
      </div>
      <el-empty v-else description="暂无数据" />
    </el-scrollbar>
    <br />
    <div class="pagination-section">
      <el-pagination
        v-model:current-page="pages"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="getList"
        @current-change="getList"
      />
    </div>
    <TunnelDetails ref="refTunnelDetails" />
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue";
import TunnelDetails from "./tunnel-details/index.vue";
import { getKeyTunnels, getDiseaseTypeEnum } from "@/api/project-delivery";
import { generateTunnelData } from "./constants";
const cardData = ref<any[]>(generateTunnelData(20));
import { useRoute } from "vue-router";
const route = useRoute(); // 获取路由对象:cite[2]:cite[5]:cite[6]
const getImageUrl = (name) => {
  return new URL(
    `../../../../../assets/images/delivery-report/${name}`,
    import.meta.url
  ).href;
};
const total = ref(0);
const pages = ref(1);
const pageSize = ref(10);
const loading = ref(false);
const getList = async () => {
  try {
    loading.value = true;
    const res = await getKeyTunnels(route.query.id, {
      pageNum: pages.value,
      pageSize: pageSize.value,
      diseaseTypeCode: typeCode.value,
    });
    total.value = res.total;
    cardData.value = res.rows;
    loading.value = false;
  } catch (err) {
    loading.value = false;
    console.log(err);
  }
};

const typeCodeOptions = ref<any>([]);
const typeCode = ref("");
const _getDiseaseTypeEnum = async (type) => {
  try {
    const res = await getDiseaseTypeEnum({
      enumType: type,
    });
    console.log(res);
    typeCodeOptions.value = res.data;
    typeCode.value = typeCodeOptions.value?.[0]["code"];
  } catch (err) {
    console.log(err);
  }
};
const refTunnelDetails = ref<any>(null);
const showTunnelDetails = (name, row) => {
  refTunnelDetails.value.openDialog(
    name,
    typeCode.value,
    route.query.id,
    row
  );
};
onMounted(() => {
  _getDiseaseTypeEnum("0");
  getList();
});
</script>
<style lang="scss" scoped>
.key-tunnels {
  .key-tunnels-content {
    // height: calc(100vh - 240px);
    width: 100%;
    display: grid;
    grid-template-columns: repeat(
      auto-fill,
      minmax(300px, 1fr)
    ); /* 自动填充，最小200px */
    gap: 20px; /* 项目间距 */
    justify-content: space-between;
    .key-tunnels-card {
      border-radius: 6px;
      padding: 12px;
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
      text-align: center;
      &:hover {
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
      }
      .key-tunnels-card-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 10px;
      }
      .key-tunnels-card-img {
        width: 100%;
        height: 200px;
      }
      .key-tunnels-card-text {
        display: flex;
        align-items: center;
        span {
          flex: 1;
          line-height: 32px;
          background: #409eff;
          color: #fff;
          font-weight: 600;
          margin: 1px;
        }
      }
    }
  }
  .pagination-section {
    display: flex;
    justify-content: flex-end;
  }
}
</style>