export const optionFilter = (data) => {
  return {
    legend: {
      top: 'bottom'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)' // {b}名称, {c}值, {d}百分比
    },
    toolbox: {
      show: true,
      feature: {
        mark: { show: true },
        dataView: { show: true, readOnly: false },
        restore: { show: true },
        saveAsImage: { show: true }
      }
    },
    label: {
        alignTo: 'edge',
        formatter: '{name|{b}}\n{time|{c}    {d}%}',
        minMargin: 5,
        edgeDistance: 10,
        lineHeight: 15,
        rich: {
          time: {
            fontSize: 10,
            color: '#999'
          }
        }
      },
    series: [
      {
        name: '',
        type: 'pie',
        radius: [60, 120],
        center: ['50%', '50%'],
        roseType: 'area',
        itemStyle: {
          borderRadius: 8
        },
        data,
        // data: [
        //   { value: 40, name: '一类' },
        //   { value: 38, name: '二类' },
        //   { value: 32, name: '三类' },
        //   { value: 30, name: '四类' },
        //   { value: 28, name: '五类' },
        // ]
      }
    ]
  };
}
// <span>技术状况统计</span>
//         <span>隧道类型</span>
//         <span>病害统计</span>
export const toolTitleOpoption = [
  {
    name: '技术状况统计',
    value: 'civilStructurePieList'
  },
  {
    name: '隧道类型',
    value: 'tunnelTypeList'
  },
  {
    name: '病害统计',
    value: 'tunnelCivilScoreLevelDTOList'
  }
]