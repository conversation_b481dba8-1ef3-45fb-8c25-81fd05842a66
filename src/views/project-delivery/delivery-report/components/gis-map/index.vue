<template>
  <div class="gis-map">
    <div id="gisMapContainer"></div>
    <div class="gis-map-tool">
      <div class="gis-map-tool-title">
        <span>隧道数量</span>
        <span>{{ gisData?.totalTunnel }}座</span>
      </div>
      <div class="gis-map-tool-views">
        <div class="">
          <p class="title-value">{{ gisData?.totalMiles }}</p>
          <p class="title-name">总里程(km)</p>
        </div>
        <div>
          <p class="title-value">{{ gisData?.totalDisease }}</p>
          <p class="title-name">病害总数(个)</p>
        </div>
      </div>
      <div class="gis-map-tool-title">
        <span
          :class="[item.name === activeName ? 'title-active' : '']"
          v-for="item in toolTitleOpoption"
          @click="changeContent(item)"
          :key="item.name"
          >{{ item.name }}</span
        >
      </div>
      <div class="gis-map-tool-content">
        <EchartsComp
          ref="refEchartsCompGis"
          :option="option"
          :height="'44vh'"
        ></EchartsComp>
      </div>
    </div>
  </div>
</template>
<script lang="ts"  setup>
import { ref, onMounted, watch, defineAsyncComponent } from "vue";
import { optionFilter, toolTitleOpoption } from "./constsnts";
import { getGisMapInfo } from "@/api/project-delivery";
import { useRoute } from "vue-router";
const route = useRoute(); // 获取路由对象:cite[2]:cite[5]:cite[6]
const EchartsComp = defineAsyncComponent(
  () => import("@/views/home/<USER>/echarts-comp/index.vue")
);
const map = ref();
const initMap = async (lat = 117.014855, lng = 31.859474, zoom = 8) => {
  map.value = new BMapGL.Map("gisMapContainer"); //创建地图实例
  let point = new BMapGL.Point(lat, lng); //地图中心点
  map.value.centerAndZoom(point, zoom); //地图初始化，同时设置地图展示级别
  map.value.enableScrollWheelZoom(true); //使用鼠标滚轮控制缩放
};
const gisData = ref<any>({});
const option = ref<any>({});
const civilStructurePieList = ref<any>([]);
const tunnelTypeList = ref<any>([]);
const tunnelCivilScoreLevelDTOList = ref<any>([]);
const refsEchartsComp = ref(null);
const getInit = async () => {
  const res = await getGisMapInfo(route.query.id);
  gisData.value = res.data;
  res.data.civilStructurePieList.forEach((item: any) => {
    civilStructurePieList.value.push({
      value: item.typeNum,
      name: item.typeName,
    });
    // item.value = item.typeNum;
    // item.name = item.typeName;
  });
  // tunnelTypeList.value = res.data.tunnelTypeList;
  res.data.tunnelTypeList.forEach((item: any) => {
    // item.value = `${item.count} ${item.proportion}%`;
    // item.name = item.typeName;
    tunnelTypeList.value.push({
      value: item.count,
      name: item.typeName,
    });
  });
  // tunnelCivilScoreLevelDTOList.value = res.data.tunnelCivilScoreLevelDTOList;
  res.data.tunnelCivilScoreLevelDTOList.forEach((item: any) => {
    tunnelCivilScoreLevelDTOList.value.push({
      value: item.count,
      name: item.level,
    });
  });
  option.value = optionFilter(civilStructurePieList.value);
  refEchartsCompGis?.value?.updataMap(option.value);
};
const activeName = ref("技术状况统计");
const refEchartsCompGis = ref<any>(null);
const changeContent = (item) => {
  activeName.value = item.name;
};

onMounted(() => {
  nextTick(() => {
    getInit();
    initMap();
  });
});
watch(
  () => activeName.value,
  (val) => {
    if (val === "技术状况统计") {
      option.value = optionFilter(civilStructurePieList.value);
    }
    if (val === "隧道类型") {
      option.value = optionFilter(tunnelTypeList.value);
    }
    if (val === "病害统计") {
      option.value = optionFilter(tunnelCivilScoreLevelDTOList.value);
    }
    console.log(option.value);
    refEchartsCompGis?.value.updataMap(option.value);
  }
);
</script>
<style lang="scss" scoped>
.gis-map {
  width: calc(100vw - 160px);
  height: calc(100vh - 80px);
  box-sizing: border-box;
  padding: 20px;
  display: flex;
  #gisMapContainer {
    flex: 1;
    border-radius: 30px;
    border: 1px solid rgba(42, 130, 228, 1);
    //   width: calc(100vw - 160px);
    //   height: calc(100vh - 80px);
  }
  .gis-map-tool {
    width: 500px;
    box-sizing: border-box;
    padding: 0 16px;
    .gis-map-tool-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 3px dashed rgba(166, 166, 166, 1);
      margin: 20px;
      span {
        font-size: 28px;
        font-weight: 400;
        letter-spacing: 0px;
        line-height: 40.54px;
        color: rgba(128, 128, 128, 1);
        text-align: left;
        vertical-align: top;
        cursor: pointer;
      }
      .title-active {
        color: rgba(42, 130, 228, 1);
      }
    }
  }
  .gis-map-tool-views {
    display: flex;
    justify-content: space-around;
    align-items: center;
    div {
      padding: 16px;
      border-radius: 20px;
      background: rgba(255, 255, 255, 1);
      border: 3px solid rgba(42, 130, 228, 1);
      text-align: center;
      p {
        margin: 0;
        padding: 0;
      }
      .title-value {
        font-size: 50px;
        font-weight: 400;
        letter-spacing: 0px;
        line-height: 60.5px;
        color: rgba(42, 130, 228, 1);
        text-align: left;
        vertical-align: top;
      }
      .title-name {
        font-size: 26px;
        font-weight: 400;
        letter-spacing: 0px;
        line-height: 37.65px;
        color: rgba(42, 130, 228, 1);
        text-align: left;
        vertical-align: top;
      }
    }
  }
}
</style>