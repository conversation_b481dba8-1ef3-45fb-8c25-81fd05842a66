export const optionsBhzk = (data: Array<any> = [], bhzkTotal) => {
    return {
        grid: {
            top: '10%',
            left: '3%',
            right: '5%',
            bottom: '3%',
            containLabel: true
        },
        tooltip: {
            trigger: 'item',
            formatter: '{b}\n{c}    {d}%',
        },
        legend: {
            type: 'scroll',
            orient: 'vertical',
            right: '10%',
            top: 'center',
            width: '40%',
            align: 'auto',
            itemWidth: 14,
            itemHeight: 14,
            textStyle: {
                // color: '#fff', // 设置红色作为默认文字颜色
                fontSize: 14
            },
        },
        label: {
            alignTo: 'edge',
            formatter: '{name|{b}}\n{time|{c}    {d}%}',
            minMargin: 5,
            edgeDistance: 10,
            lineHeight: 15,
            rich: {
                time: {
                    fontSize: 10,
                    color: '#999'
                }
            }
        },
        series: [
            {
                name: '',
                type: 'pie',
                radius: ['60%', '80%'],
                center: ['30%', '50%'],
                avoidLabelOverlap: false,
                padAngle: 5,
                itemStyle: {
                    borderRadius: 5
                },
                label: {
                    show: false,
                    position: 'center',
                    formatter: '{name|{b}}\n{time|{c}    {d}%}',
                },
                // emphasis: {
                //     label: {
                //         show: true,
                //         fontSize: 40,
                //         fontWeight: 'bold'
                //     }
                // },
                labelLine: {
                    show: false
                },
                data,
                // data: [
                //     { value: 1048, name: '状况值' },
                //     { value: 735, name: '纵向裂缝' },
                //     { value: 580, name: '惨漏水' },
                //     { value: 484, name: '剥落' },
                // ]
            },
            {
                type: 'pie',
                radius: ['0%', '0%'],
                center: ['30%', '50%'],
                label: {
                    show: true,
                    position: 'center',
                    formatter: function () {
                        return `${bhzkTotal}\n病害总个数`;
                    },
                    fontSize: 18,
                    fontWeight: 'bold',
                },
                data: [{
                    value: bhzkTotal, name: '总数'
                }]
            }
        ]
    }
};
export let dataOptions = [
    [120, 200, 150, 80, 70, 110],
    [100, 120, 190, 220, 150, 130]
];
export const optionsType = (data: Array<any> = []) => {
    return {
        grid: {
            top: '10%',
            left: '3%',
            right: '5%',
            bottom: '3%',
            containLabel: true
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            type: 'scroll',
            orient: 'vertical',
            right: '10%',
            top: 'center',
            width: '50%',
            align: 'auto',
            itemWidth: 14,
            itemHeight: 14,
            textStyle: {
                // color: '#fff', // 设置红色作为默认文字颜色
                fontSize: 14
            },
        },
        series: [
            {
                name: '',
                type: 'pie',
                // radius: ['60%', '80%'],
                center: ['30%', '50%'],
                avoidLabelOverlap: false,
                padAngle: 5,
                itemStyle: {
                    borderRadius: 5
                },
                label: {
                    show: true,
                    position: 'inner',
                    // formatter: '{d}%'  // {b}表示名称，{d}表示百分比
                    formatter: '{b}    {d}%',

                },
                labelLine: {
                    show: false
                },
                data,
                // data: [
                //     { value: 1048, name: '环向裂缝 1118个 20%' },
                //     { value: 735, name: '纵向裂缝' },
                //     { value: 580, name: '惨漏水' },
                //     { value: 484, name: '剥落' },
                // ]
            },
            // {
            //     type: 'pie',
            //     radius: ['0%', '0%'],
            //     center: ['30%', '50%'],
            //     label: {
            //         show: true,
            //         position: 'center',
            //         formatter: function () {
            //             return `2847\n病害总个数`;
            //         },
            //         fontSize: 18,
            //         fontWeight: 'bold',
            //     },
            //     data: [{
            //         value: 2847, name: '总数'
            //     }]
            // }
        ]
    }
};
export const optionNumber = (xAxisData: Array<any> = [], seriesData: Array<any> = []) => {
    return {
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            // data:legendData
            data: ['1类', '2类', '3类', '4类', '5类']
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        // toolbox: {
        //     feature: {
        //         saveAsImage: {}
        //     }
        // },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: xAxisData
            // data: ['2021', '2022', '2023', '2024', '2025']
        },
        yAxis: {
            type: 'value'
        },
        series: seriesData,

    };
};

export const tabsTypes = [
    {
        name: "土建设施",
        value: "1",
    },
    {
        name: "其他工程设施",
        value: "2",
    },
]
export const series = [
    {
        name: '1类',
        type: 'line',
        stack: 'Total',
        data: []
    },
    {
        name: '2类',
        type: 'line',
        stack: 'Total',
        data: []
    },
    {
        name: '3类',
        type: 'line',
        stack: 'Total',
        data: []
    },
    {
        name: '4类',
        type: 'line',
        stack: 'Total',
        data: []
    },
    {
        name: '5类',
        type: 'line',
        stack: 'Total',
        data: []
    }
]

export const typeObjs = {
    status1Count: '1类',
    status2Count: '2类',
    status3Count: '3类',
    status4Count: '4类',
    status5Count: '5类'
}