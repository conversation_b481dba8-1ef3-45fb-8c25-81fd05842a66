<template>
  <div class="app-container" v-loading="loading">
    <TabsType :typeTabs="tabsTypes" @change="tabsChange" />
    <br />
    <el-row class="row-bg" justify="space-around">
      <el-col :span="10">
        <cardView :titleText="'病害状况值'">
          <EchartsComp
            :option="dataOptionsBhzk"
            ref="refEchartsCompBhzk"
            :height="'25vh'"
          ></EchartsComp>
        </cardView>
      </el-col>
      <el-col :span="10">
        <cardView :titleText="'病害类型分类'">
          <EchartsComp
            ref="refEchartsCompType"
            :option="optionsTypeData"
            :height="'25vh'"
          ></EchartsComp>
        </cardView>
      </el-col>
    </el-row>
    <br />
    <el-row class="row-bg" justify="space-around">
      <el-col :span="22">
        <cardView :titleText="'病害总量变化趋势'">
          <EchartsComp
            ref="refEchartsCompNumber"
            :option="dataOptionsNumber"
            :height="'40vh'"
          ></EchartsComp>
        </cardView>
      </el-col>
    </el-row>
  </div>
</template>
<script lang="ts" setup>
import { ref, defineAsyncComponent } from "vue";
import { getDiseaseStatistics } from "@/api/project-delivery";
import {
  optionsBhzk,
  optionsType,
  dataOptions,
  optionNumber,
  tabsTypes,
  series,
  typeObjs,
} from "./constants";
import { useRouter, useRoute } from "vue-router";
import { onMounted } from "vue";
import { el } from "element-plus/es/locale/index.js";
const router = useRouter();
const route = useRoute(); // 获取路由对象:cite[2]:cite[5]:cite[6]
const cardView = defineAsyncComponent(
  () => import("@/views/project-delivery/components/card-view/index.vue")
);
const EchartsComp = defineAsyncComponent(
  () => import("@/views/home/<USER>/echarts-comp/index.vue")
);
const TabsType = defineAsyncComponent(
  () => import("@/components/TabsType/index.vue")
);
const form = ref<any>({});
const onSubmit = () => {
  console.log("submit!");
};
const ruleFormRef = ref<any>();
const resetForm = (formEl: any | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
};
const tabsChange = (val) => {
  getInit(Number(val));
};
const loading = ref(false);

const getInit = async (diseaseCategory = 1) => {
  try {
    loading.value = true;
    const res = await getDiseaseStatistics({
      deliveryId: route.query.id,
      diseaseCategory,
    });
    let { diseaseTypeStats, diseaseTrendStats, diseaseStatusStats } = res.data;
    diseaseTypeStatsFilter(diseaseTypeStats);
    diseaseTrendStatsFilter(diseaseTrendStats);
    diseaseStatusStatsFilter(diseaseStatusStats);
    loading.value = false;
  } catch (error) {
    console.log(error);
    loading.value = false;
  }
};
const dataOptionsBhzk = ref<any>({});
const refEchartsCompBhzk = ref<any>(null);
const diseaseTypeStatsFilter = (diseaseTypeStats) => {
  diseaseTypeStats.forEach((item) => {
    item.value = item.count;
    item.name = item.typeName;
  });
  let bhzkTotal = diseaseTypeStats.reduce((pre, cur) => {
    return pre + cur.count;
  }, 0);
  dataOptionsBhzk.value = optionsBhzk(diseaseTypeStats, bhzkTotal);
  refEchartsCompBhzk.value.updataMap(dataOptionsBhzk.value);
};

const refEchartsCompNumber = ref<any>(null);
const dataOptionsNumber = ref<any>({});
const diseaseTrendStatsFilter = (diseaseTrendStats) => {
  dataOptionsNumber.value = {};
  let xAxisData: Array<any> = [];
  let seriesData: Array<any> = JSON.parse(JSON.stringify(series));
  diseaseTrendStats.forEach((item) => {
    xAxisData.push(item.year);
    seriesData.forEach((item1) => {
      if (item1.name === "1类") {
        item1.data.push(item.status1Count);
      } else if (item1.name === "2类") {
        item1.data.push(item.status2Count);
      } else if (item1.name === "3类") {
        item1.data.push(item.status3Count);
      } else if (item1.name === "4类") {
        item1.data.push(item.status4Count);
      } else if (item1.name === "5类") {
        item1.data.push(item.status5Count);
      }
    });
  });
  dataOptionsNumber.value = optionNumber(xAxisData, seriesData);
  refEchartsCompNumber.value.updataMap(dataOptionsNumber.value);
};
const optionsTypeData = ref<any>({});
const refEchartsCompType = ref<any>(null);
const diseaseStatusStatsFilter = (data: object) => {
  let arr: any = [];
  Object.keys(data).forEach((item) => {
    if (item !== "totalCount") {
      arr.push({
        name: `${typeObjs[item]} ${data[item]}`,
        value: data[item],
      });
    }
  });
  optionsTypeData.value = optionsType(arr);
  refEchartsCompType.value.updataMap(optionsTypeData.value);
};
onMounted(() => {
  getInit();
});
</script>