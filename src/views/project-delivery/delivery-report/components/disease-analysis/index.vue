<template>
  <div class="app-container">
    <el-form :model="form" label-width="auto" ref="ruleFormRef" :inline="true">
      <el-form-item label="路段名称" prop="tunnelCode">
        <el-input v-model="form.tunnelCode" placeholder="请输入路段名称" />
      </el-form-item>
      <el-form-item label="隧道名称" prop="tunnelName">
        <el-input v-model="form.tunnelName" placeholder="请输入隧道名称" />
      </el-form-item>
      <el-form-item label="隧道幅别" prop="tunnelType">
        <el-select v-model="form.tunnelType" placeholder="请选择隧道幅别">
          <el-option label="左幅" value="shanghai" />
          <el-option label="右幅" value="beijing" />
        </el-select>
      </el-form-item>
      <el-form-item label="">
        <el-button type="primary" @click="onSubmit">搜索</el-button>
        <el-button @click="resetForm(ruleFormRef)">重置</el-button>
      </el-form-item>
    </el-form>
    <br />
    <el-row class="row-bg" justify="space-around">
      <el-col :span="10">
        <cardView :titleText="'病害状况值'">
          <EchartsComp
            :option="options(dataOptions)"
            :height="'25vh'"
          ></EchartsComp>
        </cardView>
      </el-col>
      <el-col :span="10">
        <cardView :titleText="'病害类型分类'">
          <EchartsComp :option="optionsType()" :height="'25vh'"></EchartsComp>
        </cardView>
      </el-col>
    </el-row>
    <br />
    <el-row class="row-bg" justify="space-around">
      <el-col :span="22">
        <cardView :titleText="'病害总量变化趋势'">
          <EchartsComp :option="optionNumber()" :height="'40vh'"></EchartsComp>
        </cardView>
      </el-col>
    </el-row>
  </div>
</template>
<script lang="ts" setup>
import { ref, defineAsyncComponent } from "vue";
import { options, optionsType, dataOptions, optionNumber } from "./constants";
const cardView = defineAsyncComponent(
  () => import("@/views/project-delivery/components/card-view/index.vue")
);
const EchartsComp = defineAsyncComponent(
  () => import("@/views/home/<USER>/echarts-comp/index.vue")
);
const form = ref<any>({});
const onSubmit = () => {
  console.log("submit!");
};
const ruleFormRef = ref<any>();
const resetForm = (formEl: any | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
};
</script>