<template>
  <div class="project-introduction">
    <div id="gisMapProjectIntroduction"></div>
    <div class="project-view">
      <div class="project-title">项目概况</div>
      <div class="project-text">
        G3W德州至上饶高速公路池州至祁门段（以下简称“本项目”）是安徽省规划的“纵三”高速公路的一段。本项目起点位于贵池区涓桥镇，顺接在建池州长江公路大桥和沪渝高速交叉处的殷家汇枢纽互通，终点位于祁门县金字牌镇，与黄祁高速交叉，路线全长约91.656公里。
        受安徽省交通控股集团有限公司池州高速公路管理中心委托，根据《公路隧道养护技术规范》（JTG
        H12-2015）及双方合同要求，安徽省高速公路试验检测科研中心有限公司于2023年10月10日至2023年11月28日对池州高速公路管理中心G0321德上高速池祁段大邀岭隧道进行技术状况检查工作。
      </div>
    </div>
  </div>
</template>
<script  setup>
import { ref, onMounted, onUnmounted, watch } from "vue";
const map = ref();
const initMap = async (lat = 117.014855, lng = 31.859474, zoom = 10) => {
  map.value = new BMapGL.Map("gisMapProjectIntroduction"); //创建地图实例
  let point = new BMapGL.Point(lat, lng); //地图中心点
  map.value.centerAndZoom(point, zoom); //地图初始化，同时设置地图展示级别
//   map.value.enableScrollWheelZoom(true); //使用鼠标滚轮控制缩放
};

onMounted(() => {
  nextTick(() => {
    initMap();
  });
});
</script>
<style lang="scss" scoped>
#gisMapProjectIntroduction {
  width: calc(100vw - 160px);
  height: calc(100vh - 80px);
}
.project-introduction {
  position: relative;
  .project-view {
    width: calc(100vw - 160px);
    height: calc(100vh - 80px);
    position: absolute;
    left: 0;
    top: 0;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding: 100px 200px;
  }
  .project-title {
    // left: 289px;
    // top: 169px;
    // width: 1584px;
    height: 80px;
    opacity: 1;
    border-radius: 30px;
    line-height: 80px;
    padding-left: 34px;
    font-size: 34px;
    background: linear-gradient(
      90deg,
      rgba(166, 166, 166, 1) 0%,
      rgba(229, 229, 229, 0) 100%
    );
    margin-bottom: 60px;
  }
  .project-text {
    height: 434px;
    opacity: 1;
    border-radius: 30px;
    font-size: 28px;
    padding: 20px;
    line-height: 50px;
    background: linear-gradient(
      90deg,
      rgba(204, 204, 204, 1) 0%,
      rgba(229, 229, 229, 0) 100%
    );
  }
}
</style>