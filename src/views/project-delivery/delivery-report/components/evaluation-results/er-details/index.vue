<template>
  <el-dialog
    width="500"
    :title="'隧道评定结果'"
    v-model="open"
    @close="handelClose"
    append-to-body
  >
    <div class="form-box">
      <el-input
        v-model="form.name"
        :style="{ width: '200px' }"
        placeholder="隧道名称"
      />
      <br />
      <TabsType :typeTabs="tabsFuTypes" />
      <br />
      <TabsType :typeTabs="tabsTypes" />
      <br />
      <div class="to-title-view">
        <span>起讫桩号</span>&nbsp;
        <el-input
          v-model="form.name"
          :style="{ width: '100px' }"
          placeholder="隧道名称"
        />
        &nbsp;
        <span>——</span>
        &nbsp;
        <el-input
          v-model="form.name"
          :style="{ width: '100px' }"
          placeholder="隧道名称"
        />
      </div>
      <br />
      <div class="search-box">
        <div>
          <span>{{
            `${tabsActive == "1" ? "土建结构" : "其他工程设施"}评分`
          }}</span>
          <span class="search-box-value">{{ `92.5分` }}</span>
        </div>
        <div>
          <span>{{
            `${tabsActive == "1" ? "土建结构" : "其他工程设施"}等级`
          }}</span>
          <span class="search-box-value">{{ `2类` }}</span>
        </div>
      </div>
      <table border="1" cellspacing="0" width="100%" class="tableStyle">
        <tr>
          <td class="td-label">洞口</td>
          <td class="td-label">洞门</td>
          <td class="td-label">衬砌破损</td>
          <td class="td-label">渗漏水</td>
          <td class="td-label">内装饰</td>
          <td class="td-label">检修道</td>
        </tr>
        <tr>
          <td class="td-label">/</td>
          <td class="td-label">/</td>
          <td class="td-label">/</td>
          <td class="td-label">/</td>
          <td class="td-label">/</td>
          <td class="td-label">/</td>
        </tr>
        <tr>
          <td class="td-label">排水设施</td>
          <td class="td-label">标志标线</td>
          <td class="td-label">路面</td>
          <td class="td-label" colspan="2">吊顶及预埋件</td>
          <td class="td-label"></td>
        </tr>
        <tr>
          <td class="td-label">/</td>
          <td class="td-label">/</td>
          <td class="td-label">/</td>
          <td class="td-label" colspan="2">/</td>
          <td class="td-label"></td>
        </tr>
      </table>
      <table border="1" cellspacing="0" width="100%" class="tableStyle">
        <tr>
          <td class="td-label">电缆沟</td>
          <td class="td-label">设备洞室</td>
          <td class="td-label">洞口联络通道</td>
          <td class="td-label">洞口限高门架</td>
          <td class="td-label">洞口绿化</td>
        </tr>
        <tr>
          <td class="td-label">/</td>
          <td class="td-label">/</td>
          <td class="td-label">/</td>
          <td class="td-label">/</td>
          <td class="td-label">/</td>
        </tr>
        <tr>
          <td class="td-label">消音设施</td>
          <td class="td-label">减光设施</td>
          <td class="td-label">污水处理设施</td>
          <td class="td-label">洞口雕塑铭牌</td>
          <td class="td-label">房屋设施</td>
        </tr>
        <tr>
          <td class="td-label">/</td>
          <td class="td-label">/</td>
          <td class="td-label">/</td>
          <td class="td-label">/</td>
          <td class="td-label">/</td>
        </tr>
      </table>
    </div>
  </el-dialog>
</template>
  
  <script lang="ts" setup>
import { ref, defineAsyncComponent } from "vue";
import { tableConfigTj, tableConfigQt } from "./constants.ts";
const TabsType = defineAsyncComponent(
  () => import("@/components/TabsType/index.vue")
);
const open = ref<boolean>(false);
const form = ref<any>({});
const tabsTypes = [
  {
    name: "土建机构",
    value: "1",
  },
  {
    name: "其他工程设施",
    value: "2",
  },
];
const tabsActive = ref<string>("1");
const tabsFuTypes = [
  {
    name: "左幅",
    value: "1",
  },
  {
    name: "右幅",
    value: "2",
  },
];
const tableConfig = ref<any[]>(tableConfigTj);
// 打开弹窗
const openDialog = async (row: any) => {
  console.log(row);
  open.value = true;
};
const loading = ref<boolean>(false);

const handelClose = () => {};

// 暴露变量
defineExpose({
  openDialog,
});
</script>
<style lang="scss" scoped>
.form-box {
  display: flex;
  flex-direction: column;
  .to-title-view {
    display: flex;
    align-items: center;
  }
}
.search-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .search-box-value {
    color: #409eff;
    font-weight: 600;
    padding-left: 12px;
  }
}
.td-label {
  text-align: center;
}
</style>
  