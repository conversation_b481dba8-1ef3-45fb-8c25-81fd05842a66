<template>
  <div class="app-container evaluation-results">
    <el-row :gutter="20">
      <el-col :span="9">
        <div class="grid-content ep-bg-purple">
          <div class="ep-card">隧道技术状况等级</div>
          <div class="ep-card-content">
            <div class="ep-card-title">
              <span class="title">土建结构</span>
              <div class="tabs-view">
                <span
                  v-for="item in typeList"
                  :key="item"
                  :class="[tabsTjValue === item && 'tabs-active']"
                  @click="tabsTjValue = item"
                  >{{ item }}</span
                >
              </div>
            </div>
            <br />
            <div class="tips-view">
              <span v-for="item in tipsTj" :key="item">{{ item }}</span>
            </div>
          </div>
          <div class="ep-card-content">
            <div class="ep-card-title">
              <span class="title">其他工程设施</span>
              <div class="tabs-view">
                <span
                  v-for="item in typeList"
                  :key="item"
                  :class="[tabsQtValue === item && 'tabs-active']"
                  @click="tabsQtValue = item"
                  >{{ item }}</span
                >
              </div>
            </div>
            <br />
            <div class="tips-view">
              <span v-for="item in tipsTj" :key="item">{{ item }}</span>
            </div>
          </div>
        </div>
        <br />
        <div class="grid-content ep-bg-purple">
          <div class="ep-card">隧道评定等级统计</div>
          <EchartsComp :option="option" :height="'30vh'"></EchartsComp>
        </div>
      </el-col>
      <el-col :span="15">
        <el-form :model="form" ref="ruleFormRef" label-width="auto" inline>
          <el-form-item label="隧道名称" prop="name">
            <el-input
              v-model="form.name"
              :style="{ width: '120px' }"
              placeholder="隧道名称"
            />
          </el-form-item>
          <el-form-item label="隧道幅别" prop="tunnelType">
            <el-select
              v-model="form.tunnelType"
              :style="{ width: '120px' }"
              placeholder="隧道幅别"
            >
              <el-option label="左幅" value="shanghai" />
              <el-option label="右幅" value="beijing" />
            </el-select>
          </el-form-item>
          <el-form-item label="专项检查项目" prop="projrct">
            <el-select
              v-model="form.projrct"
              :style="{ width: '120px' }"
              placeholder="专项检查项目"
            >
              <el-option label="项目1" value="shanghai" />
              <el-option label="项目2" value="beijing" />
            </el-select>
          </el-form-item>
          <el-form-item label="">
            <el-button type="primary" @click="onSubmit">搜索</el-button>
            <el-button @click="resetForm(ruleFormRef)">重置</el-button>
          </el-form-item>
        </el-form>
        <br />
        <el-table :data="tableData" :style="{ height: '75vh' }">
          <el-table-column type="index" fixed="left" label="序号" width="60" />
          <el-table-column
            v-for="(item, index) in tableConfig"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            :min-width="item?.width || 100"
          >
          <template #default="{ row }">
            <span class="ellipsis" v-if="item.label === '土建结构历史评分'">{{ row[item?.prop+''] || '--' }}</span>
            <!-- <span class="ellipsis" v-if="item.label === '其他设施历史评分'">{{  }}</span> -->
          </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作">
            <template #default="{ row }">
              <el-button type="primary" @click="showDetails(row)"
                >详情</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <br />
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="pages"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="getList"
            @current-change="getList"
          />
        </div>
      </el-col>
    </el-row>
    <erDetails ref="refErDetails" />
  </div>
</template>
<script lang="ts" setup>
import { ref, defineAsyncComponent } from "vue";
import { option, tableConfig } from "./constants";
const EchartsComp = defineAsyncComponent(
  () => import("@/views/home/<USER>/echarts-comp/index.vue")
);
const erDetails = defineAsyncComponent(() => import("./er-details/index.vue"));
const typeList = ["1类", "2类", "3类", "4类", "5类"];
const tabsTjValue = ref("1类");
const tabsQtValue = ref("1类");
const tipsTj = [
  "洞口",
  "洞门",
  "衬砌(结构破损)",
  "衬砌(渗漏水)",
  "路面",
  "检修道",
  "排水设施",
  "吊顶及预埋件",
  "内装饰",
  "交通标志、标线",
];
const form = ref<any>({});
const total = ref(1);
const pages = ref(1);
const pageSize = ref(10);
const tableData = ref<any[]>([{}]);
const onSubmit = () => {
  console.log(form.value);
};
const ruleFormRef = ref<any>();
const resetForm = (formEl: any | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
};
const refErDetails = ref<any>();
const showDetails = (row) => {
  refErDetails.value.openDialog(row);
};
const getList = () => {};
</script>
<style lang="scss" scoped>
.evaluation-results {
  .grid-content {
    box-sizing: border-box;
    padding: 12px 0;
    border-radius: 8px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
    &:hover {
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
    }
    .ep-card {
      height: 24px;
      line-height: 24px;
      border-left: 6px solid #409eff;
      padding-left: 12px;
      font-size: 16px;
    }
  }
  .ep-card-content {
    padding: 12px;
    .ep-card-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;
      .title {
        font-size: 16px;
      }
    }
    .tabs-view {
      min-width: 240px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-left: 24px;
      border-radius: 5px;
      border: 1px solid #006fed;
      background: #e0eaf5;
      color: #006fed;
      span {
        font-size: 18px;
        line-height: 30px;
        padding: 0 24px;
        border-radius: 4px;
        cursor: pointer;
      }
      .tabs-active {
        border: 1px solid #006fed;
        background: #006fed;
        color: #fff;
      }
    }
    .tips-view {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      background: #409eff;
      color: #fff;
      border-radius: 8px;
      padding: 8px 24px;
      span {
        width: 33.33%;
        line-height: 24px;
        cursor: pointer;
      }
    }
  }
}
.ellipsis {
  color: #006fed;
}
</style>