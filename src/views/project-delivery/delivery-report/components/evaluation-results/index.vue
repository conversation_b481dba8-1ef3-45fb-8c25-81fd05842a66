<template>
  <div class="app-container evaluation-results" v-loading="loading">
    <el-row :gutter="20">
      <el-col :span="11">
        <TabsType :typeTabs="tabsTypes" @change="tabsChange" />
        <br />
        <div class="grid-content ep-bg-purple">
          <div class="ep-card">病害类型分类</div>
          <div class="ep-card-content" v-if="false">
            <div class="ep-card-title">
              <span class="title">土建结构</span>
              <div class="tabs-view">
                <span
                  v-for="item in typeList"
                  :key="item"
                  :class="[tabsTjValue === item && 'tabs-active']"
                  @click="tabsTjValue = item"
                  >{{ item }}</span
                >
              </div>
            </div>
            <br />
            <div class="tips-view">
              <span v-for="item in tipsTj" :key="item">{{ item }}</span>
            </div>
          </div>
          <div class="ep-card-content" v-if="false">
            <div class="ep-card-title">
              <span class="title">其他工程设施</span>
              <div class="tabs-view">
                <span
                  v-for="item in typeList"
                  :key="item"
                  :class="[tabsQtValue === item && 'tabs-active']"
                  @click="tabsQtValue = item"
                  >{{ item }}</span
                >
              </div>
            </div>
            <br />
            <div class="tips-view">
              <span v-for="item in tipsTj" :key="item">{{ item }}</span>
            </div>
          </div>
          <EchartsComp
            :option="optionTypeData"
            ref="refEchartsCompType"
            :height="'30vh'"
          ></EchartsComp>
        </div>
        <br />
        <div class="grid-content ep-bg-purple">
          <div class="ep-card">隧道评定等级统计</div>
          <EchartsComp
            :option="optionYearData"
            ref="refEchartsCompYear"
            :height="'30vh'"
          ></EchartsComp>
        </div>
      </el-col>
      <el-col :span="13">
        <el-form :model="form" ref="ruleFormRef" label-width="auto" inline>
          <el-form-item label="隧道名称" prop="tunnelName">
            <el-input
              v-model="form.tunnelName"
              :style="{ width: '120px' }"
              placeholder="隧道名称"
            />
          </el-form-item>
          <el-form-item label="隶属单位" prop="affiliatedUnit">
            <el-input
              v-model="form.affiliatedUnit"
              :style="{ width: '120px' }"
              placeholder="隶属单位"
            />
          </el-form-item>
          <el-form-item label="所属路段" prop="affiliatedRoadSection">
            <el-input
              v-model="form.affiliatedRoadSection"
              :style="{ width: '120px' }"
              placeholder="所属路段"
            />
          </el-form-item>
          <el-form-item label="">
            <el-button type="primary" @click="onSubmit">搜索</el-button>
            <el-button @click="resetForm(ruleFormRef)">重置</el-button>
          </el-form-item>
        </el-form>
        <br />
        <el-table :data="tableData" :style="{ height: '75vh' }">
          <el-table-column type="index" fixed="left" label="序号" width="60" />
          <el-table-column
            v-for="(item, index) in tableConfig"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            :min-width="item?.width || 100"
            align="center"
          >
            <template #default="{ row }">
              <span class="ellipsis" v-if="item.label === '土建结构历史评分'">{{
                row[item?.prop + ""] || "--"
              }}</span>
              <!-- <span class="ellipsis" v-if="item.label === '其他设施历史评分'">{{  }}</span> -->
            </template>
          </el-table-column>
          <!-- <el-table-column fixed="right" label="操作">
            <template #default="{ row }">
              <el-button type="primary" @click="showDetails(row)"
                >详情</el-button
              >
            </template>
          </el-table-column> -->
        </el-table>
        <br />
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="pages"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="_getScoreList"
            @current-change="_getScoreList"
          />
        </div>
      </el-col>
    </el-row>
    <erDetails ref="refErDetails" />
  </div>
</template>
<script lang="ts" setup>
import { ref, defineAsyncComponent, onMounted } from "vue";
import {
  getScoreList,
  getTunnelTechnicalAssessmentStatistics,
} from "@/api/project-delivery";
import {
  option,
  tableConfig,
  optionsType,
  optionYear,
  objType,
  series,
  tabsTypes,
} from "./constants";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute(); // 获取路由对象:cite[2]:cite[5]:cite[6]
const EchartsComp = defineAsyncComponent(
  () => import("@/views/home/<USER>/echarts-comp/index.vue")
);
const TabsType = defineAsyncComponent(
  () => import("@/components/TabsType/index.vue")
);
const erDetails = defineAsyncComponent(() => import("./er-details/index.vue"));
const typeList = ["1类", "2类", "3类", "4类", "5类"];
const tabsTjValue = ref("1类");
const tabsQtValue = ref("1类");
const tipsTj = [
  "洞口",
  "洞门",
  "衬砌(结构破损)",
  "衬砌(渗漏水)",
  "路面",
  "检修道",
  "排水设施",
  "吊顶及预埋件",
  "内装饰",
  "交通标志、标线",
];
const form = ref<any>({});
const total = ref(0);
const pages = ref(1);
const pageSize = ref(10);
const tableData = ref<any[]>([]);
const onSubmit = () => {
  console.log(form.value);
  _getScoreList()
};
const ruleFormRef = ref<any>();
const resetForm = (formEl: any | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  _getScoreList()
};
const refErDetails = ref<any>();
const showDetails = (row) => {
  refErDetails.value.openDialog(row);
};
const getList = () => {};
const _getScoreList = async () => {
  try {
    const res = await getScoreList({
      deliveryId: route.query.id,
      pageNum:pages.value,
      pageSize:pageSize.value,
      ...form.value
    });
    tableData.value = res.rows;
    total.value = res.total;
  } catch (err) {
    console.log(err);
  }
};
const loading = ref(false);
const _getTunnelTechnicalAssessmentStatistics = async (diseaseCategory = 1) => {
  try {
    loading.value = true;
    const res = await getTunnelTechnicalAssessmentStatistics(route.query.id, {
      deliveryId: route.query.id,
      diseaseCategory,
    });
    const { civilStructureStats, diseaseConditionStats } = res.data;
    optionTypeFilter(civilStructureStats);
    diseaseConditionStatsFilter(diseaseConditionStats);
    loading.value = false;
  } catch (err) {
    loading.value = false;
    console.log(err);
  }
};
const optionTypeData = ref<any>({});
const refEchartsCompType = ref<any>(null);
const optionTypeFilter = (data: any) => {
  let arr = data.map((item) => {
    return {
      name: `${item.level}  ${item.count} ${item.percentage}%`,
      value: item.count,
    };
  });
  optionTypeData.value = optionsType(arr);
  refEchartsCompType.value.updataMap(optionTypeData.value);
};
const optionYearData = ref<any>({});
const refEchartsCompYear = ref<any>(null);
const diseaseConditionStatsFilter = (data: any) => {
  let xAxisData: any = data.map((item: any) => item.diseaseType);
  console.log(xAxisData);
  let seriesData: any = JSON.parse(JSON.stringify(series));
  data.forEach((item: any) => {
    // xAxisData.push(item.diseaseType);
    seriesData.forEach((item1) => {
      if (item1.name === "状况值0") {
        item1.data.push(item.condition0Count);
      } else if (item1.name === "状况值1") {
        item1.data.push(item.condition1Count);
      } else if (item1.name === "状况值2") {
        item1.data.push(item.condition2Count);
      } else if (item1.name === "状况值3") {
        item1.data.push(item.condition3Count);
      } else if (item1.name === "状况值4") {
        item1.data.push(item.condition4Count);
      } else if (item1.name === "状况值5") {
        item1.data.push(item.condition5Count);
      }
    });
  });
  optionYearData.value = optionYear(xAxisData, seriesData);
  refEchartsCompYear.value.updataMap(optionYearData.value);
};
const tabsChange = (val) => {
  _getTunnelTechnicalAssessmentStatistics(Number(val));
};
onMounted(() => {
  _getTunnelTechnicalAssessmentStatistics();
  _getScoreList();
});
</script>
<style lang="scss" scoped>
.evaluation-results {
  .grid-content {
    box-sizing: border-box;
    padding: 12px 0;
    border-radius: 8px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
    &:hover {
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
    }
    .ep-card {
      height: 24px;
      line-height: 24px;
      border-left: 6px solid #409eff;
      padding-left: 12px;
      font-size: 16px;
    }
  }
  .ep-card-content {
    padding: 12px;
    .ep-card-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;
      .title {
        font-size: 16px;
      }
    }
    .tabs-view {
      min-width: 240px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-left: 24px;
      border-radius: 5px;
      border: 1px solid #006fed;
      background: #e0eaf5;
      color: #006fed;
      span {
        font-size: 18px;
        line-height: 30px;
        padding: 0 24px;
        border-radius: 4px;
        cursor: pointer;
      }
      .tabs-active {
        border: 1px solid #006fed;
        background: #006fed;
        color: #fff;
      }
    }
    .tips-view {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      background: #409eff;
      color: #fff;
      border-radius: 8px;
      padding: 8px 24px;
      span {
        width: 33.33%;
        line-height: 24px;
        cursor: pointer;
      }
    }
  }
}
.ellipsis {
  color: #006fed;
}
</style>