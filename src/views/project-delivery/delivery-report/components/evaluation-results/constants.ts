export const option = {
    title: {
        text: ''
    },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'shadow'
        }
    },
    legend: {
        type: 'scroll',
        orient: 'vertical',
        right: 10,
        top: 0,
        bottom: 20,
    },
    grid: {
        top: '20%',
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
    },
    xAxis: {
        type: 'category',
        data: ['一类', '二类', '三类', '四类', '五类']

    },
    yAxis: {
        type: 'value',
        boundaryGap: [0, 0.01]
    },
    series: [
        {
            name: '土建结构',
            type: 'bar',
            label: {
                show: true,
                position: 'top'
            },
            data: [40, 108, 18, 14, 6]
        },
        {
            name: '其他工程设施',
            type: 'bar',
            label: {
                show: true,
                position: 'top'
            },
            data: [97, 66, 31, 30, 9]
        }
    ]
};
export const tableConfig = [
    {
        label: '隧道名称',
        prop: 'tunnelNameAndHole',
    },
    // {
    //     label: '幅别',
    //     prop: 'tunnelNameAndHole',
    //     width: '60px'
    // },
    {
        label: '土建结构评分',
        prop: 'civilStructureScore',
        width: '120px'
    },
    {
        label: '土建结构等级',
        prop: 'civilStructureLevel',
        width: '120px'
    },
    // {
    //     label: '土建结构历史评分',
    //     prop: 'projectDesc',
    //     width: '80px'
    // },
    {
        label: '其他设施评分',
        prop: 'otherFacilitiesScore',
    },
    {
        label: '其他设施等级',
        prop: 'otherFacilitiesLevel',
    },
    // {
    //     label: '其他设施历史评分',
    //     prop: 'projectDesc',
    //     width: '80px',

    // }
]
export const optionsType = (data: Array<any> = []) => {
    return {
        grid: {
            top: '10%',
            left: '3%',
            right: '5%',
            bottom: '3%',
            containLabel: true
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            type: 'scroll',
            orient: 'vertical',
            right: '10%',
            top: 'center',
            width: '50%',
            align: 'auto',
            itemWidth: 14,
            itemHeight: 14,
            textStyle: {
                // color: '#fff', // 设置红色作为默认文字颜色
                fontSize: 14
            },
        },
        series: [
            {
                name: '',
                type: 'pie',
                // radius: ['60%', '80%'],
                center: ['30%', '50%'],
                avoidLabelOverlap: false,
                padAngle: 5,
                itemStyle: {
                    borderRadius: 5
                },
                label: {
                    show: true,
                    position: 'inner',
                    // formatter: '{d}%'  // {b}表示名称，{d}表示百分比
                    // formatter: '{b}    {d}%',

                },
                labelLine: {
                    show: false
                },
                data,
                // data: [
                //     { value: 1048, name: '环向裂缝 1118个 20%' },
                //     { value: 735, name: '纵向裂缝' },
                //     { value: 580, name: '惨漏水' },
                //     { value: 484, name: '剥落' },
                // ]
            },
            // {
            //     type: 'pie',
            //     radius: ['0%', '0%'],
            //     center: ['30%', '50%'],
            //     label: {
            //         show: true,
            //         position: 'center',
            //         formatter: function () {
            //             return `2847\n病害总个数`;
            //         },
            //         fontSize: 18,
            //         fontWeight: 'bold',
            //     },
            //     data: [{
            //         value: 2847, name: '总数'
            //     }]
            // }
        ]
    }
};
export const optionYear = (xAxisData: Array<any> = [], seriesData: Array<any> = []) => {
    return {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
            }
        },
        legend: {},
        grid: {
            top: '25%',
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: xAxisData,
            // data: ['2021', '2022', '2023', '2024', '2025']
            axisLabel: {
                rotate: -45,  // 文字旋转角度，这里设置为45度
                interval: 0,  // 强制显示所有标签，默认可能会间隔显示
                margin: 15    // 标签与坐标轴的距离
            }
        },
        yAxis: {
            type: 'value',
        },
        series: seriesData,

    };
}
export const objType = {
    condition0Count: '状况值0',
    condition1Count: '状况值1',
    condition2Count: '状况值2',
    condition3Count: '状况值3',
    condition4Count: '状况值4',
    condition5Count: '状况值5',
}
export const series = [
    {
        name: '状况值0',
        type: 'bar',
        stack: 'total',
        label: {
            show: true
        },
        emphasis: {
            focus: 'series'
        },
        data: [],
        barWidth: 25,// 固定宽度（像素）
    },
    {
        name: '状况值1',
        type: 'bar',
        stack: 'total',
        label: {
            show: true
        },
        emphasis: {
            focus: 'series'
        },
        data: [],
        // barWidth: 40,// 固定宽度（像素）
    },
    {
        name: '状况值2',
        type: 'bar',
        stack: 'total',
        label: {
            show: true
        },
        emphasis: {
            focus: 'series'
        },
        data: [],
        // barWidth: 40,// 固定宽度（像素）
    },
    {
        name: '状况值3',
        type: 'bar',
        stack: 'total',
        label: {
            show: true
        },
        emphasis: {
            focus: 'series'
        },
        data: [],
        // barWidth: 40,// 固定宽度（像素）
    },
    {
        name: '状况值4',
        type: 'bar',
        stack: 'total',
        label: {
            show: true
        },
        emphasis: {
            focus: 'series'
        },
        data: [],
        // barWidth: 40,// 固定宽度（像素）
    },
    {
        name: '状况值5',
        type: 'bar',
        stack: 'total',
        label: {
            show: true
        },
        emphasis: {
            focus: 'series'
        },
        data: [],
        // barWidth: 40,// 固定宽度（像素）
    }
]
export const tabsTypes = [
    {
        name: "土建设施",
        value: "1",
    },
    {
        name: "其他工程设施",
        value: "2",
    },
]