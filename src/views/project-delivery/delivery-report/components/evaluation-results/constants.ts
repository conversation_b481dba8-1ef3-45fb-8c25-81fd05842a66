export const option = {
    title: {
        text: ''
    },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'shadow'
        }
    },
    legend: {
        type: 'scroll',
        orient: 'vertical',
        right: 10,
        top: 0,
        bottom: 20,
    },
    grid: {
        top: '20%',
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
    },
    xAxis: {
        type: 'category',
        data: ['一类', '二类', '三类', '四类', '五类']

    },
    yAxis: {
        type: 'value',
        boundaryGap: [0, 0.01]
    },
    series: [
        {
            name: '土建结构',
            type: 'bar',
            label: {
                show: true,
                position: 'top'
            },
            data: [40, 108, 18, 14, 6]
        },
        {
            name: '其他工程设施',
            type: 'bar',
            label: {
                show: true,
                position: 'top'
            },
            data: [97, 66, 31, 30, 9]
        }
    ]
};
export const tableConfig = [
    {
        label: '隧道名称',
        prop: 'projectNo',
    },
    {
        label: '幅别',
        prop: 'projectType',
        width: '60px'
    },
    {
        label: '土建结构评分',
        prop: 'projectStatus',
        width: '120px'
    },
    {
        label: '土建结构等级',
        prop: 'projectDesc',
        width: '120px'
    },
    {
        label: '土建结构历史评分',
        prop: 'projectDesc',
        width: '80px'
    },
    {
        label: '其他设施评分',
        prop: 'projectDesc',
    },
    {
        label: '其他设施等级',
        prop: 'projectDesc',
    },
    {
        label: '其他设施历史评分',
        prop: 'projectDesc',
        width: '80px',
        
    }
]