<template>
  <el-dialog
    width="500"
    :title="'病害详情'"
    v-model="open"
    @close="handelClose"
    append-to-body
  >
    <el-form :model="form" label-width="auto" style="max-width: 600px">
      <el-form-item label="隧道名称">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="病害桩号">
        <div class="input-group">
          <el-input :style="{ width: '50px' }" v-model="form.name" />&nbsp;
          <el-input :style="{ width: '100px' }" v-model="form.name" /> + 
          <el-input :style="{ width: '100px' }" v-model="form.name" />
        </div>
        
      </el-form-item>
      <el-form-item label="病害位置">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="病害类型">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="状况值">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="病害照片">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="长度(mm)">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="宽度(mm)">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="面积(m²)">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="维修状态">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="维修时间">
        <el-input v-model="form.name" />
      </el-form-item>
    </el-form>
  </el-dialog>
</template>
  
  <script lang="ts" setup>
import { ref, defineAsyncComponent } from "vue";
const open = ref<boolean>(false);
const form = ref<any>({});

// 打开弹窗
const openDialog = async (row: any) => {
  console.log(row);
  open.value = true;
};
const loading = ref<boolean>(false);

const handelClose = () => {};

// 暴露变量
defineExpose({
  openDialog,
});
</script>
<style lang="scss" scoped>
.input-group {
  display: flex;
  align-items: center;
}
</style>
  