<template>
  <div class="delivery-report">
    <el-container>
      <el-header>
        <div class="row-bg">
          <span @click="toBack">返回</span>
          <h1>{{ "2025年项目汇报工作" }}</h1>
          <span>时间:{{ days }}</span>
        </div>
      </el-header>
      <el-container>
        <el-aside width="160px">
          <div class="aside-view">
            <div
              :class="[
                'aside-item',
                stores.templateName === item.templateName ? 'active' : '',
              ]"
              v-for="(item, index) in tabsData"
              @click="handleClick(item)"
              :key="index"
            >
              {{ item.label }}
            </div>
          </div>
        </el-aside>
        <el-main>
          <div class="">
            <keep-alive>
              <component :is="layouts[stores.templateName]"></component>
            </keep-alive>
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, defineAsyncComponent } from "vue";
import { getInfo, startReport } from "@/api/project-delivery";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute(); // 获取路由对象:cite[2]:cite[5]:cite[6]
import { tabsData } from "./constants";
const asideAcitve = ref("1");
// 引入组件
const layouts: any = {
  // GIS地图================
  gisMap: defineAsyncComponent(() => import("./components/gis-map/index.vue")),
  // // 项目介绍
  // projectIntroduction: defineAsyncComponent(
  //   () => import("./components/project-introduction/index.vue")
  // ),
  // 项目概况
  projectOverview: defineAsyncComponent(
    () => import("./components/project-overview/index.vue")
  ),
  // 项目明细
  projectDetails: defineAsyncComponent(
    () => import("./components/project-details/index.vue")
  ),
  // 评定结果
  evaluationResults: defineAsyncComponent(
    () => import("./components/evaluation-results/index.vue")
  ),
  // 重点隧道
  keyTunnels: defineAsyncComponent(
    () => import("./components/key-tunnels/index.vue")
  ),
  // 重点隧道
  keyDiseases: defineAsyncComponent(
    () => import("./components/key-diseases/index.vue")
  ),
  // 病害分析
  diseaseAnalysis: defineAsyncComponent(
    () => import("./components/disease-analysis/index.vue")
  ),
  // 三维可视化
  threedVisualization: defineAsyncComponent(
    () => import("./components/threed-visualization/index.vue")
  ),
  // 结论简易
  conclusionSimple: defineAsyncComponent(
    () => import("./components/conclusion-simple/index.vue")
  ),
};
const stores = ref<any>({
  value: "1",
  label: "GIS地图",
  templateName: "gisMap",
});
const handleClick = (row: any) => {
  stores.value = row;
};
const today = () => {
  // 获取当前日期
  const now = new Date();

  // 获取年月日
  const year = now.getFullYear();
  const month = now.getMonth() + 1; // 月份从0开始，所以要加1
  const day = now.getDate();
  // 格式化为"YYYY年MM月DD日 星期X"
  return `${year}年${month}月${day}日`;
};
const days = ref(today());
const _getInfo = async () => {
  const res = await getInfo(route.query.id);
  console.log(res);
};
const toBack = () => {
  ElMessageBox.confirm("您已经阅览项目交付内容,是否汇报完毕?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "返回",
    type: "warning",
  })
    .then(() => {
      startReport(route.query.id).then(() => {
        ElMessage.success("汇报完成");
        router.go(-1);
      });
    })
    .catch(() => {
      router.go(-1);
    });
};
onMounted(() => {
  _getInfo();
});
</script>
<style lang="scss" scoped>
.aside-view {
  height: calc(100vh - 100px);
  .aside-item {
    text-align: center;
    height: 40px;
    line-height: 40px;
    margin-bottom: 3px;
    border-radius: 3px;
    cursor: pointer;
    &:hover {
      color: #fff;
      background: var(--el-color-primary);
    }
  }
  .active {
    color: #fff;
    background: var(--el-color-primary);
  }
}
.el-header {
  background: rgb(48, 65, 86);
}
.el-main {
  padding: 0;
}
.row-bg {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fff;
  h1 {
    margin: 0;
    padding: 0;
    font-size: 28px;
    line-height: 60px;
  }
  span {
    font-size: 16px;
    margin-top: 10px;
    cursor: pointer;
  }
}
</style>