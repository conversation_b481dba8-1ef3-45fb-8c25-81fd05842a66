<template>
    <div class="check-import-container">
        <el-tabs v-model="state.currentTab" type="card" class="demo-tabs" @tab-click="tabAction">
            <el-tab-pane label="外观检查数据统计" name="first">
                <el-table :data="state.list1" style="width: 100%">
                    <el-table-column prop="hole" label="进出洞口类别" width="200" />
                    <el-table-column prop="content" label="洞口图片">
                        <template #default="scope">
                            <div class="img-list">
                                <el-image preview-teleported class="img-item" v-for="(item, index) in scope.row.holeImgs"
                                    :src="item" :key="index" :zoom-rate="1.2" :preview-src-list="scope.row.imgs"
                                    fit="cover" />
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="abnormal" label="质量异常" />
                    <el-table-column prop="record" label="隧道附属设施排查记录" />
                    <el-table-column prop="time" label="隧道标志、标线、如果想检查记录">
                        <template #default="scope">
                            <div class="img-list">
                                <el-image preview-teleported class="img-item" v-for="(item, index) in scope.row.checkImgs"
                                    :src="item" :key="index" :zoom-rate="1.2" :preview-src-list="scope.row.imgs"
                                    fit="cover" />
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </el-tab-pane>
            <el-tab-pane label="衬砌病害录入" name="second">
                <el-table :data="state.list2" style="width: 100%">
                    <el-table-column prop="checkTime" label="检测日期" width="200" />
                    <el-table-column prop="hole" label="洞别" width="200" />
                    <el-table-column prop="pile" label="桩号" width="200" />
                    <el-table-column prop="position" label="所在位置" width="200" />
                    <el-table-column prop="disease" label="病害名称" width="200" />
                    <el-table-column prop="desc" label="状况描述" width="200" />
                    <el-table-column prop="value" label="状况值" />
                    <el-table-column prop="time" label="病害图片">
                        <template #default="scope">
                            <div class="img-list">
                                <el-image preview-teleported class="img-item" v-for="(item, index) in scope.row.imgs"
                                    :src="item" :key="index" :zoom-rate="1.2" :preview-src-list="scope.row.imgs"
                                    fit="cover" />
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </el-tab-pane>
            <el-tab-pane label="路面检测数据" name="third">
                <el-table :data="state.list3" style="width: 100%">
                    <el-table-column prop="checkTime" label="检测日期" width="200" />
                    <el-table-column prop="hole" label="洞别" width="200" />
                    <el-table-column prop="pile" label="桩号" width="200" />
                    <el-table-column prop="position" label="病害位置" width="200" />
                    <el-table-column prop="disease" label="病害名称" width="200" />
                    <el-table-column prop="length" label="长度(m)" width="200" />
                    <el-table-column prop="square" label="面积(㎡)" />
                    <el-table-column prop="time" label="病害图片">
                        <template #default="scope">
                            <div class="img-list">
                                <el-image preview-teleported class="img-item" v-for="(item, index) in scope.row.imgs"
                                    :src="item" :key="index" :zoom-rate="1.2" :preview-src-list="scope.row.imgs"
                                    fit="cover" />
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </el-tab-pane>
            <el-tab-pane label="检修道检测数据" name="fourth">
                <el-table :data="state.list4" style="width: 100%">
                    <el-table-column prop="checkTime" label="检测日期" width="200" />
                    <el-table-column prop="hole" label="洞别" width="200" />
                    <el-table-column prop="pile" label="桩号" width="200" />
                    <el-table-column prop="position" label="所在位置" width="200" />
                    <el-table-column prop="disease" label="病害名称" width="200" />
                    <el-table-column prop="desc" label="状况描述" width="200" />
                    <el-table-column prop="value" label="状况值" />
                    <el-table-column prop="time" label="病害图片">
                        <template #default="scope">
                            <div class="img-list">
                                <el-image preview-teleported class="img-item" v-for="(item, index) in scope.row.imgs"
                                    :src="item" :key="index" :zoom-rate="1.2" :preview-src-list="scope.row.imgs"
                                    fit="cover" />
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </el-tab-pane>
            <el-tab-pane label="机电设施监测数据" name="fifth">
                <el-table :data="state.list5" style="width: 100%">
                    <el-table-column prop="checkTime" label="检测日期" width="200" />
                    <el-table-column prop="hole" label="洞别" width="200" />
                    <el-table-column prop="pile" label="桩号" width="200" />
                    <el-table-column prop="position" label="所在位置" width="200" />
                    <el-table-column prop="equipmentName" label="设备名称" width="200" />
                    <el-table-column prop="equipmentStatus" label="设备状态" width="200" />
                    <el-table-column prop="flaw" label="有无重大缺陷" />
                    <el-table-column prop="time" label="病害图片">
                        <template #default="scope">
                            <div class="img-list">
                                <el-image preview-teleported class="img-item" v-for="(item, index) in scope.row.imgs"
                                    :src="item" :key="index" :zoom-rate="1.2" :preview-src-list="scope.row.imgs"
                                    fit="cover" />
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>
    
<script setup name="technologyNavbar">
import { ref, reactive, onMounted } from 'vue';
const emit = defineEmits(['navTap']);
const props = defineProps({
    titleArr: {
        type: Array,
        default: () => {
            return [];
        }
    }
});
/**
 *  code: 'N125589', name: '中建五局武汉市黄家湖隧道工程',
            km: '25',
            startDate: '2023 05-25',
            endDate: '2025 05-25',
            status: '施工中',
            aPerson: '赵小虎',
            bPerson: '李峰',
            company: '中建五局',
            checkNum: 10,
            remark: '项目正在施工中'
*/
const state = reactive({
    detailForm: {
        startDate: '',
        endDate: ''
    },
    rules: [],
    statusList: [],
    companyList: [],
    currentTab: 'first',
    list1: [
        {
            hole: '左洞',
            holeImgs: ['https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333', 'https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333'],
            checkImgs: ['https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333', 'https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333'],
            abnormal: '暂无',
            record: '早上八点检查完毕'
        }, {
            hole: '左洞',
            holeImgs: ['https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333', 'https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333'],
            checkImgs: ['https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333', 'https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333'],
            abnormal: '暂无',
            record: '早上八点检查完毕'
        }, {
            hole: '左洞',
            holeImgs: ['https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333', 'https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333'],
            checkImgs: ['https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333', 'https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333'],
            abnormal: '暂无',
            record: '早上八点检查完毕'
        }, {
            hole: '左洞',
            holeImgs: ['https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333', 'https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333'],
            checkImgs: ['https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333', 'https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333'],
            abnormal: '暂无',
            record: '早上八点检查完毕'
        }
    ],
    /**
     *  <el-table-column prop="checkTime" label="检测日期" width="200" />
                    <el-table-column prop="hole" label="洞别" width="200" />
                    <el-table-column prop="pile" label="桩号" width="200" />
                    <el-table-column prop="position" label="所在位置" width="200" />
                    <el-table-column prop="disease" label="病害名称" width="200" />
                    <el-table-column prop="desc" label="状况描述" width="200" />
                    <el-table-column prop="value" label="状况值" />
    */
    list2: [
        {
            checkTime: '2023-03-20',
            hole: '右洞',
            pile: 'YK96+016',
            position: '左边墙',
            disease: '斜向裂缝',
            desc: 'L=2.2M,W=0.22MM',
            value: '2',
            imgs: ['https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333']
        }, {
            checkTime: '2023-03-20',
            hole: '右洞',
            pile: 'YK96+016',
            position: '左边墙',
            disease: '斜向裂缝',
            desc: 'L=2.2M,W=0.22MM',
            value: '2',
            imgs: ['https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333']
        }, {
            checkTime: '2023-03-20',
            hole: '右洞',
            pile: 'YK96+016',
            position: '左边墙',
            disease: '斜向裂缝',
            desc: 'L=2.2M,W=0.22MM',
            value: '2',
            imgs: ['https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333']
        }, {
            checkTime: '2023-03-20',
            hole: '右洞',
            pile: 'YK96+016',
            position: '左边墙',
            disease: '斜向裂缝',
            desc: 'L=2.2M,W=0.22MM',
            value: '2',
            imgs: ['https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333']
        }
    ],
    list3: [
        {
            checkTime: '2023-03-20',
            hole: '右洞',
            pile: 'YK96+016',
            position: '行车道',
            disease: '斜向裂缝',
            desc: 'L=2.2M,W=0.22MM',
            value: '2',
            imgs: ['https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333'],
            length: '5.22',
            square: '1.22'
        }, {
            checkTime: '2023-03-20',
            hole: '右洞',
            pile: 'YK96+016',
            position: '行车道',
            disease: '斜向裂缝',
            desc: 'L=2.2M,W=0.22MM',
            value: '2',
            imgs: ['https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333'],
            length: '5.22',
            square: '1.22'
        }, {
            checkTime: '2023-03-20',
            hole: '右洞',
            pile: 'YK96+016',
            position: '行车道',
            disease: '斜向裂缝',
            desc: 'L=2.2M,W=0.22MM',
            value: '2',
            imgs: ['https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333'],
            length: '5.22',
            square: '1.22'
        }, {
            checkTime: '2023-03-20',
            hole: '右洞',
            pile: 'YK96+016',
            position: '行车道',
            disease: '斜向裂缝',
            desc: 'L=2.2M,W=0.22MM',
            value: '2',
            imgs: ['https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333'],
            length: '5.22',
            square: '1.22'
        }
    ],
    list4: [
        {
            checkTime: '2023-03-20',
            hole: '右洞',
            pile: 'YK96+016',
            position: '左边墙',
            disease: '斜向裂缝',
            desc: 'S=0.001㎡',
            value: '2',
            imgs: ['https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333']
        }, {
            checkTime: '2023-03-20',
            hole: '右洞',
            pile: 'YK96+016',
            position: '左边墙',
            disease: '斜向裂缝',
            desc: 'S=0.001㎡',
            value: '2',
            imgs: ['https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333']
        }, {
            checkTime: '2023-03-20',
            hole: '右洞',
            pile: 'YK96+016',
            position: '左边墙',
            disease: '斜向裂缝',
            desc: 'S=0.001㎡',
            value: '2',
            imgs: ['https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333']
        }, {
            checkTime: '2023-03-20',
            hole: '右洞',
            pile: 'YK96+016',
            position: '左边墙',
            disease: '斜向裂缝',
            desc: 'S=0.001㎡',
            value: '2',
            imgs: ['https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333']
        }
    ],
    list5: [
        {
            checkTime: '2023-03-20',
            hole: '右洞',
            pile: 'YK96+016',
            position: '左边墙',
            disease: '斜向裂缝',
            desc: 'S=0.001㎡',
            value: '2',
            imgs: ['https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333'],
            flaw: '暂无',
            equipmentName: '设备1',
            equipmentStatus: '良好'
        }, {
            checkTime: '2023-03-20',
            hole: '右洞',
            pile: 'YK96+016',
            position: '左边墙',
            disease: '斜向裂缝',
            desc: 'S=0.001㎡',
            value: '2',
            imgs: ['https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333'],
            flaw: '暂无',
            equipmentName: '设备1',
            equipmentStatus: '良好'
        }, {
            checkTime: '2023-03-20',
            hole: '右洞',
            pile: 'YK96+016',
            position: '左边墙',
            disease: '斜向裂缝',
            desc: 'S=0.001㎡',
            value: '2',
            imgs: ['https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333'],
            flaw: '暂无',
            equipmentName: '设备1',
            equipmentStatus: '良好'
        }, {
            checkTime: '2023-03-20',
            hole: '右洞',
            pile: 'YK96+016',
            position: '左边墙',
            disease: '斜向裂缝',
            desc: 'S=0.001㎡',
            value: '2',
            imgs: ['https://img0.baidu.com/it/u=*********,2445275954&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333'],
            flaw: '暂无',
            equipmentName: '设备1',
            equipmentStatus: '良好'
        }
    ]
})

const navAction = (index) => {
    emit('navTap', index);
}

const tabAction = () => { };

onMounted(() => {
    state.detailForm = props.projectObj;
})
</script>
    
<style lang="scss" scoped>
.check-import-container {
    display: flex;
    height: 100%;
    width: 100%;

    :deep(.el-tabs) {
        width: 100%;
    }

    .img-list {
        display: flex;

        .img-item {
            width: 50px;
            height: 50px;
            margin-left: 5px;

            &:first-child {
                margin-left: 0;
            }
        }
    }
}
</style>