<template>
    <div class="check-analyse-container">
        <div class="analyse-item" ref="chart1"></div>
        <div class="analyse-item" ref="chart2"></div>
        <div class="analyse-item" ref="chart3"></div>
        <div class="analyse-item" ref="chart4"></div>
        <div class="analyse-item" ref="chart5"></div>
        <div class="analyse-item" ref="chart6"></div>
        <div class="analyse-item special-item" ref="chart7"></div>
    </div>
</template>
    
<script setup name="technologyNavbar">
import { ref, reactive, onMounted } from 'vue';
import * as echarts from "echarts";

const chart1 = ref();
const chart2 = ref();
const chart3 = ref();
const chart4 = ref();
const chart5 = ref();
const chart6 = ref();
const chart7 = ref();

const emit = defineEmits(['navTap']);
const props = defineProps({
    titleArr: {
        type: Array,
        default: () => {
            return [];
        }
    }
});

const initChart1 = () => {
    // 基于准备好的dom，初始化echarts实例
    var myChart = echarts.init(chart1.value);
    // 指定图表的配置项和数据
    var option = {
        title: {
            text: '隧道总数',
            left: '1%'
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            top: '5%',
            left: 'center'
        },
        series: [
            {
                name: 'Access From',
                type: 'pie',
                radius: ['40%', '70%'],
                avoidLabelOverlap: false,
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: 40,
                        fontWeight: 'bold'
                    }
                },
                labelLine: {
                    show: false
                },
                data: [
                    { value: 1048, name: 'Search Engine' },
                    { value: 735, name: 'Direct' },
                    { value: 580, name: 'Email' },
                    { value: 484, name: 'Union Ads' },
                    { value: 300, name: 'Video Ads' }
                ]
            }
        ]
    };
    // 使用刚指定的配置项和数据显示图表。
    myChart.setOption(option);
}

const initChart2 = () => {
    // 基于准备好的dom，初始化echarts实例
    var myChart = echarts.init(chart2.value);
    // 指定图表的配置项和数据
    var option = {
        title: {
            text: '隧道结构形式统计',
            left: '1%'
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            orient: 'vertical',
            left: 'right'
        },
        series: [
            {
                name: 'Access From',
                type: 'pie',
                radius: '50%',
                data: [
                    { value: 1048, name: 'Search Engine' },
                    { value: 735, name: 'Direct' },
                    { value: 580, name: 'Email' },
                    { value: 484, name: 'Union Ads' },
                    { value: 300, name: 'Video Ads' }
                ],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    };
    // 使用刚指定的配置项和数据显示图表。
    myChart.setOption(option);
}

const initChart3 = () => {
    // 基于准备好的dom，初始化echarts实例
    var myChart = echarts.init(chart3.value);
    // 指定图表的配置项和数据
    var option = {
        title: {
            text: '隧道评定等级统计',
            left: '1%'
        },
        legend: {},
        tooltip: {},
        dataset: {
            source: [
                ['product', '2015', '2016', '2017'],
                ['Matcha Latte', 43.3, 85.8, 93.7],
                ['Milk Tea', 83.1, 73.4, 55.1],
                ['Cheese Cocoa', 86.4, 65.2, 82.5],
                ['Walnut Brownie', 72.4, 53.9, 39.1]
            ]
        },
        xAxis: { type: 'category' },
        yAxis: {},
        // Declare several bar series, each will be mapped
        // to a column of dataset.source by default.
        series: [{ type: 'bar' }, { type: 'bar' }, { type: 'bar' }]
    };
    // 使用刚指定的配置项和数据显示图表。
    myChart.setOption(option);
}

const initChart4 = () => {
    // 基于准备好的dom，初始化echarts实例
    var myChart = echarts.init(chart4.value);
    // 指定图表的配置项和数据
    var option = {
        title: {
            text: '隧道病害数据统计',
            left: '1%'
        },
        legend: {},
        tooltip: {},
        dataset: {
            source: [
                ['product', '2015', '2016', '2017'],
                ['Matcha Latte', 43.3, 85.8, 93.7],
                ['Milk Tea', 83.1, 73.4, 55.1],
                ['Cheese Cocoa', 86.4, 65.2, 82.5],
                ['Walnut Brownie', 72.4, 53.9, 39.1]
            ]
        },
        xAxis: { type: 'category' },
        yAxis: {},
        // Declare several bar series, each will be mapped
        // to a column of dataset.source by default.
        series: [{ type: 'bar' }, { type: 'bar' }, { type: 'bar' }]
    };
    // 使用刚指定的配置项和数据显示图表。
    myChart.setOption(option);
};
const initChart5 = () => {
    // 基于准备好的dom，初始化echarts实例
    var myChart = echarts.init(chart5.value);
    // 指定图表的配置项和数据
    var option = {
        title: {
            text: '隧道病害状况统计',
            left: '1%'
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            orient: 'vertical',
            left: 'right'
        },
        series: [
            {
                name: 'Access From',
                type: 'pie',
                radius: '50%',
                data: [
                    { value: 1048, name: 'Search Engine' },
                    { value: 735, name: 'Direct' },
                    { value: 580, name: 'Email' },
                    { value: 484, name: 'Union Ads' },
                    { value: 300, name: 'Video Ads' }
                ],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    };
    // 使用刚指定的配置项和数据显示图表。
    myChart.setOption(option);
};
const initChart6 = () => {
    // 基于准备好的dom，初始化echarts实例
    var myChart = echarts.init(chart6.value);
    // 指定图表的配置项和数据
    var option = {
        title: {
            text: '隧道病害类型占比统计',
            left: '1%'
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            top: '5%',
            left: 'center'
        },
        series: [
            {
                name: 'Access From',
                type: 'pie',
                radius: ['40%', '70%'],
                avoidLabelOverlap: false,
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: 40,
                        fontWeight: 'bold'
                    }
                },
                labelLine: {
                    show: false
                },
                data: [
                    { value: 1048, name: 'Search Engine' },
                    { value: 735, name: 'Direct' },
                    { value: 580, name: 'Email' },
                    { value: 484, name: 'Union Ads' },
                    { value: 300, name: 'Video Ads' }
                ]
            }
        ]
    };
    // 使用刚指定的配置项和数据显示图表。
    myChart.setOption(option);
};

const initChart7 = () => {
    // 基于准备好的dom，初始化echarts实例
    var myChart = echarts.init(chart7.value);
    // 指定图表的配置项和数据
    var option = {
        title: {
            text: '隧道近五年技术状况评分趋势',
            left: '1%'
        },
        xAxis: {
            type: 'category',
            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
                data: [820, 932, 901, 934, 1290, 1330, 1320],
                type: 'line',
                smooth: true
            }
        ]
    };
    // 使用刚指定的配置项和数据显示图表。
    myChart.setOption(option);
}

const state = reactive({
    detailForm: {
        startDate: '',
        endDate: ''
    },
    rules: [],
    statusList: [],
    companyList: []
})

onMounted(() => {
    initChart1();
    initChart2();
    initChart3();
    initChart4();
    initChart5();
    initChart6();
    initChart7();
})
</script>
    
<style lang="scss" scoped>
.check-analyse-container {
    display: flex;
    flex-wrap: wrap;
    width: 100%;

    .analyse-item {
        width: calc(100% / 3);
        height: 500px;
    }

    .special-item {
        width: 100%;
    }
}
</style>