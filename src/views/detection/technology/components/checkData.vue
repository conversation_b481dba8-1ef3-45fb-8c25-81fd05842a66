<template>
    <div class="check-data-container">
        <div class="table-section">
            <el-table :data="state.dataList" style="width: 100%">
                <el-table-column prop="name" label="检查项目名称" width="200" />
                <el-table-column prop="content" label="检查内容" />
                <el-table-column prop="time" label="时间" />
                <el-table-column fixed="right" prop="operation" label="操作" width="300">
                    <template #default="scope">
                        <div class="operate-section">
                            <el-button type="primary" @click="btnAction('modify', scope.row)" class="btn">编辑</el-button>
                            <el-button @click="btnAction('delete', scope.row)" class="btn">删除</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>
    
<script setup name="technologyNavbar">
import { ref, reactive, onMounted } from 'vue';
const emit = defineEmits(['navTap']);
const props = defineProps({
});
/**
 *  code: 'N125589', name: '中建五局武汉市黄家湖隧道工程',
            km: '25',
            startDate: '2023 05-25',
            endDate: '2025 05-25',
            status: '施工中',
            aPerson: '赵小虎',
            bPerson: '李峰',
            company: '中建五局',
            checkNum: 10,
            remark: '项目正在施工中'
*/
const state = reactive({
    dataList: [
        {
            name: '洞口',
            content: '山体滑坡、延时崩塌的征兆以及发展趋势，边坡、碎落台、护坡道的缺口、冲沟、潜流通水、呈现、塔罗机器发展趋势',
            time: '2023-02-20'
        }, {
            name: '洞口',
            content: '山体滑坡、延时崩塌的征兆以及发展趋势，边坡、碎落台、护坡道的缺口、冲沟、潜流通水、呈现、塔罗机器发展趋势',
            time: '2023-02-20'
        }, {
            name: '洞口',
            content: '山体滑坡、延时崩塌的征兆以及发展趋势，边坡、碎落台、护坡道的缺口、冲沟、潜流通水、呈现、塔罗机器发展趋势',
            time: '2023-02-20'
        }
    ]
})

const navAction = (index) => {
    emit('navTap', index);
}

onMounted(() => {
    state.detailForm = props.projectObj;
})
</script>
    
<style lang="scss" scoped>
.check-data-container {
    width: 100%;
}
</style>