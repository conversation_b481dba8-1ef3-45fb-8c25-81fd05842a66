<template>
    <div class="check-rank-container">
        <div class="table-section">
            <el-table :data="state.dataList" style="width: 100%">
                <el-table-column prop="name" label="隧道" width="200" />
                <el-table-column prop="hole" label="洞别" />
                <el-table-column prop="score" label="土建结构技术状况评分" />
                <el-table-column prop="rank" label="土建评定等级" />
                <el-table-column prop="otherScore" label="附属结构技术状况评分" />
                <el-table-column prop="otherRank" label="其他设施评定等级" />
                <el-table-column fixed="right" prop="operation" label="评定结果" width="300">
                    <template #default="scope">
                        <div class="operate-section">
                            <el-button type="primary" @click="btnAction('modify', scope.row)" class="btn">查看</el-button>
                            <el-button @click="btnAction('delete', scope.row)" class="btn">导出</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>
    
<script setup name="technologyNavbar">
import { ref, reactive, onMounted } from 'vue';
const emit = defineEmits(['navTap']);
const props = defineProps({
});
/**
 *  code: 'N125589', name: '中建五局武汉市黄家湖隧道工程',
            km: '25',
            startDate: '2023 05-25',
            endDate: '2025 05-25',
            status: '施工中',
            aPerson: '赵小虎',
            bPerson: '李峰',
            company: '中建五局',
            checkNum: 10,
            remark: '项目正在施工中'
*/
const state = reactive({
    dataList: [
        {
            name: '洞口',
            hole: '左洞',
            score: '81.5',
            rank: '2类',
            otherScore: '100',
            otherRank: '1类'
        }, {
            name: '洞口',
            hole: '左洞',
            score: '81.5',
            rank: '2类',
            otherScore: '100',
            otherRank: '1类'
        }, {
            name: '洞口',
            hole: '左洞',
            score: '81.5',
            rank: '2类',
            otherScore: '100',
            otherRank: '1类'
        }, {
            name: '洞口',
            hole: '左洞',
            score: '81.5',
            rank: '2类',
            otherScore: '100',
            otherRank: '1类'
        }
    ]
})

const navAction = (index) => {
    emit('navTap', index);
}

onMounted(() => {
    state.detailForm = props.projectObj;
})
</script>
    
<style lang="scss" scoped>
.check-rank-container {
    width: 100%;
}
</style>