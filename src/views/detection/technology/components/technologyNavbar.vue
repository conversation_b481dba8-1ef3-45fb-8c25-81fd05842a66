<template>
    <div class="technology-navbar">
        <div class="nav-item" v-for="(item, index) in props.titleArr" :key="index">
            <span @click="navAction(index)" class="nav-title" :class="{select: item.select}">{{ item.title }}</span>
        </div>
    </div>
</template>
    
<script setup name="technologyNavbar">
import { ref, reactive, onMounted } from 'vue';
const emit = defineEmits(['navTap']);
const props = defineProps({
    titleArr: {
        type: Array,
        default: () => {
            return [];
        }
    }
});
/**
 *  code: 'N125589', name: '中建五局武汉市黄家湖隧道工程',
            km: '25',
            startDate: '2023 05-25',
            endDate: '2025 05-25',
            status: '施工中',
            aPerson: '赵小虎',
            bPerson: '李峰',
            company: '中建五局',
            checkNum: 10,
            remark: '项目正在施工中'
*/
const state = reactive({
    detailForm: {
        startDate: '',
        endDate: ''
    },
    rules: [],
    statusList: [],
    companyList: []
})

const navAction = (index) => {
    emit('navTap', index);
}

onMounted(() => {
    state.detailForm = props.projectObj;
})
</script>
    
<style lang="scss" scoped>
.technology-navbar {
    display: flex;

    .nav-item {
        width: 25%;

        .nav-title {
            font-size: 15px;
            padding: 5px 0;
            cursor: pointer;
        }

        .select {
            color: #409eff;
            cursor: pointer;
            padding: 5px 0;
            border-bottom: 2px #409eff solid;
        }
    }
}
</style>