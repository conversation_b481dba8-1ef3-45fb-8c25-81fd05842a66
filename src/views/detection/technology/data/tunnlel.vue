<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm"  :inline="true" v-show="showSearch" label-width="auto">
      <el-form-item label="隶属单位" prop="affiliatedUnit">
        <el-input
            v-model="queryParams.affiliatedUnit"
            placeholder="请输入隶属单位"
            clearable
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属路段" prop="affiliatedRoadSection">
        <el-input
            v-model="queryParams.affiliatedRoadSection"
            placeholder="请输入所属路段"
            clearable
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="隧道名称" prop="tunnelName">
        <el-input
          v-model="queryParams.tunnelName"
          placeholder="请输入隧道名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="检测时间" prop="onDate"  >
        <el-date-picker
            v-model="queryParams.onDate"
            type="daterange"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            range-separator="~"
            start-placeholder="开始日期"
            end-placeholder="结束日期" style="width: 270px">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary"  size="mini" @click="handleQuery">搜索</el-button>
        <el-button  size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          @click="handleAdd"
          v-hasPermi="['tunnel:manage:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['tunnel:manage:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['tunnel:manage:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            size="mini"
            @click="handleBatchAdd"
            v-hasPermi="['tunnel:manage:batchAdd']"
        >批量新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          size="mini"
          @click="handleExport"
          v-hasPermi="['tunnel:manage:export']"
          v-if="isExport"
        >导出</el-button>
        <el-button type="primary" v-if="isExportIng" :loading="true">导出中</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="manageList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" fixed="left" label="序号" width="70" />
      <el-table-column label="隶属单位" align="center" prop="affiliatedUnit" min-width="180" show-tooltip-when-overflow="true"/>
      <el-table-column label="所属路段" align="center" prop="affiliatedRoadSection" min-width="180" show-tooltip-when-overflow="true"/>
      <el-table-column label="路段编号" align="center" prop="tunnelRoadNumber" min-width="150" show-tooltip-when-overflow="true"/>
      <!-- <el-table-column label="隧道编码" align="center" prop="tunnelCode" min-width="150" show-tooltip-when-overflow="true"/> -->
      <el-table-column label="隧道名称" align="center" prop="tunnelName" min-width="120" show-tooltip-when-overflow="true"/>
      <el-table-column label="隧道洞别" align="center" prop="hole" min-width="120" show-tooltip-when-overflow="true"/>
      <el-table-column label="起点桩号" align="center" prop="startingStation" min-width="120" show-tooltip-when-overflow="true"/>
       <el-table-column label="止点桩号" align="center" prop="endingStation" min-width="120" show-tooltip-when-overflow="true"/>
      <el-table-column label="隧道长度" align="center" prop="tunnelLength" min-width="120" show-tooltip-when-overflow="true"/>
      <!-- <el-table-column label="隶属单位" align="center" prop="affiliatedUnit" width="150" show-tooltip-when-overflow="true"/> -->
      <!-- <el-table-column label="结构形式" align="center" prop="structuralStyle" width="150" show-tooltip-when-overflow="true"/> -->
      <!--<el-table-column label="隧道交工时间" align="center" prop="tunnelHandoverTime" width="150" show-tooltip-when-overflow="true">-->
      <!--</el-table-column>-->
      <!--<el-table-column label="隧道竣工时间" align="center" prop="tunnelCompletionTime" width="150" show-tooltip-when-overflow="true">-->
      <!--</el-table-column>-->
      <!--<el-table-column label="经度" align="center" prop="longitude" width="150" show-tooltip-when-overflow="true"/>-->
      <!--<el-table-column label="纬度" align="center" prop="latitude" width="150" show-tooltip-when-overflow="true"/>-->
      
      <!-- <el-table-column label="截至桩号" align="center" prop="endingStation" width="150" show-tooltip-when-overflow="true"/> -->
      <!-- <el-table-column label="隧道长度" align="center" prop="tunnelLength" width="150" show-tooltip-when-overflow="true"/> -->
      <!--<el-table-column label="埋深起" align="center" prop="burialDepthBegin" width="150" show-tooltip-when-overflow="true"/>-->
      <!--<el-table-column label="埋深止" align="center" prop="buriedDepthStop" width="150" show-tooltip-when-overflow="true"/>-->
      <!-- <el-table-column label="施工期中心桩号" align="center" prop="constructionCentralNumber" width="150" show-tooltip-when-overflow="true"/> -->
      <!-- <el-table-column label="运营期中心桩号" align="center" prop="operateCentralNumber" width="150" show-tooltip-when-overflow="true"/> -->
      
      <!--<el-table-column label="图片" align="center" prop="pictureUrl"  >-->
        <!--<template v-slot:default="scope">-->
          <!--<el-image :src="scope.row.pictureUrl" preview-teleported :preview-src-list="scope.row.prePictureList" fit="cover"/>-->
        <!--</template>-->
      <!--</el-table-column>-->

        <!--<el-table-column label="三维隧道编码" align="center"  prop="threeViewTunnelCode" width="150" show-tooltip-when-overflow="true"/>-->
        <!--<el-table-column label="隧道图片英文名" align="center" prop="tunnelEnglishName" width="150" show-tooltip-when-overflow="true"/>-->
        <!--<el-table-column label="右洞是否实现3d" align="center" prop="rightIsHasThreeView" width="150" show-tooltip-when-overflow="true">-->
            <!--<template v-slot:default="scope">-->
                <!--{{scope.row.rightIsHasThreeView==0?'否':'是'}}-->
            <!--</template>-->
        <!--</el-table-column>-->
        <!--<el-table-column label="左洞是否实现3d" align="center" prop="leftIsHasThreeView" width="150" show-tooltip-when-overflow="true">-->
            <!--<template v-slot:default="scope">-->
                <!--{{scope.row.leftIsHasThreeView==0?'否':'是'}}-->
            <!--</template>-->
        <!--</el-table-column>-->
        <!--<el-table-column label="隧道桩号总长" align="center"   prop="tunnelPots" width="150" show-tooltip-when-overflow="true"/>-->
        <!--<el-table-column label="单张图片长度桩号" align="center" prop="sectionPots" width="150" show-tooltip-when-overflow="true"/>-->
        <!--<el-table-column label="单张图片宽度像素值" align="center" prop="pixSection" width="150" show-tooltip-when-overflow="true"/>-->
        <!--<el-table-column label="图片高度像素值" align="center" prop="pixHeight" width="150" show-tooltip-when-overflow="true"/>-->
        <!--<el-table-column label="隧道节(单张图片)数量" align="center" prop="sections" width="150" show-tooltip-when-overflow="true"/>-->
        <!--<el-table-column label="起点节编号" align="center" prop="startSection" width="150" show-tooltip-when-overflow="true">-->
            <!--<template v-slot:default="scope">-->
                <!--{{scope.row.startSection==0?'入口外':'入口'}}-->
            <!--</template>-->
        <!--</el-table-column>-->
        <!--<el-table-column label="3D用图片URL路径" align="center" prop="picurePath" width="150" show-tooltip-when-overflow="true">-->
            <!--<template v-slot:default="scope">-->
                <!--<el-image :src="scope.row.picurePath" fit="cover"/>-->
            <!--</template>-->
        <!--</el-table-column>-->

      <!-- <el-table-column label="创建人编码" align="center" prop="creatorCode" width="150" show-tooltip-when-overflow="true"/> -->
      <!-- <el-table-column label="创建人名称" align="center" prop="creatorName" width="150" show-tooltip-when-overflow="true"/> -->
      <!-- <el-table-column label="创建时间" align="center" prop="creatorTime" width="180" show-tooltip-when-overflow="true"> -->
      <!-- </el-table-column> -->
      <!-- <el-table-column label="修改人编码" align="center" prop="modifierCode" width="150" show-tooltip-when-overflow="true"/> -->
      <!-- <el-table-column label="修改人名称" align="center" prop="modifierName" width="150" show-tooltip-when-overflow="true"/> -->
      <!-- <el-table-column label="修改时间" align="center" prop="modifierTime" width="180" show-tooltip-when-overflow="true"> -->
      <!-- </el-table-column> -->
      <el-table-column fixed="right" label="操作" align="center" class-name="small-padding fixed-width"
                       width="200">
        <template v-slot:default="scope">
          <el-button
            size="mini"
            type="success"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['tunnel:manage:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(scope.row)"
            v-hasPermi="['tunnel:manage:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="queryParams.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="queryParams.pageSize"
        :total="total"
        layout="->,total, sizes, prev, pager, next, jumper"
        style="margin-top:10px;text-align: center"
    >
    </el-pagination>

    <!-- 添加或修改隧道档案管理基本信息对话框 -->
    <el-dialog :title="title" v-model="open" width="850px" >
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-row>
            <el-col :span="10">
                <el-form-item label="隧道总编码" prop="totalTunnelCode">
                    <el-input v-model="form.totalTunnelCode" placeholder="请输入隧道总编码" class="myInput"/>
                </el-form-item>
            </el-col>
          <el-col :span="10">
            <el-form-item label="隧道编码" prop="tunnelCode">
              <el-input v-model="form.tunnelCode" placeholder="请输入隧道编码" class="myInput"/>
            </el-form-item>
          </el-col>
        </el-row>
          <el-row>
              <el-col :span="10">
                  <el-form-item label="隧道名称" prop="tunnelName">
                      <el-input v-model="form.tunnelName" placeholder="请输入隧道名称" class="myInput"/>
                  </el-form-item>
              </el-col>
              <el-col :span="10">
                  <el-form-item label="隧道路线编号" prop="tunnelRoadNumber">
                      <el-input v-model="form.tunnelRoadNumber" placeholder="请输入隧道路线编号" class="myInput"/>
                  </el-form-item>

              </el-col>
          </el-row>
        <el-row>
          <el-col :span="10">
            <el-form-item label="隶属单位" prop="affiliatedUnit">
<!--              <el-select v-model="form.affiliatedUnit" placeholder="请选择隶属单位" clearable>-->
<!--                <el-option v-for="item in companyList" :key="item.value" :label="item.label"-->
<!--                           :value="item.value" />-->
<!--              </el-select>-->
              <el-input v-model="form.affiliatedUnit" placeholder="请输入隶属单位" class="myInput"/>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="所属路段" prop="affiliatedRoadSection">
<!--              <el-select v-model="form.affiliatedRoadSection" placeholder="请选择所属路段" clearable>-->
<!--                <el-option v-for="item in wayList" :key="item.value" :label="item.label"-->
<!--                           :value="item.value" />-->
<!--              </el-select>-->
              <el-input v-model="form.affiliatedRoadSection" placeholder="请输入所属路段" class="myInput"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="隧道洞别" prop="hole">
              <el-select v-model="form.hole" placeholder="请选择隧道洞别" clearable>
                <el-option v-for="item in holeList" :key="item.value" :label="item.label"
                           :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="结构形式" prop="structuralStyle">
<!--              <el-select v-model="form.structuralStyle" placeholder="请选择结构形式" clearable>-->
<!--                <el-option v-for="item in structureList" :key="item.value" :label="item.label"-->
<!--                           :value="item.value" />-->
<!--              </el-select>-->
              <el-input v-model="form.structuralStyle" placeholder="请输入结构形式" class="myInput"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="隧道交工时间" prop="tunnelHandoverTime">
              <el-date-picker clearable
                              v-model="form.tunnelHandoverTime"
                              type="date"
                              format="YYYY-MM-DD"
                              value-format="YYYY-MM-DD"
                              placeholder="请选择隧道交工时间" style="width: 215px">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="隧道竣工时间" prop="tunnelCompletionTime">
              <el-date-picker clearable
                              v-model="form.tunnelCompletionTime"
                              type="date"
                              format="YYYY-MM-DD"
                              value-format="YYYY-MM-DD"
                              placeholder="请选择隧道竣工时间" style="width: 215px">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="经度" prop="longitude">
              <el-input v-model="form.longitude" placeholder="请输入经度" class="myInput"/>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="纬度" prop="latitude">
              <el-input v-model="form.latitude" placeholder="请输入纬度" class="myInput"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="起始桩号" prop="startingStation">
              <el-input v-model="form.startingStation" placeholder="请输入起始桩号" class="myInput"/>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="截至桩号" prop="endingStation">
              <el-input v-model="form.endingStation" placeholder="请输入截至桩号" class="myInput"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="隧道长度" prop="tunnelLength">
              <el-input v-model="form.tunnelLength" placeholder="请输入隧道长度" class="myInput"/>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="埋深起" prop="burialDepthBegin">
              <el-input v-model="form.burialDepthBegin" placeholder="请输入埋深起" class="myInput"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="埋深止" prop="buriedDepthStop">
              <el-input v-model="form.buriedDepthStop" placeholder="请输入埋深止" class="myInput"/>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="施工期中心桩号" prop="constructionCentralNumber">
              <el-input v-model="form.constructionCentralNumber" placeholder="请输入施工期中心桩号" class="myInput"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="10">
            <el-form-item label="运营期中心桩号" prop="operateCentralNumber">
              <el-input v-model="form.operateCentralNumber" placeholder="请输入运营期中心桩号" class="myInput"/>
            </el-form-item>
          </el-col>
            <el-col :span="10">
                <el-form-item label="单张图片长度桩号" prop="sectionPots">
                    <el-input v-model="form.sectionPots" placeholder="请输入单张图片长度桩号" class="myInput"/>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row>
            <el-col :span="10">
                <el-form-item label="隧道节(单张)数量" prop="sections">
                    <el-input v-model="form.sections" placeholder="请输入隧道节（单张图片）数量" class="myInput"/>
                </el-form-item>
            </el-col>
            <el-col :span="10">
                <el-form-item label="起点节编号" prop="startSection">
                    <el-select v-model="form.startSection" placeholder="请选择">
                        <el-option label="入口外" :value="0"></el-option>
                        <el-option label="入口"   :value="1"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
        </el-row>
          <el-row>
              <el-col :span="10">
                  <el-form-item label="隧道图片英文名" prop="tunnelEnglishName">
                      <el-input v-model="form.tunnelEnglishName" placeholder="请输入隧道图片英文名" class="myInput"/>
                  </el-form-item>
              </el-col>
              <el-col :span="10">
                  <el-form-item label="右洞是否实现3d" prop="rightIsHasThreeView">
                      <el-select v-model="form.rightIsHasThreeView" placeholder="请选择">
                          <el-option label="否" :value="0"></el-option>
                          <el-option label="是" :value="1"></el-option>
                      </el-select>
                  </el-form-item>
              </el-col>
          </el-row>
          <el-row>
              <el-col :span="10">
                  <el-form-item label="右洞是否实现3d" prop="leftIsHasThreeView">
                      <el-select v-model="form.leftIsHasThreeView" placeholder="请选择">
                          <el-option label="否" :value="0"></el-option>
                          <el-option label="是" :value="1"></el-option>
                      </el-select>
                  </el-form-item>
              </el-col>
              <el-col :span="10">
                  <el-form-item label="隧道桩号总长" prop="tunnelPots">
                      <el-input v-model="form.tunnelPots" placeholder="请输入隧道桩号总长" class="myInput"/>
                  </el-form-item>
              </el-col>
          </el-row>
          <el-row>
              <el-col :span="10">
                  <el-form-item label="单张图片宽度像素值" prop="pixSection">
                      <el-input v-model="form.pixSection" placeholder="请输入单张图片宽度像素值" class="myInput"/>
                  </el-form-item>
              </el-col>
              <el-col :span="10">
                  <el-form-item label="图片高度像素值" prop="pixHeight">
                      <el-input v-model="form.pixHeight" placeholder="请输入图片高度像素值" class="myInput"/>
                  </el-form-item>
              </el-col>
          </el-row>
          <el-row>
              <el-col :span="10">
                  <el-form-item label="图片" prop="pictureUrl">
                      <el-upload v-model="form.pictureUrl" class="upload-demo"
                                 action="/dev-api/tunnel/upload/uploadPicture"
                                 :on-remove="handleRemove"
                                 :on-success="fileUpload"
                                 :show-file-list="true"
                                 :headers="{'Authorization': 'Bearer ' + token}"
                                 list-type="picture-card"
                                 :file-list="fileLists"
                                 :limit="1"
                                 :on-exceed="handleExceed">
                          <el-button type="primary">上传图片</el-button>
                      </el-upload>
                  </el-form-item>
              </el-col>
              <el-col :span="10">
                  <el-form-item label="3D用图片" prop="picurePath">
                      <el-upload v-model="form.picurePath" class="upload-demo"
                                 action="/dev-api/tunnel/upload/uploadPicture"
                                 :on-remove="handleRemove3D"
                                 :on-success="fileUpload3D"
                                 :show-file-list="true"
                                 :headers="{'Authorization': 'Bearer ' + token}"
                                 list-type="picture-card"
                                 :file-list="fileLists3D"
                                 :limit="1"
                                 :on-exceed="handleExceed">
                          <el-button type="primary">上传图片</el-button>
                      </el-upload>
                  </el-form-item>
              </el-col>
          </el-row>
          <el-row>
              <el-col :span="10">
                  <el-form-item label="附件压缩包" prop="zipPath">
                      <el-upload  class="upload-demo"
                                 action="/dev-api/tunnel/upload/uploadPicture"
                                 :on-remove="handleRemoveZip"
                                 :on-success="fileUploadZip"
                                 :show-file-list="true"
                                 :headers="{'Authorization': 'Bearer ' + token}"
                                 :file-list="fileListsZip"
                                 :limit="1"
                                  drag
                                 accept=".zip,.rar"
                                 :on-exceed="handleExceed">
                          <i class="el-icon-upload"></i>
                          <div class="el-upload__text">文件拖到此处/<em>点击上传</em></div>
                      </el-upload>
                      <a :href="form.zipPath" style="color: #409EFF">下载压缩包</a>
                  </el-form-item>
              </el-col>
          </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog v-model="batchAddOpen" :title="batchAddTitle" width="40%" destroy-on-close="true" @closed="closeFileUpload()">
      <tunnlelFileUpload></tunnlelFileUpload>
    </el-dialog>
  </div>
</template>

<script>
import { listManage, getManage, delManage, addManage, updateManage,exportData } from "@/api/system/tunnlel";
import tunnlelFileUpload from "./fileUpload/tunnlelFileUpload.vue";
import axios from 'axios'
import { getToken } from "@/utils/auth";


export default {
  name: "Manage",
  components: {tunnlelFileUpload},
  data() {
    return {
        zipName:null,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 隧道档案管理基本信息表格数据
      manageList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      batchAddTitle: "",
      batchAddOpen: false,
      token:null,
      isExport:true,
      isExportIng:false,
      fileLists:[],
      fileLists3D:[],
      fileListsZip:[],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        totalTunnelCode: null,
        tunnelCode: null,
        tunnelName: null,
        affiliatedUnit: null,
        affiliatedRoadSection: null,
        hole: null,
        structuralStyle: null,
        tunnelHandoverTime: null,
        tunnelCompletionTime: null,
        longitude: null,
        latitude: null,
        startingStation: null,
        endingStation: null,
        tunnelLength: null,
        burialDepthBegin: null,
        buriedDepthStop: null,
        constructionCentralNumber: null,
        operateCentralNumber: null,
        pictureUrl: null,
        creatorCode: null,
        creatorName: null,
        creatorTime: null,
        createBeginTime: null,
        createEndTime: null,
        modifierCode: null,
        modifierName: null,
        modifierTime: null,
        isAvailable: null,
        isDeleted: null,
        threeViewTunnelCode: null,
        tunnelEnglishName: null,
        rightIsHasThreeView: null,
        leftIsHasThreeView: null,
        tunnelPots: null,
        sectionPots: null,
        pixSection: null,
        pixHeight: null,
        sections: null,
        startSection: null,
        picurePath: null,
        zipPath:null
      },
      // 表单参数
      form: {
        date1:[]
      },
      // 表单校验
      rules: {
        totalTunnelCode: [{required: true, message: '隧道总编码不能为空', trigger: 'blur'}],
        tunnelCode: [{required: true, message: '隧道编码不能为空', trigger: 'blur'}],
        tunnelName: [{required: true, message: '隧道名称不能为空', trigger: 'blur'}],
        longitude:[{ pattern: /^\d{1,10}(\.\d{0,7})?$/, message: '只能输数字，最多10位整数，最多7位小数', trigger: 'blur' }],
        latitude:[{ pattern: /^\d{1,10}(\.\d{0,7})?$/, message: '只能输数字，最多10位整数，最多7位小数', trigger: 'blur' }],
        pileNumber: [{required: true, message: '桩号不能为空', trigger: 'blur'}],
        conditionValue:[{ pattern: /^[0-4]?$/, message: '只能输0-4的整数', trigger: 'blur' },{ required: true, message: '状况值不能为空', trigger: 'blur'}],
      },
      // companyList: [
      //   {
      //     label: '中国交建',
      //     value: 'zgjj'
      //   }, {
      //     label: '中交三局',
      //     value: 'zjsj'
      //   }, {
      //     label: '中铁三局',
      //     value: 'ztsj'
      //   }
      // ],
      holeList: [
        {
          label: '左洞',
          value: '左洞'
        }, {
          label: '右洞',
          value: '右洞'
        }, {
          label: '贯穿',
          value: '贯穿'
        }
      ],
      // structureList: [
      //   {
      //     label: '楔形结构',
      //     value: '楔形结构'
      //   }, {
      //     label: '倒三角',
      //     value: '倒三角'
      //   }, {
      //     label: '梯形结构',
      //     value: '梯形结构'
      //   }
      // ],
      // wayList: [
      //   {
      //     label: '武汉市东湖路',
      //     value: '武汉市东湖路'
      //   }, {
      //     label: '杭州湖滨路',
      //     value: '杭州湖滨路'
      //   }, {
      //     label: '上海黄浦路',
      //     value: '上海黄浦路'
      //   }
      // ]
    };
  },
  created() {
    this.getList();
    this.token=getToken();
  },
  methods: {
    /** 查询隧道档案管理基本信息列表 */
    getList() {
      this.loading = true;
      if(this.queryParams.onDate!=null&&this.queryParams.onDate.length!=0){
        this.queryParams.createBeginTime=this.queryParams.onDate[0];
        this.queryParams.createEndTime=this.queryParams.onDate[1];
      }
      listManage(this.queryParams).then(response => {
        this.manageList = response.rows;
        this.manageList.forEach(item=>{Object.assign(item,{prePictureList:[item.pictureUrl]})});
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    closeFileUpload(){
      this.handleQuery();
    },
    sizeChangeHandle(val) {
      this.queryParams.pageSize = val;
      this.queryParams.pageNum = 1;
      this.getList();
    },
    currentChangeHandle: function currentChangeHandle(val) {
      this.queryParams.pageNum = val;
      this.getList();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        totalTunnelCode: null,
        tunnelCode: null,
        tunnelName: null,
        affiliatedUnit: null,
        affiliatedRoadSection: null,
        hole: null,
        structuralStyle: null,
        tunnelHandoverTime: null,
        tunnelCompletionTime: null,
        longitude: null,
        latitude: null,
        startingStation: null,
        endingStation: null,
        tunnelLength: null,
        burialDepthBegin: null,
        buriedDepthStop: null,
        constructionCentralNumber: null,
        operateCentralNumber: null,
        pictureUrl: null,
        tunnelRoadNumber: null,
        creatorCode: null,
        creatorName: null,
        creatorTime: null,
        modifierCode: null,
        modifierName: null,
        modifierTime: null,
        isAvailable: null,
        isDeleted: null,
        threeViewTunnelCode: null,
        tunnelEnglishName: null,
        rightIsHasThreeView: null,
        leftIsHasThreeView: null,
        tunnelPots: null,
        sectionPots: null,
        pixSection: null,
        pixHeight: null,
        sections: null,
        startSection: null,
        picurePath: null,
        zipPath:null
      };
      this.resetForm("form");
      this.fileLists=[];
        this.fileLists3D=[];
        this.fileListsZip=[];
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.createEndTime=null;
      this.queryParams.createBeginTime=null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    handleBatchAdd() {
      this.batchAddOpen = true;
      this.batchAddTitle = "批量新增";
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加隧道档案管理基本信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getManage(id).then(response => {
        this.form = response.data;
        if(response.data.pictureUrl!=null){
          let pictureList = [response.data.pictureUrl];
          this.fileLists = pictureList.map(item => {
            return {
              name: item,
              url: item
            }
          });
        }
          if(response.data.picurePath!=null){
              let pictureList = [response.data.picurePath];
              this.fileLists3D = pictureList.map(item => {
                  return {
                      name: item,
                      url: item
                  }
              });
          }
          if(response.data.zipPath!=null){
              let pictureList = [response.data.zipPath];
              this.fileListsZip = pictureList.map(item => {
                  return {
                      name: item,
                      url: item
                  }
              });
          }
        this.open = true;
        this.title = "修改隧道档案管理基本信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateManage(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addManage(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除隧道档案管理基本信息编号为"' + ids + '"的数据项？').then(function() {
        return delManage(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.manageList.length == 0) {
        this.$alert('无导出数据', '', {
          confirmButtonText: '确定'
        })
      } else {
        //开启loading
        this.isExport = false;
        this.isExportIng = true;
        if (this.queryParams.onDate!=null&&this.queryParams.onDate.length != 0) {
          this.queryParams.createBeginTime = this.queryParams.onDate[0];
          this.queryParams.createEndTime = this.queryParams.onDate[1];
        }
        exportData(this.queryParams).then(res => {
          if (res.code == '500') {
            this.$modal.msgSuccess("导出失败，请联系开发人员");
            return
          } else {
            this.$modal.msgSuccess("导出成功");
          }
          let blob = new Blob([res], {type: "application/force-download"})
          console.log(blob);
          let fileReader = new FileReader()
          fileReader.readAsDataURL(blob)
          fileReader.onload = (e) => {
            let a = document.createElement('a')
            let date = new Date();
            let year = date.getFullYear();
            let month = date.getMonth()+1;
            let day = date.getDate();
            a.download = `导出数据${year+"_"+month+"_"+day}.xlsx`
            a.href = e.target.result
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
          }
          this.isExport = true;
          this.isExportIng = false;
        })

      }
    },
    handleExceed(files, fileList){
      this.$modal.msgWarning(`只能上传一个文件`);
    },
    //上传图片
    fileUpload: function (response, file, fileList) {

      let pictureList = [response.msg];
      this.fileLists = pictureList.map(item => {
        return {
          name: item,
          url: item
        }
      });
      var url = response.msg;
      this.form.pictureUrl = url;
    },
    //上传图片
    handleRemove() {
      this.form.pictureUrl = null;
    },
      //上传图片
      fileUpload3D: function (response, file, fileList) {
          let pictureList = [response.msg];
          this.fileLists3D = pictureList.map(item => {
              return {
                  name: item,
                  url: item
              }
          });
          var url = response.msg;
          this.form.picurePath = url;
      },
      //上传图片
      handleRemove3D() {
          this.form.picurePath = null;
      },
      //上传图片
      fileUploadZip: function (response, file, fileList) {
        let _this=this;
          let pictureList = [response.msg];
          let target=JSON.parse(JSON.stringify(file));
          this.zipName=target.name;
           this.fileListsZip = pictureList.map(item => {
              return {
                  name: _this.zipName,
                  url: item
              }
          });
          var url = response.msg;
          this.form.zipPath = url;
      },
      //上传图片
      handleRemoveZip() {
          this.form.zipPath = null;
      },
  }
};
</script>

<style lang="scss" scoped>

.myInput {width: 215px}
</style>
