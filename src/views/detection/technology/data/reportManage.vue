<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="auto"

    >
    <el-form-item label="隶属单位" prop="affiliatedUnit">
        <el-input
          v-model="queryParams.affiliatedUnit"
          placeholder="请输入隶属单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="项目编码" prop="projectCode">
        <el-input
          v-model="queryParams.projectCode"
          placeholder="请输入项目编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="项目名称" prop="projectName">
        <el-select
          v-model="queryParams.projectName"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="item in projects"
            :key="item.projectCode"
            :label="item.projectName"
            :value="item.projectName"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="所属路段" prop="affiliatedRoadSection">
        <el-input
          v-model="queryParams.affiliatedRoadSection"
          placeholder="请输入所属路段"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="隧道名称" prop="tunnelName">
        <el-select
          v-model="queryParams.tunnelName"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="item in tunnels"
            :key="item.tunnelCode"
            :label="item.tunnelName"
            :value="item.tunnelName"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <!-- <el-form-item label="评定时间" prop="onDate">
        <el-date-picker
          v-model="queryParams.onDate"
          type="daterange"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          range-separator="~"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" size="mini" @click="handleQuery"
          >搜索</el-button
        >
        <el-button size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          @click="handleAdd"
          v-hasPermi="['project:report:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['project:report:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          size="mini"
          @click="handleExport"
          v-hasPermi="['project:report:export']"
          v-if="isExport"
          >导出</el-button
        >
        <el-button type="primary" v-if="isExportIng" :loading="true"
          >导出中</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="reportList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
       <el-table-column type="index" label="序号" align="center" width="60" />
      <el-table-column
        label="项目名称"
        align="center"
        prop="projectName"
        min-width="100"
        show-tooltip-when-overflow="true"
      />
      <el-table-column
        label="隶属单位"
        align="center"
        prop="affiliatedUnit"
        width="180"
        show-tooltip-when-overflow="true"
      />
       <el-table-column
        label="所属路段"
        align="center"
        prop="affiliatedRoadSection"
        min-width="100"
        show-tooltip-when-overflow="true"
      />
      <!-- <el-table-column
        label="项目编码"
        align="center"
        prop="projectCode"
        min-width="110"
        show-tooltip-when-overflow="true"
      /> -->
      <!-- <el-table-column
        label="隧道编码"
        align="center"
        prop="tunnelCode"
        width="170"
        show-tooltip-when-overflow="true"
      /> -->
      <el-table-column
        label="隧道名称"
        align="center"
        prop="tunnelName"
        width="170"
        show-tooltip-when-overflow="true"
      />
      <el-table-column
        label="状态"
        align="center"
        prop="status"
        :formatter="formatStatus"
        width="170"
        show-tooltip-when-overflow="true"
      />
      <!-- <el-table-column label="报告生成失败原因" align="center" prop="reportErrorMsg" width="170" show-tooltip-when-overflow="true"/> -->
      <!--      <el-table-column label="报告下载url" align="center" prop="reportDownloadUrl" />-->
      <!--      <el-table-column label="创建人编码" align="center" prop="creatorCode" />-->
      <!--      <el-table-column label="创建人名称" align="center" prop="creatorName" />-->
      <!--      <el-table-column label="创建时间" align="center" prop="creatorTime" width="180">-->
      <!--      </el-table-column>-->
      <!--      <el-table-column label="修改人编码" align="center" prop="modifierCode" />-->
      <el-table-column
        label="操作人"
        align="center"
        prop="modifierName"
        show-tooltip-when-overflow="true"
      />
      <el-table-column
        label="操作时间"
        align="center"
        prop="modifierTime"
        width="200"
        show-tooltip-when-overflow="true"
      >
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        :resizable="false"
        align="center"
        width="200"
        label="操作"
      >
        <template #default="scope">
          <div class="operate-section">
            <el-button
              @click="handleCreate(scope.row)"
              type="primary"
              v-loading.fullscreen.lock="fullscreenLoading"
              v-hasPermi="['project:report:generater']"
              v-if="scope.row.status == 0"
              >生成报告</el-button
            >
            <a
              v-if="
                scope.row.status == 2 &&
                scope.row.reportDownloadUrl != null &&
                scope.row.reportDownloadUrl != ''
              "
              style="color: blue"
              type="warning"
              :href="scope.row.reportDownloadUrl"
            >
              <el-button
                type="warning"
                v-hasPermi="['project:report:exportSingle']"
                v-if="
                  scope.row.status == 2 &&
                  scope.row.reportDownloadUrl != null &&
                  scope.row.reportDownloadUrl != ''
                "
                >导出</el-button
              >
            </a>
            <el-button
              type="danger"
              plain
              size="mini"
              @click="handleDelete(scope.row)"
              v-hasPermi="['project:report:remove']"
              >删除</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="queryParams.pageSize"
      :total="total"
      layout="->,total, sizes, prev, pager, next, jumper"
      style="margin-top: 10px; text-align: center"
    >
    </el-pagination>

    <!-- 添加或修改项目隧道检测报告管理对话框 -->
    <el-dialog :title="title" v-model="open" width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="项目名称" prop="projectCode">
          <el-select
            v-model="form.projectCode"
            @change="handelProjectChange"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="item in projects"
              :key="item.projectCode"
              :label="item.projectName"
              :value="item.projectCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="隧道名称" prop="totalTunnelCode">
          <el-select v-model="form.totalTunnelCode" placeholder="请选择" clearable>
            <el-option
              v-for="item in tunnels"
              :key="item.totalTunnelCode"
              :label="item.tunnelName"
              :value="item.totalTunnelCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-model="pictureOpen"
      :title="pictureTitle"
      width="75%"
      v-if="pictureOpen"
    >
      <reportDetail :projectObj="currentRow"></reportDetail>
    </el-dialog>
  </div>
</template>

<script>
import {
  listReport,
  getReport,
  delReport,
  addReport,
  updateReport,
  create,
  exportData,
  getProjects,
  searchTunnelListByProjectIdForReport,
} from "@/api/system/report";
import reportDetail from "@/views/detection/technology/data/reportDetail.vue";
import axios from "axios";
import { getToken } from "@/utils/auth";
import request from "@/utils/request";
export default {
  name: "Report",
  components: { reportDetail },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      currentRow: {},
      pictureTitle: "",
      pictureOpen: false,
      isExport: true,
      isExportIng: false,
      fullscreenLoading: false,
      // 项目隧道检测报告管理表格数据
      reportList: [
        // {
        //   "id":1,
        //   "projectCode":"项目编码111",
        //   "projectName":"项目一",
        //   "tunnelCode":"隧道编码111",
        //   "tunnelName":"隧道一",
        //   "status":"已评定",
        //   "reportErrorMsg":null,
        //   "reportDownloadUrl":null,
        //   "creatorCode":"zs",
        //   "creatorName":"张三",
        //   "creatorTime":"2023-08-05 00:01:00",
        //   "modifierCode":"ls",
        //   "modifierName":"李四",
        //   "modifierTime":"2023-08-05 00:01:05"
        // },{
        //   "id":2,
        //   "projectCode":"项目编码222",
        //   "projectName":"项目二",
        //   "tunnelCode":"隧道编码222",
        //   "tunnelName":"隧道二",
        //   "status":"报告生成中",
        //   "reportErrorMsg":null,
        //   "reportDownloadUrl":null,
        //   "creatorCode":"ww",
        //   "creatorName":"王五",
        //   "creatorTime":"2023-08-05 00:03:00",
        //   "modifierCode":"zl",
        //   "modifierName":"赵六",
        //   "modifierTime":"2023-08-05 00:08:05"
        // },{
        //   "id":2,
        //   "projectCode":"项目编码333",
        //   "projectName":"项目三",
        //   "tunnelCode":"隧道编码333",
        //   "tunnelName":"隧道三",
        //   "status":"报告已生成",
        //   "reportErrorMsg":null,
        //   "reportDownloadUrl":"https://img1.baidu.com/it/u=3129770633,3933811923&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500",
        //   "creatorCode":"ww",
        //   "creatorName":"王五",
        //   "creatorTime":"2023-08-05 00:03:00",
        //   "modifierCode":"zl",
        //   "modifierName":"赵六",
        //   "modifierTime":"2023-08-05 00:08:05"
        // },{
        //   "id":2,
        //   "projectCode":"项目编码333",
        //   "projectName":"项目三",
        //   "tunnelCode":"隧道编码333",
        //   "tunnelName":"隧道三",
        //   "status":"生成失败",
        //   "reportErrorMsg":"数据缺失",
        //   "reportDownloadUrl":null,
        //   "creatorCode":"ww",
        //   "creatorName":"王五",
        //   "creatorTime":"2023-08-05 00:03:00",
        //   "modifierCode":"zl",
        //   "modifierName":"赵六",
        //   "modifierTime":"2023-08-05 00:08:05"
        // }
      ],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectTunnelInspectionId: null,
        projectName: null,
        projectCode: null,
        tunnelName: null,
        tunnelCode: null,
        status: null,
        reportErrorMsg: null,
        reportDownloadUrl: null,
        creatorCode: null,
        creatorName: null,
        creatorTime: null,
        createBeginTime: null,
        createEndTime: null,
        modifierCode: null,
        modifierName: null,
        modifierTime: null,
        isAvaliable: null,
        isDeleted: null,
      },
      // 表单参数
      form: {
        date1: [],
      },
      projects: [],
      tunnels: [],
      // 表单校验
      rules: {
        totalTunnelCode: [
          { required: true, message: "隧道名称不能为空", trigger: "blur" },
        ],
        projectCode: [
          { required: true, message: "项目名称不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.getProject();
    // this.getTunnel();
    this.token = getToken();
  },
  methods: {
    /** 查询项目隧道检测报告管理列表 */
    getList() {
      this.loading = true;
      if (
        this.queryParams.onDate != null &&
        this.queryParams.onDate.length != 0
      ) {
        this.queryParams.createBeginTime = this.queryParams.onDate[0];
        this.queryParams.createEndTime = this.queryParams.onDate[1];
      }
      listReport(this.queryParams).then((response) => {
        this.reportList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    sizeChangeHandle(val) {
      this.queryParams.pageSize = val;
      this.queryParams.pageNum = 1;
      this.getList();
    },
    currentChangeHandle: function currentChangeHandle(val) {
      this.queryParams.pageNum = val;
      this.getList();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectTunnelInspectionId: null,
        tunnelName: null,
        tunnelCode: null,
        status: "0",
        reportErrorMsg: null,
        reportDownloadUrl: null,
        creatorCode: null,
        creatorName: null,
        creatorTime: null,
        modifierCode: null,
        modifierName: null,
        modifierTime: null,
        isAvaliable: null,
        isDeleted: null,
      };
      this.resetForm("form");
    },
    formatStatus(row, column, cellValue) {
      if (cellValue == 0) {
        return "未生成";
      } else if (cellValue == 1) {
        return "报告生成中";
      } else if (cellValue == 2) {
        return "报告已生成";
      } else if (cellValue == 3) {
        return "报告生成失败";
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.createEndTime = null;
      this.queryParams.createBeginTime = null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加项目隧道检测报告管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getReport(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改项目隧道检测报告管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.projects.forEach((item, index, arr) => {
            if (item.projectCode == this.form.projectCode) {
              this.form.projectName = item.projectName;
            }
          });
          this.tunnels.forEach((item, index, arr) => {
            if (item.totalTunnelCode == this.form.totalTunnelCode) {
              this.form.tunnelName = item.tunnelName;
            }
          });
          if (this.form.id != null) {
            updateReport(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addReport(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm(
          '是否确认删除项目隧道检测报告管理编号为"' + ids + '"的数据项？'
        )
        .then(function () {
          return delReport(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // this.download('project/report/export', {
      //   ...this.queryParams
      // }, `report_${new Date().getTime()}.xlsx`)
      if (this.reportList.length == 0) {
        this.$alert("无导出数据", "", {
          confirmButtonText: "确定",
        });
      } else {
        //开启loading
        this.isExport = false;
        this.isExportIng = true;
        if (
          this.queryParams.onDate != null &&
          this.queryParams.onDate.length != 0
        ) {
          this.queryParams.createBeginTime = this.queryParams.onDate[0];
          this.queryParams.createEndTime = this.queryParams.onDate[1];
        }
        exportData(this.queryParams)
          .then((res) => {
            if (res.code == "500") {
              this.$modal.msgSuccess("导出失败，请联系开发人员");
              return;
            } else {
              this.$modal.msgSuccess("导出成功");
            }
            let blob = new Blob([res], { type: "application/force-download" });
            console.log(blob);
            let fileReader = new FileReader();
            fileReader.readAsDataURL(blob);
            fileReader.onload = (e) => {
              let a = document.createElement("a");
              let date = new Date();
              let year = date.getFullYear();
              let month = date.getMonth() + 1;
              let day = date.getDate();
              a.download = `导出数据${year + "_" + month + "_" + day}.xlsx`;
              a.href = e.target.result;
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
            };
            this.isExport = true;
            this.isExportIng = false;
          })
          .catch(() => {
            this.isExport = true;
            this.isExportIng = false;
          });
      }
    },
    /** 导出按钮操作 */
    handleCreate(row) {
      this.fullscreenLoading = true;
      create(row.id).then((response) => {
        this.$modal.msgSuccess("报告生成成功！");
        this.fullscreenLoading = false;
        this.getList();
      });
    },
    /** 导出按钮操作 */
    // handleSingleExport(row) {
    //   let date = new Date();
    //   let year = date.getFullYear();
    //   let month = date.getMonth()+1;
    //   let day = date.getDate();
    //   // this.download(row.reportDownloadUrl, {
    //   //
    //   // }, `report_${year+"_"+month+"_"+day}.xlsx`)
    //
    //   var x = new XMLHttpRequest();
    //   x.open("POST", row.reportDownloadUrl, true);
    //   x.responseType = 'blob';
    //   x.onload=function(e) {
    //     //会创建一个 DOMString，其中包含一个表示参数中给出的对象的URL。这个 URL 的生命周期和创建它的窗口中的 document 绑定。这个新的URL 对象表示指定的 File 对象或 Blob 对象。
    //     var url = window.URL.createObjectURL(x.response)
    //     var a = document.createElement('a');
    //     a.href = url
    //     a.download = `report_${year+"_"+month+"_"+day}.doc`;
    //     a.click()
    //   }
    //   x.send();
    // },
    /**
     * 导出excel
     * @param fileName
     * @returns {AxiosPromise}
     */
    // handleSingleExport(row){
    //   let date = new Date();
    //   let year = date.getFullYear();
    //   let month = date.getMonth()+1;
    //   let day = date.getDate();
    //   request({
    //     url: row.reportDownloadUrl,
    //     method: 'get',
    //     responseType:'blob'
    //   }).then(res=>{
    //     // const date = new Date(+new Date() + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '').replace(/\-/g, '').replace(/\:/g, '').replace(/\s*/g, '')
    //     // const downloadName = fileName +'-'+ date + '.xlsx'
    //     this.downloadFunc(`report_${year+"_"+month+"_"+day}.doc`,res)
    //   })
    // },

    downloadFunc(fileName, data) {
      const blob = new Blob([data], {
        type: `'application/vnd.ms-excel';charset=utf-8`,
      });
      const downloadElement = document.createElement("a");
      const href = window.URL.createObjectURL(blob);
      downloadElement.href = href;
      downloadElement.download = fileName;
      document.body.appendChild(downloadElement);
      downloadElement.click();
      document.body.removeChild(downloadElement);
      window.URL.revokeObjectURL(href);
    },

    // 下载按钮点击事件
    handleSingleExport(row) {
      // @click="handleSingleExport(scope.row)"
      let url = row.reportDownloadUrl; // data:项目中获取的数据，包含文件url以及文件名等相关参数
      let fileName = "654665.doc";
      let xhr = new XMLHttpRequest();
      xhr.open("GET", url, true);
      xhr.responseType = "blob";
      xhr.onload = (e) => {
        const res = e.target.response;
        this.saveAs(res, fileName);
      };
      xhr.send();
    },

    // 导出文件函数
    saveAs(obj, fileName) {
      let ele = document.createElement("a");
      ele.download = fileName || "下载";
      ele.href = URL.createObjectURL(obj); // 绑定a标签
      ele.style.display = "none";
      document.body.appendChild(ele); // 兼容火狐浏览器
      ele.click();
      setTimeout(function () {
        // 延时释放
        URL.revokeObjectURL(obj); // 用URL.revokeObjectURL()来释放这个object URL
        document.body.removeChild(ele); // 兼容火狐浏览器
      }, 100);
    },

    /** 详情 */
    handleDetail(row) {
      console.log(row);
      this.currentRow = row;
      this.pictureOpen = true;
      this.pictureTitle = "报告";
      console.log("111111");
    },
    getProject() {
      getProjects().then((response) => {
        this.projects = response.data;
      });
    },
    handelProjectChange(value) {
      // console.log(value);
      let obsj = this.projects.find((item) => item.projectCode === value);
      // console.log(obsj);
      this.getTunnel(obsj.id)
    },
    getTunnel(id) {
      searchTunnelListByProjectIdForReport({
        projectId: id,
      }).then((response) => {
        this.tunnels = response.data;
        // this.tunnels.forEach(function (item, index, array) {
        //   item.totalTunnelCode += item?.totalTunnelCode;
        // });
      });
    },
  },
};
</script>
