<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="auto">
      <el-form-item label="隶属单位" prop="affiliatedUnit">
        <el-input
            v-model="queryParams.affiliatedUnit"
            placeholder="请输入隶属单位"
            clearable
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="检测状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择检测状态" clearable>
          <el-option v-for="item in statusList" :key="item.value" :label="item.label"
                     :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="检测时间" prop="onDate"  >
        <el-date-picker
            v-model="queryParams.onDate"
            type="daterange"
            range-separator="~"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            start-placeholder="开始日期"
            end-placeholder="结束日期" style="width: 270px">
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary"  size="mini" @click="handleQuery">搜索</el-button>
        <el-button  size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          @click="handleAdd"
          v-hasPermi="['project:project:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['project:project:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:project:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            size="mini"
            @click="handleBatchAdd"
            v-hasPermi="['project:project:batchAdd']"
        >批量新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          size="mini"
          @click="handleExport"
          v-hasPermi="['project:project:export']"
          v-if="isExport"
        >导出</el-button>
        <el-button type="primary" v-if="isExportIng" :loading="true">导出中</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="projectList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
       <el-table-column type="index" fixed="left" label="序号" width="70" />
       <el-table-column label="隶属单位" align="center" prop="affiliatedUnit" min-width="120" show-tooltip-when-overflow="true"/>
      <el-table-column label="项目编码" align="center" prop="projectCode" width="120" show-tooltip-when-overflow="true"/>
      <el-table-column label="项目名称" align="center" prop="projectName" min-width="150" show-tooltip-when-overflow="true"/>
      <!-- <el-table-column label="总里程（km）" align="center" prop="totalMileage" width="150" show-tooltip-when-overflow="true"/> -->
      <el-table-column label="检测时间" align="center" prop="beginTime" min-width="150" show-tooltip-when-overflow="true">
        <template #default="{ row }">
          <span>{{ `${row.beginTime} - ${row.endTime}` }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="截至时间" align="center" prop="endTime" width="150" show-tooltip-when-overflow="true">
      </el-table-column> -->
      <el-table-column
          label="检测状态"
          align="center"
          prop="status"
          :formatter="formatStatus"
          min-width="120" show-tooltip-when-overflow="true"
      />
      <!-- <el-table-column label="甲方项目负责人" align="center" prop="firstPartyName" width="150" show-tooltip-when-overflow="true"/> -->
       <!-- <el-table-column label="项目负责人" align="center" prop="firstPartyName" width="150" show-tooltip-when-overflow="true"/> -->
      <!-- <el-table-column label="乙方负责人" align="center" prop="secondPartyName" width="150" show-tooltip-when-overflow="true"/> -->
     
      <el-table-column label="隧道数量" align="center" prop="tunnelInspectionNum" width="150" show-tooltip-when-overflow="true"/>
      <!-- <el-table-column label="管养单位" align="center" prop="careUnit" width="150" show-tooltip-when-overflow="true"/> -->
      <!-- <el-table-column label="备注" align="center" prop="remark" min-width="150" show-tooltip-when-overflow="true"/> -->
      <!-- <el-table-column label="创建人编码" align="center" prop="creatorCode" width="150" show-tooltip-when-overflow="true"/> -->
      <!-- <el-table-column label="创建人名称" align="center" prop="creatorName" width="150" show-tooltip-when-overflow="true"/> -->
      <!-- <el-table-column label="创建时间" align="center" prop="creatorTime" width="180" show-tooltip-when-overflow="true"> -->
      <!-- </el-table-column> -->
      <!-- <el-table-column label="修改人编码" align="center" prop="modifierCode" width="150" show-tooltip-when-overflow="true"/> -->
      <!-- <el-table-column label="修改人名称" align="center" prop="modifierName" width="150" show-tooltip-when-overflow="true"/> -->
      <!-- <el-table-column label="修改时间" align="center" prop="modifierTime" width="180" show-tooltip-when-overflow="true"> -->
      <!-- </el-table-column> -->
      <!-- <el-table-column label="创建人部门编码" align="center" prop="creatorDeptCode" width="150" show-tooltip-when-overflow="true"/> -->
      <!-- <el-table-column label="创建人部门名称" align="center" prop="creatorDeptName" width="150" show-tooltip-when-overflow="true" /> -->
      <el-table-column fixed="right" label="操作" align="center" class-name="small-padding fixed-width" width="300">
        <template v-slot:default="scope">
          <el-button
            size="mini"
            type="success"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['project:project:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="primary"
            @click="handleRelate(scope.row)"
            v-hasPermi="['project:project:relate']"
          >关联</el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(scope.row)"
            v-hasPermi="['project:project:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="queryParams.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="queryParams.pageSize"
        :total="total"
        layout="->,total, sizes, prev, pager, next, jumper"
        style="margin-top:10px;text-align: center"
        >
    </el-pagination>

    <!-- 添加或修改项目管理对话框 -->
    <el-dialog :title="title"
               v-model="open"
               width="500px"
               >
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="项目编码" prop="projectCode">
          <el-input v-model="form.projectCode" placeholder="请输入项目编码" class="myInput"/>
        </el-form-item>
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入项目名称" class="myInput"/>
        </el-form-item>
        <el-form-item label="总里程（km）" prop="totalMileage">
          <el-input v-model="form.totalMileage" placeholder="请输入总里程（km）" class="myInput"/>
        </el-form-item>
        <el-form-item label="开始~截至时间:" prop="date1">
          <el-date-picker
              v-model="form.date1"
              type="daterange"
              range-separator="~"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              start-placeholder="开始时间"
              end-placeholder="截至时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="状态" prop="status" >
          <el-select v-model="form.status"  placeholder="请选择状态">
            <el-option v-for="item in statusList" :key="item.value" :label="item.label"
                       :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="甲方项目负责人" prop="firstPartyName">
          <el-input v-model="form.firstPartyName" placeholder="请输入甲方项目负责人" class="myInput"/>
        </el-form-item>
        <el-form-item label="乙方负责人" prop="secondPartyName">
          <el-input v-model="form.secondPartyName" placeholder="请输入乙方负责人" class="myInput"/>
        </el-form-item>
        <el-form-item label="隶属单位" prop="affiliatedUnit" >
<!--          <el-select v-model="form.affiliatedUnit"  placeholder="请选择隶属单位">-->
<!--            <el-option v-for="item in companyList" :key="item.value" :label="item.label"-->
<!--                       :value="item.value" />-->
<!--          </el-select>-->
          <el-input v-model="form.affiliatedUnit" placeholder="请输入隶属单位" class="myInput"/>
        </el-form-item>
        <el-form-item label="检测隧道数量" prop="tunnelInspectionNum">
          <el-input v-model="form.tunnelInspectionNum" placeholder="请输入检测隧道数量" class="myInput"/>
        </el-form-item>
        <el-form-item label="管养单位" prop="careUnit">
          <el-input v-model="form.careUnit" placeholder="请输入管养单位" class="myInput"/>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" type="textarea" class="myInput"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog v-model="batchAddOpen" :title="batchAddTitle" width="40%" destroy-on-close="true" @closed="closeFileUpload()">
      <projectFileUpload></projectFileUpload>
    </el-dialog>


    <el-dialog title="修改" v-model="editOpen" width="500px" >
      <el-form ref="editForm" :model="editForm" :rules="editRules" label-width="auto">
        <el-form-item label="项目名称" prop="projectId">
          <el-select v-model="editForm.projectId" placeholder="请选择" clearable>
            <el-option
                v-for="item in projectList"
                :key="item.id"
                :label="item.projectName"
                :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="开始~截至时间:" prop="date1">
          <el-date-picker
              v-model="editForm.date1"
              type="daterange"
              range-separator="~"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              start-placeholder="开始时间"
              end-placeholder="截至时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="检测里程" prop="detectionMileage">
          <el-input v-model="editForm.detectionMileage" placeholder="请输入检测里程" />
        </el-form-item>
        <el-form-item label="甲方项目负责人" prop="firstPartyName">
          <el-input v-model="editForm.firstPartyName" placeholder="请输入甲方项目负责人" />
        </el-form-item>
        <el-form-item label="乙方负责人" prop="secondPartyName">
          <el-input v-model="editForm.secondPartyName" placeholder="请输入乙方负责人" />
        </el-form-item>
        <el-form-item label="隶属单位" prop="affiliatedUnit">
          <el-input v-model="editForm.affiliatedUnit" placeholder="请输入隶属单位" />
        </el-form-item>
        <el-form-item label="管养单位" prop="careUnit">
          <el-input v-model="editForm.careUnit" placeholder="请输入管养单位"/>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="editForm.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEditForm">确 定</el-button>
        <el-button @click="editCancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog v-model="relateOpen" title="项目关联" width="95%">
      <div>
        <el-form  :model="relateSearchParams" ref="queryRef" :inline="true">
          <el-form-item label="隧道编码" prop="tunnelCode">
            <el-input v-model="relateSearchParams.tunnelCode" placeholder="请输入隧道编码" clearable />
          </el-form-item>
          <el-form-item label="隧道名称" prop="tunnelName">
            <el-input v-model="relateSearchParams.tunnelName" placeholder="请输入隧道名称" clearable />
          </el-form-item>
          <el-form-item label="隶属单位" prop="affiliatedUnit">
<!--            <el-select v-model="relateSearchParams.affiliatedUnit" placeholder="请选择隶属单位" clearable>-->
<!--              <el-option v-for="item in companyList" :key="item.value" :label="item.label"-->
<!--                         :value="item.value" />-->
<!--            </el-select>-->
            <el-input v-model="relateSearchParams.affiliatedUnit" placeholder="请输入隶属单位" clearable />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="relateSearchParams.status" placeholder="请选择状态" clearable>
              <el-option v-for="item in statusList" :key="item.value" :label="item.label"
                         :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary"  @click="searchRelate">搜索</el-button>
            <el-button  @click="resetRelateAction">重置</el-button>
            <el-button type="primary" @click="relateTunnelAction" class="btn">关联隧道</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div>
        <el-table v-loading="relateLoading" :data="relateTunnelList" style="width: 100%" @selection-change="handleRelateSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column prop="id" label="项目隧道关联ID" width="150" show-tooltip-when-overflow="true" align="center"/>
          <el-table-column prop="tunnelCode" align="center" label="隧道编码" width="150" show-tooltip-when-overflow="true"/>
          <el-table-column prop="tunnelName" align="center"  label="隧道名称" width="150" show-tooltip-when-overflow="true" />
          <el-table-column prop="projectName" align="center" label="所属项目" width="150" show-tooltip-when-overflow="true" />
          <el-table-column prop="detectionMileage" align="center" label="检测里程" width="150" show-tooltip-when-overflow="true" />
          <el-table-column prop="beginTime" align="center" label="开始时间" width="150" show-tooltip-when-overflow="true" />
          <el-table-column prop="endTime" align="center" label="截止时间" width="150" show-tooltip-when-overflow="true" />
          <el-table-column
              label="状态"
              align="center"
              prop="status"
              :formatter="formatStatus"
              width="150" show-tooltip-when-overflow="true"
          />
          <el-table-column prop="firstPartyName" align="center" label="甲方项目负责人" width="150" show-tooltip-when-overflow="true" />
          <el-table-column prop="secondPartyName" align="center" label="乙方项目负责人" width="150" show-tooltip-when-overflow="true" />
          <el-table-column prop="affiliatedUnit" align="center" label="隶属单位" width="150" show-tooltip-when-overflow="true" />
          <el-table-column prop="remark" align="center" label="备注" width="150" show-tooltip-when-overflow="true" />
          <el-table-column fixed="right" prop="operation" label="操作" align="center" width="200">
            <template v-slot:default="scope">
              <div class="operate-section">
                <el-button @click="handleEditUpdate(scope.row)" type="success" class="btn">编辑</el-button>
                <el-button @click="relateDelete(scope.row)" type="danger" class="btn">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
            @size-change="relateSizeChangeHandle"
            @current-change="relateCurrentChangeHandle"
            :current-page="relateSearchParams.pageNum"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="relateSearchParams.pageSize"
            :total="relateTotal"
            layout="->,total, sizes, prev, pager, next, jumper"
            style="margin-top:10px;text-align: center"
        >
        </el-pagination>
      </div>
    </el-dialog>


    <el-dialog v-model="tunnelOpen" title="关联隧道" width="95%">
      <div>
        <el-form :model="tunnelSearchParams" ref="tunnelQueryRef" :inline="true">
          <el-form-item label="隧道编码" prop="tunnelCode">
            <el-input v-model="tunnelSearchParams.tunnelCode" placeholder="请输入隧道编码" clearable />
          </el-form-item>
          <el-form-item label="隧道名称" prop="tunnelName">
            <el-input v-model="tunnelSearchParams.tunnelName" placeholder="请输入隧道名称" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary"  @click="searchTunnel">搜索</el-button>
            <el-button  @click="resetTunnelAction">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div>
        <el-table v-loading="tunnelLoading" :data="manageList" @selection-change="handleManageSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="隧道编码" align="center" prop="tunnelCode" width="150" show-tooltip-when-overflow="true"/>
          <el-table-column label="隧道名称" align="center" prop="tunnelName" width="150" show-tooltip-when-overflow="true"/>
          <el-table-column label="隶属单位" align="center" prop="affiliatedUnit" width="150" show-tooltip-when-overflow="true"/>
          <el-table-column label="所属路段" align="center" prop="affiliatedRoadSection" width="150" show-tooltip-when-overflow="true"/>
          <el-table-column label="隧道洞别" align="center" prop="hole" width="150" show-tooltip-when-overflow="true"/>
          <el-table-column label="结构形式" align="center" prop="structuralStyle" width="150" show-tooltip-when-overflow="true"/>
          <el-table-column label="隧道交工时间" align="center" prop="tunnelHandoverTime" width="150" show-tooltip-when-overflow="true">
          </el-table-column>
          <el-table-column label="隧道竣工时间" align="center" prop="tunnelCompletionTime" width="150" show-tooltip-when-overflow="true">
          </el-table-column>
          <el-table-column label="经度" align="center" prop="longitude" width="150" show-tooltip-when-overflow="true"/>
          <el-table-column label="纬度" align="center" prop="latitude" width="150" show-tooltip-when-overflow="true"/>
          <el-table-column label="起始桩号" align="center" prop="startingStation" width="150" show-tooltip-when-overflow="true"/>
          <el-table-column label="截至桩号" align="center" prop="endingStation" width="150" show-tooltip-when-overflow="true"/>
          <el-table-column label="隧道长度" align="center" prop="tunnelLength" width="150" show-tooltip-when-overflow="true"/>
          <el-table-column label="埋深起" align="center" prop="burialDepthBegin" width="150" show-tooltip-when-overflow="true"/>
          <el-table-column label="埋深止" align="center" prop="buriedDepthStop" width="150" show-tooltip-when-overflow="true"/>
          <el-table-column label="施工期中心桩号" align="center" prop="constructionCentralNumber" width="150" show-tooltip-when-overflow="true"/>
          <el-table-column label="运营期中心桩号" align="center" prop="operateCentralNumber" width="150" show-tooltip-when-overflow="true"/>
          <el-table-column label="图片" align="center" prop="pictureUrl"  >
            <template v-slot:default="scope">
              <el-image :src="scope.row.pictureUrl" preview-teleported :preview-src-list="scope.row.prePictureList" fit="cover"/>
            </template>
          </el-table-column>
          <!--        <el-table-column label="创建人编码" align="center" prop="creatorCode" />-->
          <!--        <el-table-column label="创建人名称" align="center" prop="creatorName" />-->
          <!--        <el-table-column label="创建时间" align="center" prop="creatorTime" width="180">-->
          <!--        </el-table-column>-->
          <!--        <el-table-column label="修改人编码" align="center" prop="modifierCode" />-->
          <!--        <el-table-column label="修改人名称" align="center" prop="modifierName" />-->
          <!--        <el-table-column label="修改时间" align="center" prop="modifierTime" width="180">-->
          <!--        </el-table-column>-->
        </el-table>
        <div>
          <el-pagination
              @size-change="tunnelSizeChangeHandle"
              @current-change="tunnelCurrentChangeHandle"
              :current-page="tunnelSearchParams.pageNum"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="tunnelSearchParams.pageSize"
              :total="manageTotal"
              layout="->,total, sizes, prev, pager, next, jumper->"
              style="margin-top:10px;text-align: center"
          >
          </el-pagination>
        </div>

      </div>

      <div slot="footer" class="dialog-footer" align="center">
        <el-button type="primary" @click="tunnelRelate">确 定</el-button>
        <el-button @click="tunnelCancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listProject, getProject, delProject, addProject, updateProject,exportData,listRelateTunnelList,delRelate,getInspection,updateInspection,listTunnelList,tunnelRelates } from "@/api/system/project";
import projectFileUpload from "./fileUpload/projectFileUpload.vue";
import axios from 'axios'
import { getToken } from "@/utils/auth";
import ProjectRelate from "../../../basic/projectManage/components/projectRelate.vue";
export default {
  name: "Project",
  components:{
    ProjectRelate,
    projectFileUpload
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      relateLoading: true,
      tunnelLoading: true,
      // 选中数组
      ids: [],
      tunnelIds: [],
      relateIds: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      relateTotal:0,
      manageTotal:0,
      // 项目管理表格数据
      projectList: [],
      manageList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      editOpen: false,
      tunnelOpen: false,
      batchAddTitle: "",
      batchAddOpen: false,
      token:null,
      isExport:true,
      isExportIng:false,
      relateOpen:false,
      projectId:null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectCode: null,
        projectName: null,
        totalMileage: null,
        beginTime: null,
        endTime: null,
        status: null,
        firstPartyName: null,
        secondPartyName: null,
        affiliatedUnit: null,
        tunnelInspectionNum: null,
        creatorCode: null,
        creatorName: null,
        creatorTime: null,
        modifierCode: null,
        modifierName: null,
        modifierTime: null,
        creatorDeptCode: null,
        creatorDeptName: null,
        isAvailable: null,
        isDeleted: null,
        onDate:[],
        offDate:[],
        careUnit:null,
      },
      relateSearchParams:{
        tunnelName: null,
        tunnelCode: null,
        affiliatedUnit: null,
        status: null,
        projectId:null,
        pageNum: 1,
        pageSize: 10,
      },
      tunnelSearchParams:{
        tunnelName: null,
        tunnelCode: null,
        projectId:null,
        pageNum: 1,
        pageSize: 10,
      },
      // 表单参数
      form: {
        date1:[]
      },
      editForm: {
        date1:[]
      },
      relateForm: {
        projectId:null,
        tunnelIds:[],
      },
      statusList: [
        {
          label: '检测中',
          value: 1
        }, {
          label: '未检测',
          value: 0
        }, {
          label: '检测完成',
          value: 2
        }
      ],
      relateTunnelList: [],
      // companyList: [
      //   {
      //     label: '中国交建',
      //     value: '中国交建'
      //   }, {
      //     label: '中交三局',
      //     value: '中交三局'
      //   }, {
      //     label: '中铁三局',
      //     value: '中铁三局'
      //   }
      // ],
      // 表单校验
      rules: {
        // remainShelfLife :[{ pattern: /^[0-9]*[1-9][0-9]*$/, message: '只能输入正整数', trigger: 'blur' }],
        // totalShelfLife :[{required: true, message: '总货架寿命不能为空!', trigger: 'blur'},{ pattern: /^[0-9]*[1-9][0-9]*$/, message: '只能输入正整数', trigger: 'blur' }],
        // warehouseLatestDeliveryDay :[{ pattern: /^[0-9]*[1-9][0-9]*$/, message: '只能输入正整数', trigger: 'blur' }],
        // storeBestSaleDay :[{ pattern: /^[0-9]*[1-9][0-9]*$/, message: '只能输入正整数', trigger: 'blur' }],
        // maxSaleDay :[{ pattern: /^[0-9]*[1-9][0-9]*$/, message: '只能输入正整数', trigger: 'blur' }],
        // materialCode :[{required: true, message: '物料编码不能为空!', trigger: 'blur'},{ validator:validatePass },{max:30, message: '物料编码最大为30个字符',trigger:'blur'}],
        /*skuDateList: [{required: true, message: '有效日期不能为空', trigger: 'blur'}],*/
        projectName: [{required: true, message: '项目名称不能为空', trigger: 'blur'}],
        projectCode: [{required: true, message: '项目编码不能为空', trigger: 'blur'}],
        pileNumber: [{required: true, message: '桩号不能为空', trigger: 'blur'}],
        conditionValue:[{ pattern: /^[0-4]?$/, message: '只能输0-4的整数', trigger: 'blur' },{ required: true, message: '状况值不能为空', trigger: 'blur'}],
        // num:[{ pattern: /^\d{1,8}(\.\d{0,3})?$/, message: '只能输数字，最多三位整数，最多3位小数', trigger: 'blur' },{ required: true, message: '数量不能为空', trigger: 'blur'}],
        // combineSkuUnit:[{ required: true, message: '单位不能为空', trigger: 'blur'}],
        // merchantId: [{required: true, message: '商家不能为空', trigger: 'blur'}],
        /*standardPrice: [
            { required: true, message: '标准价不能为空', trigger: 'blur' },
            {
                validator (rule, value, callback) {
                    var pattern = '^\\d{1,8}(\\.\\d{0,2})?$';
                    var reg = new RegExp(pattern, 'g');
                    if (reg.test(value)) {
                        callback()
                    } else {
                        callback(new Error('输入非法'))
                    }
                },
                trigger: 'blur'
            }
        ],*/
        // isDirectSending: [{required: true, message: '是否直送不能为空', trigger: 'blur'}],
        // needRequired: [{required: true, message: '是否质检不能为空', trigger: 'blur'}],
        // abbreviation: [{max:50, message: '简称最大为50个字符',trigger:'blur'}],
        // name: [{required: true, message: 'sku名称不能为空', trigger: 'blur'},{max:50, message: 'sku名称最大为50个字符',trigger:'blur'}]
      },
      // 表单校验
      editRules: {
        projectId: [{required: true, message: '项目名称不能为空', trigger: 'blur'}],
      }
    };
  },
  created() {
    this.getList();
    this.token=getToken();
  },
  methods: {
    /** 查询项目管理列表 */
    getList() {
      this.loading = true;
      if(this.queryParams.onDate!=null&&this.queryParams.onDate.length!=0){
        this.queryParams.createBeginTime=this.queryParams.onDate[0];
        this.queryParams.createEndTime=this.queryParams.onDate[1];
      }
      listProject(this.queryParams).then(response => {
        this.projectList = response.rows;
        this.projectList.forEach(item=>{Object.assign(item,{prePictureList:[item.pictureUrl]})});
        this.total = response.total;
        this.loading = false;
      });
    },
    getRelateList() {
      // this.relateSearchParams.projectId=this.projectId;
      this.relateLoading=true;
      listRelateTunnelList(this.relateSearchParams).then(response => {
        this.relateTunnelList = response.rows;
        this.relateTotal = response.total;
        this.relateLoading=false;
      });
      console.log(this.relateLoading)
    },
    getTunnelList() {
      this.tunnelLoading=true;
      listTunnelList(this.tunnelSearchParams).then(response => {
        this.manageList = response.rows;
        this.manageTotal = response.total;
        this.manageList.forEach(item=>{Object.assign(item,{prePictureList:[item.pictureUrl]})});
        this.tunnelLoading=false;
      });
    },
    sizeChangeHandle(val) {
      this.queryParams.pageSize = val;
      this.queryParams.pageNum = 1;
      this.getList();
    },
    currentChangeHandle: function currentChangeHandle(val) {
      this.queryParams.pageNum = val;
      this.getList();
    },
    relateSizeChangeHandle(val) {
      this.relateSearchParams.pageSize = val;
      this.relateSearchParams.pageNum = 1;
      this.getRelateList();
    },
    relateCurrentChangeHandle: function currentChangeHandle(val) {
      this.relateSearchParams.pageNum = val;
      this.getRelateList();
    },
    tunnelSizeChangeHandle(val) {
      this.tunnelSearchParams.pageSize = val;
      this.tunnelSearchParams.pageNum = 1;
      this.getTunnelList();
    },
    tunnelCurrentChangeHandle: function currentChangeHandle(val) {
      this.tunnelSearchParams.pageNum = val;
      this.getTunnelList();
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    closeFileUpload(){
      this.handleQuery();
    },
    tunnelCancel() {
      this.tunnelOpen = false;
      this.tunnelReset();
    },
    // 取消按钮
    editCancel() {
      this.editOpen = false;
      this.editReset();
    },
    /** 批量新增按钮操作 */
    handleBatchAdd() {
      this.batchAddOpen = true;
      this.batchAddTitle = "批量新增";
    },
    //关联状态
    formatStatus(row, column, cellValue) {
      if (cellValue == 0) {
        return '未检测';
      } else if (cellValue == 1) {
        return '检测中';
      }else if(cellValue == 2){
        return '检测完成';
      }
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectCode: null,
        projectName: null,
        totalMileage: null,
        beginTime: null,
        endTime: null,
        status: null,
        firstPartyName: null,
        secondPartyName: null,
        affiliatedUnit: null,
        tunnelInspectionNum: null,
        remark: null,
        creatorCode: null,
        creatorName: null,
        creatorTime: null,
        modifierCode: null,
        modifierName: null,
        modifierTime: null,
        creatorDeptCode: null,
        creatorDeptName: null,
        isAvailable: null,
        isDeleted: null,
        careUnit:null,
      };
      this.resetForm("form");
    },
    editReset() {
      this.editForm = {
        id: null,
        projectId: null,
        tunnelId: null,
        detectionMileage: null,
        beginTime: null,
        endTime: null,
        status: "0",
        firstPartyName: null,
        secondPartyName: null,
        affiliatedUnit: null,
        remark: null,
        creatorCode: null,
        creatorName: null,
        creatorTime: null,
        modifierCode: null,
        modifierName: null,
        modifierTime: null,
        isAvailable: null,
        isDeleted: null,
        careUnit:null,
      };
      this.resetForm("editForm");
    },
    tunnelReset() {
      this.relateForm= {
        projectId:null,
        tunnelIds:[],
      };
      this.resetForm("relateForm");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    searchRelate(){
      this.relateSearchParams.pageNum = 1;
      this.getRelateList();
    },
    searchTunnel(){
      this.tunnelSearchParams.pageNum = 1;
      this.getTunnelList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.createEndTime=null;
      this.queryParams.createBeginTime=null;
      this.handleQuery();
    },
    resetRelateAction() {
      this.resetForm("queryRef");
      this.relateSearchParams.projectId=this.projectId;
      this.handleQuery();
    },
    resetTunnelAction() {
      this.resetForm("tunnelQueryRef");
      this.tunnelSearchParams.projectId=this.projectId;
      this.searchTunnel();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    handleManageSelectionChange(selection) {
      this.tunnelIds = selection.map(item => item.id)
    },
    // 多选框选中数据
    handleRelateSelectionChange(selection) {
      this.relateIds = selection.map(item => item.id)
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加项目管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getProject(id).then(response => {
        this.form = response.data;
        this.form.date1=[response.data.beginTime,response.data.endTime]
        this.open = true;
        this.title = "修改项目管理";
      });
    },
    handleEditUpdate(row) {
      this.editReset();
      const id = row.id
      getInspection(id).then(response => {
        this.editForm = response.data;
        this.editForm.date1=[response.data.beginTime,response.data.endTime]
        this.editOpen = true;
        this.title = "修改";
      });
    },
    relateTunnelAction() {
      this.relateSearchParams.projectId=this.projectId;
      this.getTunnelList();
      this.tunnelOpen = true;
    },
    handleRelate(row) {
      this.projectId=row.id;
      this.relateSearchParams.projectId=this.projectId;
      this.getRelateList();
      this.relateOpen = true;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.date1!=null&&this.form.date1.length != 0) {
            this.form.beginTime=this.form.date1[0];
            this.form.endTime=this.form.date1[1];
          }
          if (this.form.id != null) {
            updateProject(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addProject(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    tunnelRelate() {
      if(this.tunnelIds.length==0){
        this.$modal.msgWarning("请选择隧道");
        return
      }
      this.relateForm.projectId=this.projectId;
      this.relateForm.tunnelIds=this.tunnelIds;
      tunnelRelates(this.relateForm).then(response => {
        this.$modal.msgSuccess("关联成功");
        this.tunnelOpen = false;
        this.getRelateList();
      });
    },
    submitEditForm() {
      this.$refs["editForm"].validate(valid => {
        if (valid) {
          if (this.editForm.date1!=null&&this.form.date1.length != 0) {
            this.editForm.beginTime=this.editForm.date1[0];
            this.editForm.endTime=this.editForm.date1[1];
          }
          updateInspection(this.editForm).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.editOpen = false;
            this.getRelateList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除项目管理编号为"' + ids + '"的数据项？').then(function() {
        return delProject(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 删除按钮操作 */
    relateDelete(row) {
      const ids = row.id;
      this.$modal.confirm('是否确认删除项目关联编号为"' + ids + '"的数据项？').then(function() {
        return delRelate(ids);
      }).then(() => {
        this.getRelateList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.projectList.length == 0) {
        this.$alert('无导出数据', '', {
          confirmButtonText: '确定'
        })
      } else {
        //开启loading
        this.isExport = false;
        this.isExportIng = true;
        if (this.queryParams.onDate!=null&&this.queryParams.onDate.length != 0) {
          this.queryParams.createBeginTime = this.queryParams.onDate[0];
          this.queryParams.createEndTime = this.queryParams.onDate[1];
        }
        exportData(this.queryParams).then(res => {
          if (res.code == '500') {
            this.$modal.msgSuccess("导出失败，请联系开发人员");
            return
          } else {
            this.$modal.msgSuccess("导出成功");
          }
          let blob = new Blob([res], {type: "application/force-download"})
          console.log(blob);
          let fileReader = new FileReader()
          fileReader.readAsDataURL(blob)
          fileReader.onload = (e) => {
            let a = document.createElement('a')
            let date = new Date();
            let year = date.getFullYear();
            let month = date.getMonth()+1;
            let day = date.getDate();
            a.download = `导出数据${year+"_"+month+"_"+day}.xlsx`
            a.href = e.target.result
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
          }
          this.isExport = true;
          this.isExportIng = false;
        }).catch(() => {
          this.isExport = true;
          this.isExportIng = false;
        });

      }
    }
  }
};
</script>

<style lang="scss" scoped>

.myInput {width: 215px}
.pageCenter {
  width:40px;
  margin-left: auto;
  margin-right: auto;
}

</style>
