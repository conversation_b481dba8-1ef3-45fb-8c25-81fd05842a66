<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="auto"
    >
      <el-form-item label="隶属单位" prop="projectCode">
        <el-input
          v-model="queryParams.projectCode"
          placeholder="请输入隶属单位"
          clearable
        />
      </el-form-item>
      <el-form-item label="所属路段" prop="affiliatedRoadSection">
        <el-input
          v-model="queryParams.affiliatedRoadSection"
          placeholder="请输入所属路段"
          clearable
        />
      </el-form-item>
      <!-- <el-form-item label="项目编码" prop="projectCode">
        <el-input
          v-model="queryParams.projectCode"
          placeholder="请输入项目编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="项目名称" prop="projectName" label-width="auto">
        <el-select
          v-model="queryParams.projectName"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="item in projects"
            :key="item.projectCode"
            :label="item.projectName"
            :value="item.projectName"
          >
          </el-option>
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item label="隧道编码" prop="tunnelCode">
        <el-input
          v-model="queryParams.tunnelCode"
          placeholder="请输入隧道编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="隧道名称" prop="tunnelName">
        <el-select
          v-model="queryParams.tunnelName"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="item in tunnels"
            :key="item.tunnelCode"
            :label="item.tunnelName"
            :value="item.tunnelName"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="检测完成时间" prop="onDate">
        <el-date-picker
          v-model="queryParams.onDate"
          type="daterange"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          range-separator="~"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 300px"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="mini" @click="handleQuery"
          >搜索</el-button
        >
        <el-button size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          @click="handleAdd"
          v-hasPermi="['project:main:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['project:main:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          size="mini"
          @click="handleExport"
          v-hasPermi="['project:main:export']"
          v-if="isExport"
          >导出</el-button
        >
        <el-button type="primary" v-if="isExportIng" :loading="true"
          >导出中</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="mainList"
      @selection-change="handleSelectionChange"
      width="100%"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号" align="center" width="50" />
      <el-table-column
        label="隶属单位"
        align="center"
        prop="affiliatedUnit"
        width="120"
        show-tooltip-when-overflow="true"
      />

      <el-table-column
        label="所属路段"
        align="center"
        prop="affiliatedRoadSection"
        min-width="120"
        show-tooltip-when-overflow="true"
      />
      <!-- <el-table-column
        label="项目编码"
        align="center"
        prop="projectCode"
        width="150"
        show-tooltip-when-overflow="true"
      /> -->
      <!-- <el-table-column
        label="项目名称"
        align="center"
        prop="projectName"
        width="150"
        show-tooltip-when-overflow="true"
      /> -->
      <!-- <el-table-column
        label="隧道编码"
        align="center"
        prop="tunnelCode"
        width="150"
        show-tooltip-when-overflow="true"
      /> -->
      <el-table-column
        label="隧道名称"
        align="center"
        prop="tunnelNameAndHole"
        min-width="120"
        show-tooltip-when-overflow="true"
      />
      
      <!-- <el-table-column
        label="重新评定时间"
        align="center"
        prop="evaluateCompleteDate"
        min-width="180"
        show-tooltip-when-overflow="true"
      ></el-table-column> -->
      <!-- <el-table-column
        label="评定状态"
        align="center"
        prop="evaluateStatus"
        :formatter="formatStatus"
        width="150"
        show-tooltip-when-overflow="true"
      /> -->
      <!-- <el-table-column
        label="土建结构"
        align="center"
        prop="===="
        width="150"
        show-tooltip-when-overflow="true"
      /> -->
      <el-table-column
        label="土建结构评分"
        align="center"
        prop="civilStructureScore"
        width="150"
        show-tooltip-when-overflow="true"
      />
      <el-table-column
        label="土建结构评级"
        align="center"
        prop="civilStructureLevel"
        width="150"
        show-tooltip-when-overflow="true"
      />
      <!-- <el-table-column
        label="其他工程设施"
        align="center"
        prop="====="
        width="150"
        show-tooltip-when-overflow="true"
      /> -->
      <el-table-column
        label="其他工程设施评分"
        align="center"
        prop="otherFacilitiesScore"
        width="150"
        show-tooltip-when-overflow="true"
      />
      <el-table-column
        label="其他工程设施评级"
        align="center"
        prop="otherFacilitiesLevel"
        width="150"
        show-tooltip-when-overflow="true"
      />
      <!-- <el-table-column
        label="评定时间"
        align="center"
        prop="evaluateCompleteDate"
        min-width="160"
        show-tooltip-when-overflow="true"
      /> -->

      <el-table-column
        fixed="right"
        header-align="center"
        :resizable="false"
        align="center"
        min-width="220"
        label="操作"
      >
        <template #default="scope">
          <div class="operate-section">
            <el-button
              @click="judge(scope.row)"
              v-hasPermi="['project:score:score']"
              v-if="scope.row.evaluateStatus == 0"
              class="btn"
              type="primary"
              >重新评定</el-button
            >
            <el-button
              type="primary"
              @click="handleDetail(scope.row)"
              v-hasPermi="['project:score:detail']"
              v-if="scope.row.evaluateStatus == 2"
              >查看</el-button
            >
            <el-button
              type="danger"
              @click="handleDelete(scope.row)"
              v-hasPermi="['project:main:remove']"
              class="btn"
              >删除</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="queryParams.pageSize"
      :total="total"
      layout="->,total, sizes, prev, pager, next, jumper"
      style="margin-top: 10px; text-align: center"
    >
    </el-pagination>

    <!-- 添加或修改项目隧道评定主对话框 -->
    <el-dialog :title="title" v-model="open" width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="项目名称" prop="projectCode">
          <el-select v-model="form.projectCode" placeholder="请选择" clearable>
            <el-option
              v-for="item in projects"
              :key="item.projectCode"
              :label="item.projectName"
              :value="item.projectCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="隧道名称" prop="tunnelCode">
          <el-select v-model="form.tunnelCode" placeholder="请选择" clearable>
            <el-option
              v-for="item in tunnels"
              :key="item.tunnelCode"
              :label="item.tunnelNameAndHole"
              :value="item.tunnelCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-model="pictureOpen"
      :title="'评定'"
      width="66%"
      v-if="pictureOpen"
    >
      <mainDetails :projectObj="currentRow"></mainDetails>
    </el-dialog>
  </div>
</template>

<script>
import {
  listMain,
  getMain,
  delMain,
  addMain,
  updateMain,
  doJudge,
  exportData,
  getProjects,
  getTunnels,
} from "@/api/system/main";
import axios from "axios";
import { getToken } from "@/utils/auth";
// import reportDetail from "@/views/detection/technology/data/reportDetail.vue";
import mainDetails from "@/views/detection/technology/data/mainDetails.vue";
export default {
  name: "Main",
  components: { mainDetails },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      isExport: true,
      isExportIng: false,
      currentRow: {},
      pictureTitle: "",
      pictureOpen: false,
      // 项目隧道评定主表格数据
      mainList: [
        // {
        //   "id":1,
        //   "projectCode":"项目编码111",
        //   "projectName":"项目一",
        //   "tunnelCode":"隧道编码111",
        //   "tunnelName":"隧道一",
        //   "evaluateStatus":"未评定",
        //   "evaluateCompleteDate":"2023-08-05 00:01:00",
        //   "checkTime":"2023-08-05 00:01:00",
        //   "evaluateErrorMsg":null,
        //   "creatorCode":"zs",
        //   "creatorName":"张三",
        //   "creatorTime":"2023-08-05 00:01:00",
        //   "modifierCode":"ls",
        //   "modifierName":"李四",
        //   "modifierTime":"2023-08-05 00:01:05"
        // },{
        //   "id":2,
        //   "projectCode":"项目编码222",
        //   "projectName":"项目二",
        //   "tunnelCode":"隧道编码222",
        //   "tunnelName":"隧道二",
        //   "evaluateStatus":"评定失败",
        //   "evaluateCompleteDate":"2023-08-05 00:01:00",
        //   "checkTime":"2023-08-05 00:01:00",
        //   "evaluateErrorMsg":"数据缺失",
        //   "creatorCode":"ww",
        //   "creatorName":"王五",
        //   "creatorTime":"2023-08-05 00:03:00",
        //   "modifierCode":"zl",
        //   "modifierName":"赵六",
        //   "modifierTime":"2023-08-05 00:08:05"
        // },{
        //   "id":3,
        //   "projectCode":"项目编码333",
        //   "projectName":"项目三",
        //   "tunnelCode":"隧道编码333",
        //   "tunnelName":"隧道三",
        //   "evaluateStatus":"已评定",
        //   "evaluateCompleteDate":"2023-08-05 00:01:00",
        //   "checkTime":"2023-08-05 00:01:00",
        //   "evaluateErrorMsg":null,
        //   "creatorCode":"ww",
        //   "creatorName":"王五",
        //   "creatorTime":"2023-08-05 00:03:00",
        //   "modifierCode":"zl",
        //   "modifierName":"赵六",
        //   "modifierTime":"2023-08-05 00:08:05"
        // }
      ],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectTunnelInspectionId: null,
        tunnelName: null,
        tunnelCode: null,
        evaluateStatus: null,
        evaluateCompleteDate: null,
        evaluateErrorMsg: null,
        creatorCode: null,
        creatorName: null,
        creatorTime: null,
        createBeginTime: null,
        createEndTime: null,
        modifierCode: null,
        modifierName: null,
        modifierTime: null,
        isAvaliable: null,
        isDeleted: null,
      },
      // 表单参数
      form: {
        date1: [],
      },
      projects: [],
      tunnels: [],
      // 表单校验
      rules: {
        tunnelCode: [
          { required: true, message: "隧道名称不能为空", trigger: "blur" },
        ],
        projectCode: [
          { required: true, message: "项目名称不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.getProject();
    this.getTunnel();
    this.token = getToken();
  },
  methods: {
    /** 查询项目隧道评定主列表 */
    getList() {
      this.loading = true;
      if (
        this.queryParams.onDate != null &&
        this.queryParams.onDate.length != 0
      ) {
        this.queryParams.createBeginTime = this.queryParams.onDate[0];
        this.queryParams.createEndTime = this.queryParams.onDate[1];
      }
      listMain(this.queryParams).then((response) => {
        this.mainList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    handleDetail(row) {
      console.log(row);
      this.currentRow = row;
      this.pictureOpen = true;
      this.pictureTitle = "报告";
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectTunnelInspectionId: null,
        tunnelName: null,
        tunnelCode: null,
        projectName: null,
        projectCode: null,
        evaluateStatus: "0",
        evaluateCompleteDate: null,
        evaluateErrorMsg: null,
        creatorCode: null,
        creatorName: null,
        creatorTime: null,
        modifierCode: null,
        modifierName: null,
        modifierTime: null,
        isAvaliable: null,
        isDeleted: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.createEndTime = null;
      this.queryParams.createBeginTime = null;
      this.handleQuery();
    },
    sizeChangeHandle(val) {
      this.queryParams.pageSize = val;
      this.queryParams.pageNum = 1;
      this.getList();
    },
    currentChangeHandle: function currentChangeHandle(val) {
      this.queryParams.pageNum = val;
      this.getList();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加项目隧道评定主";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getMain(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改项目隧道评定主";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.projects.forEach((item, index, arr) => {
            if (item.projectCode == this.form.projectCode) {
              this.form.projectName = item.projectName;
            }
          });
          this.tunnels.forEach((item, index, arr) => {
            if (item.tunnelCode == this.form.tunnelCode) {
              this.form.tunnelName = item.tunnelName;
            }
          });
          if (this.form.id != null) {
            updateMain(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMain(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除项目隧道评定主编号为"' + ids + '"的数据项？')
        .then(function () {
          return delMain(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.mainList.length == 0) {
        this.$alert("无导出数据", "", {
          confirmButtonText: "确定",
        });
      } else {
        //开启loading
        this.isExport = false;
        this.isExportIng = true;
        if (
          this.queryParams.onDate != null &&
          this.queryParams.onDate.length != 0
        ) {
          this.queryParams.createBeginTime = this.queryParams.onDate[0];
          this.queryParams.createEndTime = this.queryParams.onDate[1];
        }
        exportData(this.queryParams)
          .then((res) => {
            if (res.code == "500") {
              this.$modal.msgSuccess("导出失败，请联系开发人员");
              return;
            } else {
              this.$modal.msgSuccess("导出成功");
            }
            let blob = new Blob([res], { type: "application/force-download" });
            console.log(blob);
            let fileReader = new FileReader();
            fileReader.readAsDataURL(blob);
            fileReader.onload = (e) => {
              let a = document.createElement("a");
              let date = new Date();
              let year = date.getFullYear();
              let month = date.getMonth() + 1;
              let day = date.getDate();
              a.download = `导出数据${year + "_" + month + "_" + day}.xlsx`;
              a.href = e.target.result;
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
            };
            this.isExport = true;
            this.isExportIng = false;
          })
          .catch(() => {
            this.isExport = true;
            this.isExportIng = false;
          });
      }
    },
    //评定状态
    formatStatus(row, column, cellValue) {
      if (cellValue == 0) {
        return "未评定";
      } else if (cellValue == 1) {
        return "评定中";
      } else if (cellValue == 2) {
        return "评定成功";
      } else if (cellValue == 3) {
        return "评定失败";
      }
    },
    judge(row) {
      const id = row.id || this.ids;
      doJudge(id).then((response) => {
        this.$modal.msgSuccess("开始评定");
        this.getList();
      });
    },
    getProject() {
      getProjects().then((response) => {
        this.projects = response.data;
      });
    },
    getTunnel() {
      getTunnels().then((response) => {
        this.tunnels = response.data;
        this.tunnels.forEach(function (item, index, array) {
          item.tunnelNameAndHole = item.tunnelName + "-" + item.hole;
        });
      });
    },
  },
};
</script>
