<template>
  <div class="project-detail-container">
    <el-card>
      <template #header>隧道情况</template>
      <div class="names">
        <span class="name-label">隧道名称</span>
        <span>{{ detaisl?.tunnelName || "--" }}</span>
        <span class="name-label">路线名称</span>
        <span >{{
          detaisl?.affiliatedRoadSection || "--"
        }}</span>
      </div>
    </el-card>
    <br />
    <el-card>
      <template #header>各设施评分</template>
      <div class="nums">
        <div class="num-view">
          <div class="num-view__header">
            <span class="num-view__title">土建设施</span>
            <span>
              <span class="num-view__num">{{
                detaisl?.civilStructureScore
              }}</span
              >&nbsp;
              <span class="num-view__num">{{
                detaisl?.civilStructureLevel
              }}</span>
            </span>
          </div>
          <el-progress
            :percentage="detaisl?.civilStructureScore"
            status="success"
            :show-text="false"
          />
        </div>
        <div class="num-view">
          <div class="num-view__header">
            <span class="num-view__title">其他工程设施</span>
            <span>
              <span class="num-view__num">{{
                detaisl?.appurtenanceScore || 67
              }}</span
              >&nbsp;
              <span class="num-view__num">{{
                detaisl?.otherLevel || "1类"
              }}</span>
            </span>
          </div>
          <el-progress
            :percentage="detaisl?.appurtenanceScore || 67"
            status="success"
            :show-text="false"
          />
        </div>
      </div>
    </el-card>
    <br />
    <el-tabs type="border-card" v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="土建设施" name="condtiondetaisl">
        <div class="condtiondetaisl">
          <div class="condtion">
            <span class="condtion__span">分项名称</span>
            <span class="condtion__span">分项等级</span>
          </div>
          <div class="condtion">
            <span class="condtion__span">洞口</span>
            <span class="condtion__span">
              <el-tag type="success" effect="dark">
                {{ condtiondetaisl?.openingConditionValue || '/' }}
              </el-tag>
            </span>
          </div>
          <div class="condtion">
            <span class="condtion__span">洞门</span>
            <span class="condtion__span">
              <el-tag type="success" effect="dark">
                {{ condtiondetaisl?.portalConditionValue || '/' }}
              </el-tag>
            </span>
          </div>
          <div class="condtion">
            <span class="condtion__span">衬砌</span>
            <span class="condtion__span">
              <el-tag type="success" effect="dark">
                {{ condtiondetaisl?.liningsSlsConditionValue || '/' }}
              </el-tag></span
            >
          </div>
          <div class="condtion">
            <span class="condtion__span">路面</span>
            <span class="condtion__span">
              <el-tag type="success" effect="dark">
                {{ condtiondetaisl?.roadConditionValue || '/' }}
              </el-tag>
            </span>
          </div>
          <div class="condtion">
            <span class="condtion__span">检修道</span>
            <span class="condtion__span">
              <el-tag type="success" effect="dark">
                {{ condtiondetaisl?.manholeConditionValue || '/' }}
              </el-tag>
            </span>
          </div>
          <div class="condtion">
            <span class="condtion__span">排水设施</span>
            <span class="condtion__span">
              <el-tag type="success" effect="dark">
                {{ condtiondetaisl?.drainageConditionValue || '/' }}
              </el-tag>
            </span>
          </div>
          <div class="condtion">
            <span class="condtion__span">吊顶及预埋件</span>
            <span class="condtion__span">
              <el-tag type="success" effect="dark">
                {{ condtiondetaisl?.ceilingConditionValue || '/' }}
              </el-tag></span
            >
          </div>
          <div class="condtion">
            <span class="condtion__span">内装饰</span>
            <span class="condtion__span">
              <el-tag type="success" effect="dark">
                {{ condtiondetaisl?.manholeInteriorConditionValue || '/' }}
              </el-tag>
            </span>
          </div>
          <div class="condtion">
            <span class="condtion__span">交通标志、标线</span>
            <span class="condtion__span">
              <el-tag type="success" effect="dark">
                {{ condtiondetaisl?.outlineConditionValue || '/' }}
              </el-tag>
            </span>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="其他工程设施"  name="ohter">
        <div class="condtiondetaisl">
          <div class="condtion">
            <span class="condtion__span">分项名称</span>
            <span class="condtion__span">分项得分/等级</span>
          </div>
          <div class="condtion">
            <span class="condtion__span">电缆沟</span>
            <span class="condtion__span">
              <el-tag type="success" effect="dark">
                 {{ condtiondetaisl?.cableGouConditionValue || '/' }}
              </el-tag>
            </span>
          </div>
          <div class="condtion">
            <span class="condtion__span">设备洞室</span>
            <span class="condtion__span">
              <el-tag type="success" effect="dark">
               {{ condtiondetaisl?.deviceHoleConditionValue || '/' }}
              </el-tag>
            </span>
          </div>
          <div class="condtion">
            <span class="condtion__span">洞内外联络通道</span>
            <span class="condtion__span">
              <el-tag type="success" effect="dark">
                {{ condtiondetaisl?.passageAccessConditionValue || '/' }}
              </el-tag></span
            >
          </div>
          <div class="condtion">
            <span class="condtion__span">洞口限高门架</span>
            <span class="condtion__span">
              <el-tag type="success" effect="dark">
                 {{ condtiondetaisl?.caveLimitFrameConditionValue || '/' }}
              </el-tag>
            </span>
          </div>
          <div class="condtion">
            <span class="condtion__span">洞口绿化</span>
            <span class="condtion__span">
              <el-tag type="success" effect="dark">
                {{ condtiondetaisl?.caveGreeningConditionValue || '/' }}
              </el-tag>
            </span>
          </div>
          <div class="condtion">
            <span class="condtion__span">消音设施</span>
            <span class="condtion__span">
              <el-tag type="success" effect="dark">
                {{ condtiondetaisl?.noiseReductionConditionValue || '/' }}
              </el-tag>
            </span>
          </div>
          <div class="condtion">
            <span class="condtion__span">减光设施</span>
            <span class="condtion__span">
              <el-tag type="success" effect="dark">
                {{ condtiondetaisl?.lightReductionConditionValue || '/' }}
              </el-tag></span
            >
          </div>
          <div class="condtion">
            <span class="condtion__span">污水处理设施</span>
            <span class="condtion__span">
              <el-tag type="success" effect="dark">
                {{ condtiondetaisl?.wastewaterTreatmentConditionValue || '/' }}
              </el-tag>
            </span>
          </div>
          <div class="condtion">
            <span class="condtion__span">洞口雕塑隧道铭牌</span>
            <span class="condtion__span">
              <el-tag type="success" effect="dark">
               {{ condtiondetaisl?.caveTunnelNameplateConditionValue || '/' }}
              </el-tag>
            </span>
          </div>
          <div class="condtion">
            <span class="condtion__span"> 房屋设施</span>
            <span class="condtion__span">
              <el-tag type="success" effect="dark">
               {{ condtiondetaisl?.housingTreatementConditionValue || '/' }}
              </el-tag>
            </span>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
    
<script setup name="reportDetail">
import { ref, reactive, onMounted } from "vue";
import { getDetailReport, getDetailScoreValue } from "@/api/system/report";
import originPicture from "./originPicture.vue";
const emit = defineEmits(["btnTap"]);
const props = defineProps({
  projectObj: {
    type: Object,
    default: () => {},
  },
});
let detaisl = ref({});
let condtiondetaisl = ref({});
const detailForm = ref();
const activeName = ref("condtiondetaisl");
const handleClick = (tab, event) => {
  console.log(tab, event)
}

const btnAction = (type) => {
  emit("btnTap", type);
};

onMounted(() => {
  detailForm.value = props.projectObj;
  getList(props.projectObj.id);
  getDetailScoreCondtionValue(props.projectObj.id);
});
const getList = (id) => {
  getDetailReport(id).then((response) => {
    detaisl.value = response.data;
  });
};

const getDetailScoreCondtionValue = (id) => {
  getDetailScoreValue(id).then((response) => {
    condtiondetaisl.value = response;
    Object.keys(condtiondetaisl.value).forEach((item) => { 
      condtiondetaisl.value[item] = condtiondetaisl.value[item] === 'null' ? '' : condtiondetaisl.value[item];
    });
  });
};
</script>
    
<style lang="scss" scoped>
.names {
  display: flex;
  border: 1px solid #eee;
  span {
    flex: 1;
    height: 32px;
    line-height: 32px;
    box-sizing: border-box;
    padding: 0 12px;
    border-right: 1px solid #eee;
  }
  .name-label {
    background: #e9f0f6;
  }
}
.nums {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .num-view {
    flex: 1;
    margin: 0 12px;
    background: #e9f0f6;
    box-sizing: border-box;
    padding: 16px;
    border-radius: 4px;
    .num-view__header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      .num-view__num {
        color: var(--el-color-success);
        border: 1px solid var(--el-color-success);
        padding: 3px 7px;
        border-radius: 2px;
      }
    }
  }
}
.condtiondetaisl {
  .condtion {
    display: flex;
    border-left: 1px solid #ebeef5;
    .condtion__span {
      flex: 1;
      line-height: 32px;
      box-sizing: border-box;
      padding: 0 12px;
      border-right: 1px solid #ebeef5;
    }
  }
  .condtion:nth-child(2n + 1) {
    background: #f5f5f5;
  }
}
</style>