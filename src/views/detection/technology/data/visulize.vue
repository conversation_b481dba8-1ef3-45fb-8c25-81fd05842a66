<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="auto"
    >
      <el-form-item label="隶属单位" prop="tunnelName">
        <el-input
          v-model="queryParams.tunnelName"
          placeholder="请输入隧道名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-select
          v-model="queryParams.projectName"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="item in projects"
            :key="item.projectCode"
            :label="item.projectName"
            :value="item.projectName"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="所属路段" prop="affiliatedRoadSection">
        <el-input
          v-model="queryParams.affiliatedRoadSection"
          placeholder="请输入所属路段"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="隧道名称" prop="tunnelName">
        <el-input
          v-model="queryParams.tunnelName"
          placeholder="请输入隧道名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="隧道幅别" prop="tunnelName">
        <el-input
          v-model="queryParams.tunnelName"
          placeholder="请输入隧道幅别"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="mini" @click="handleQuery"
          >搜索</el-button
        >
        <el-button size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          @click="handleAdd"
          v-hasPermi="['tunnel:visulize:add']"
          >新增</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="visulizeList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号" align="center" width="50" />
      <el-table-column label="隶属单位" align="center"  min-width="100" prop="affiliatedUnit" />
      <el-table-column label="项目名称" align="center"  min-width="140" prop="projectName" />
      <!-- <el-table-column label="路线编号" align="center" prop="tunnelRoadNumber" /> -->
      <el-table-column label="隧道编码" align="center" min-width="100" prop="tunnelCode" />
      <el-table-column label="隧道名称" align="center" min-width="120" prop="tunnelName" />
      <!-- <el-table-column
        label="隧道总编码"
        align="center"
        prop="totalTunnelCode"
      /> -->
      <el-table-column label="隧道幅别" align="center" prop="hole" min-width="100" />
      <el-table-column label="起止桩号" align="center" min-width="180" prop="startingStation" >
        <template #default="scope"> 
          {{ scope.row.startingStation + "--" + scope.row.endingStation }}
        </template>
      </el-table-column>
      <el-table-column label="隧道长度" align="center" min-width="100" prop="tunnelLength" />
      <!-- <el-table-column label="运营公司" align="center" prop="company" />
      <el-table-column
        label="定检时间"
        align="center"
        prop="checkTime"
        width="180"
      /> -->
      <el-table-column
        label="操作"
        align="center"
        min-width="100"
        class-name="small-padding fixed-width"
      >
        <template v-slot:default="scope">
          <el-button
            size="mini"
            type="primary"
            @click="handleDetail(scope.row)"
            v-hasPermi="['tunnel:visulize:edit']"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(scope.row)"
            v-hasPermi="['tunnel:visulize:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="queryParams.pageSize"
      :total="total"
      layout="->,total, sizes, prev, pager, next, jumper"
      style="margin-top: 10px; text-align: center"
    >
    </el-pagination>

    <!-- 添加或修改三维可视化对话框 -->
    <el-dialog :title="title" v-model="open" width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="隧道总编码" prop="totalTunnelCode">
          <el-input
            v-model="form.totalTunnelCode"
            placeholder="请输入隧道总编码"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listVisulize,
  getVisulize,
  delVisulize,
  addVisulize,
} from "@/api/system/visulize.js";
import {
  getProjects,
} from "@/api/system/report";
export default {
  name: "Visulize",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 三维可视化表格数据
      visulizeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tunnelName: null,
        totalTunnelCode: null,
        roadName: null,
        company: null,
        checkTime: null,
        creatorCode: null,
        creatorName: null,
        creatorTime: null,
        modifierCode: null,
        modifierName: null,
        modifierTime: null,
        isAvailable: null,
        isDeleted: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      projects:[]
    };
  },
  created() {
    this.getList();
    this.getProject();
  },
  methods: {
    /** 查询三维可视化列表 */
    getList() {
      this.loading = true;
      listVisulize(this.queryParams).then((response) => {
        this.visulizeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getProject() {
      getProjects().then((response) => {
        this.projects = response.data;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        tunnelName: null,
        totalTunnelCode: null,
        roadName: null,
        company: null,
        checkTime: null,
        creatorCode: null,
        creatorName: null,
        creatorTime: null,
        modifierCode: null,
        modifierName: null,
        modifierTime: null,
        isAvailable: null,
        isDeleted: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    sizeChangeHandle(val) {
      this.queryParams.pageSize = val;
      this.queryParams.pageNum = 1;
      this.getList();
    },
    currentChangeHandle: function currentChangeHandle(val) {
      this.queryParams.pageNum = val;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加三维可视化";
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          addVisulize(this.form).then((response) => {
            this.$modal.msgSuccess("新增成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    /** 查看详情 */
    handleDetail(row) {
      const totalTunnelCode = row.totalTunnelCode;
      getVisulize(totalTunnelCode);
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除三维可视化编号为"' + ids + '"的数据项？')
        .then(function () {
          return delVisulize(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>
