<template>
    <div class="project-detail-container">
      <el-table v-loading="state.loading" :data="state.pictureList">
        <el-table-column align="center">
          <template #default="scope">
              <el-row :gutter="20">
                  <el-col :span="12"><div class="grid-content bg-purple">

                      <a> <img :src="scope.row.thumbnailUrl" width="500" height="150" @click="turnOrigin(scope.row)"/></a>
                      <p>{{ scope.row.thumbnailName }}</p>
                  </div></el-col>
<!--                  <el-col :span="12"><div class="grid-content bg-purple">-->
<!--                      <a> <img :src="scope.row.thumbnailUrl" width="500" height="150" @click="turnOrigin(scope.row)"/></a>-->
<!--                      <p>{{ scope.row.thumbnailName }}</p>-->

<!--                  </div></el-col>-->
              </el-row>
<!--            <a href="{{ scope.row.layoutUrl }}"></a>-->

  　　    </template>
        </el-table-column>
      </el-table>

      <el-pagination
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
          :current-page="state.queryParams.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="state.queryParams.pageSize"
          :total="state.total"
          layout="->,total, sizes, prev, pager, next, jumper"
          style="margin-top:10px;text-align: center"
      >
      </el-pagination>
      <el-dialog v-model="state.originOpen" :title="state.originTitle" width="50%" v-if="state.originOpen">
        <originPicture :projectObj="state.currentRow"></originPicture>
      </el-dialog>
    </div>
</template>
    
<script setup name="pictureDetail">
import { ref, reactive, onMounted } from 'vue';
import { listPicture } from "@/api/system/picture";
import originPicture from "./originPicture.vue";
const emit = defineEmits(['btnTap']);
const props = defineProps({
    projectObj: {
        type: Object,
        default: () => {}
    }
});
const state = reactive({
    detailForm: {
        startDate: '',
        endDate: ''
    },
    rules: [],
    statusList: [],
    companyList: [],
    // 遮罩层
    loading: true,
    // 选中数组
    ids: [],
    // 非单个禁用
    single: true,
    // 非多个禁用
    multiple: true,
    originOpen:false,
    originTitle:"展布原图",
    // 显示搜索条件
    showSearch: true,
    // 总条数
    total: 0,
    currentRow:{},
    pictureList: [],
    // 弹出层标题
    title: "",
    // 是否显示弹出层
    open: false,
    projectTunnelInspectionId:null,
    // 查询参数
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      projectTunnelInspectionId: null,
      zipName: null,
      picNum: null,
      successNum: null,
      failNum: null,
      createBeginTime: null,
      createEndTime: null,
      errorMsg: null,
      dealTime: null,
      dataType: null
    },
    // 表单参数
    form: {},
})

const btnAction = (type) => {
    emit('btnTap', type);
}

onMounted(() => {
    state.detailForm = props.projectObj;
    console.log(state.detailForm);
    state.projectTunnelInspectionId=props.projectObj.projectTunnelInspectionId;
    getList(props.projectObj.projectTunnelInspectionId);

})
const getList = (id) => {
  state.loading = true;
  console.log("this.projectTunnelInspectionId"+id)
  state.queryParams.projectTunnelInspectionId=id;
  listPicture(state.queryParams).then(response => {
    state.pictureList = response.rows;
    state.total = response.total;
    state.loading = false;
  });
}

const turnOrigin = (row) => {
  console.log(row)
  state.currentRow=row;
  state.originOpen=true;
}
const sizeChangeHandle=(val) =>{
  state.queryParams.pageSize = val;
  state.queryParams.pageNum = 1;
  getList();
}
const currentChangeHandle=(val)=> {

  state.queryParams.pageNum = val;
  getList();
}
</script>
    
<style lang="scss" scoped>
:deep(.el-input) {
    width: 20px;
}

.btn-section {
    text-align: right;
}
</style>