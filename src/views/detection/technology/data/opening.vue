<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" label-width="auto" :inline="true" v-show="showSearch" >
      <el-form-item label="项目编码" prop="projectCode">
        <el-input
            v-model="queryParams.projectCode"
            placeholder="请输入项目编码"
            clearable
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName" >
        <el-select v-model="queryParams.projectName" placeholder="请选择" clearable filterable reserve-keyword>
          <el-option
              v-for="item in projects"
              :key="item.projectCode"
              :label="item.projectName"
              :value="item.projectName">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="隧道编码" prop="tunnelCode">
        <el-input
            v-model="queryParams.tunnelCode"
            placeholder="请输入隧道编码"
            clearable
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="隧道名称" prop="tunnelName">
        <el-select v-model="queryParams.tunnelName" placeholder="请选择" clearable>
          <el-option
              v-for="item in tunnels"
              :key="item.tunnelCode"
              :label="item.tunnelName"
              :value="item.tunnelName">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="桩号" prop="pileNumber" >
        <el-input
            v-model="queryParams.pileNumber"
            placeholder="请输入桩号"
            clearable
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="检测时间范围" prop="onDate">
        <el-date-picker
            v-model="queryParams.onDate"
            type="daterange"
            range-separator="~"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary"  size="mini" @click="handleQuery">搜索</el-button>
        <el-button  size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          @click="handleAdd"
          v-hasPermi="['tunnel:opening:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['tunnel:opening:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['tunnel:opening:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            size="mini"
            @click="handleBatchAdd"
            v-hasPermi="['tunnel:opening:batchAdd']"
        >批量新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          size="mini"
          @click="handleExport"
          v-hasPermi="['tunnel:opening:export']"
          v-if="isExport"
        >导出</el-button>
        <el-button type="primary" v-if="isExportIng" :loading="true">导出中</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="openingList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="项目编码" align="center" prop="projectCode" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="项目名称" align="center" prop="projectName" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="隧道编码" align="center" prop="tunnelCode" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="隧道名称" align="center" prop="tunnelNameAndHole" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="开始时间" align="center" prop="beginTime" width="150" show-tooltip-when-overflow="true">
      </el-table-column>
      <el-table-column label="截至时间" align="center" prop="endTime" width="150" show-tooltip-when-overflow="true">
      </el-table-column>
      <el-table-column label="桩号" align="center" prop="pileNumber" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="检查内容" align="center" prop="inspectioContent" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="判定描述" align="center" prop="judgmentDescription" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="图片" align="center" prop="pictureUrl"  >
        <template v-slot:default="scope">
          <el-image preview-teleported :src="scope.row.pictureUrl" :preview-src-list="scope.row.prePictureList" fit="cover"/>
        </template>
      </el-table-column>
      <el-table-column label="状况值" align="center" prop="conditionValue" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="创建人编码" align="center" prop="creatorCode" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="创建人名称" align="center" prop="creatorName" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="创建时间" align="center" prop="creatorTime" width="180" show-tooltip-when-overflow="true">
      </el-table-column>
      <el-table-column label="修改人编码" align="center" prop="modifierCode" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="修改人名称" align="center" prop="modifierName" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="修改时间" align="center" prop="modifierTime" width="180" show-tooltip-when-overflow="true">
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" class-name="small-padding fixed-width"
                       width="200">
        <template v-slot:default="scope">
          <el-button
            size="mini"
            type="success"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['tunnel:opening:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(scope.row)"
            v-hasPermi="['tunnel:opening:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="queryParams.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="queryParams.pageSize"
        :total="total"
        layout="->,total, sizes, prev, pager, next, jumper"
        style="margin-top:10px;text-align: center"
    >
    </el-pagination>

    <!-- 添加或修改隧道检查-洞口信息对话框 -->
    <el-dialog :title="title" v-model="open" width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
          <el-form-item label="项目名称" prop="projectCode">
            <el-select v-model="form.projectCode" placeholder="请选择" clearable @change="changeProject">
              <el-option
                  v-for="item in projects"
                  :key="item.projectCode"
                  :label="item.projectName"
                  :value="item.projectCode">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="隧道名称" prop="tunnelCode">
            <el-select v-model="form.tunnelCode" placeholder="请选择" clearable>
              <el-option
                  v-for="item in tunnels"
                  :key="item.tunnelCode"
                  :label="item.tunnelNameAndHole"
                  :value="item.tunnelCode">
              </el-option>
            </el-select>
          </el-form-item>

        <el-form-item label="开始~截至时间:" prop="date1">
          <el-date-picker
              v-model="form.date1"
              type="daterange"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              range-separator="~"
              start-placeholder="开始时间"
              end-placeholder="截至时间">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="桩号" prop="pileNumber">
          <el-input v-model="form.pileNumber" placeholder="请输入桩号" class="myInput"/>
        </el-form-item>
        <el-form-item label="检查内容">
          <el-input v-model="form.inspectioContent" placeholder="请输入检查内容" type="textarea" class="myInput"/>
        </el-form-item>
        <el-form-item label="判定描述" prop="judgmentDescription">
          <el-input v-model="form.judgmentDescription" placeholder="请输入判定描述" type="textarea" class="myInput"/>
        </el-form-item>
        <el-form-item label="状况值" prop="conditionValue">
          <el-input v-model="form.conditionValue" placeholder="请输入状况值" class="myInput"/>
        </el-form-item>
        <el-form-item label="图片" prop="pictureUrl">
          <el-upload v-model="form.pictureUrl"
                     class="upload-demo"
                     action="/dev-api/tunnel/upload/uploadPicture"
                     :on-remove="handleRemove"
                     :on-success="fileUpload"
                     :show-file-list="true"
                     :headers="{'Authorization': 'Bearer ' + token}"
                     list-type="picture-card"
                     :file-list="fileLists"
                     :limit="1"
                     :on-exceed="handleExceed">
            <el-button type="primary">上传图片</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 批量上传 -->
    <el-dialog v-model="batchAddOpen" :title="batchAddTitle" width="40%" destroy-on-close="true" @closed="closeFileUpload()">
      <openingFileUpload></openingFileUpload>
    </el-dialog>
  </div>
</template>

<script>
import { listOpening, getOpening, delOpening, addOpening, updateOpening,exportData,getProjects,getTunnels } from "@/api/system/opening";
import openingFileUpload from "./fileUpload/openingFileUpload.vue";
import axios from 'axios'
import { getToken } from "@/utils/auth";

export default {
  name: "Opening",
  components: {openingFileUpload},
  data() {
    return {
      // 遮罩层
      loading: true,
      selectLoading:true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 隧道检查-洞口信息表格数据
      openingList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      batchAddTitle: "",
      batchAddOpen: false,
      token:null,
      isExport:true,
      isExportIng:false,
      fileLists:[],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectTunnelInspectionId: null,
        tunnelCode: null,
        tunnelName: null,
        beginTime: null,
        endTime: null,
        pileNumber: null,
        inspectioContent: null,
        judgmentDescription: null,
        pictureUrl: null,
        conditionValue: null,
        creatorCode: null,
        creatorName: null,
        creatorTime: null,
        modifierCode: null,
        modifierName: null,
        modifierTime: null,
        isAvaliable: null,
        isDeleted: null,
        createBeginTime:null,
        createEndTime:null,
        onDate:[],
        offDate:[]
      },
      projects: [
      ],
      tunnels: [
      ],
      // 表单参数
      form: {
        date1:[]
      },
      // 表单校验
      rules: {
        // remainShelfLife :[{ pattern: /^[0-9]*[1-9][0-9]*$/, message: '只能输入正整数', trigger: 'blur' }],
        // totalShelfLife :[{required: true, message: '总货架寿命不能为空!', trigger: 'blur'},{ pattern: /^[0-9]*[1-9][0-9]*$/, message: '只能输入正整数', trigger: 'blur' }],
        // warehouseLatestDeliveryDay :[{ pattern: /^[0-9]*[1-9][0-9]*$/, message: '只能输入正整数', trigger: 'blur' }],
        // storeBestSaleDay :[{ pattern: /^[0-9]*[1-9][0-9]*$/, message: '只能输入正整数', trigger: 'blur' }],
        // maxSaleDay :[{ pattern: /^[0-9]*[1-9][0-9]*$/, message: '只能输入正整数', trigger: 'blur' }],
        // materialCode :[{required: true, message: '物料编码不能为空!', trigger: 'blur'},{ validator:validatePass },{max:30, message: '物料编码最大为30个字符',trigger:'blur'}],
        /*skuDateList: [{required: true, message: '有效日期不能为空', trigger: 'blur'}],*/
        tunnelCode: [{required: true, message: '隧道名称不能为空', trigger: 'blur'}],
        projectCode: [{required: true, message: '项目名称不能为空', trigger: 'blur'}],
        pileNumber: [{required: true, message: '桩号不能为空', trigger: 'blur'}],
        conditionValue:[{ pattern: /^[0-4]?$/, message: '只能输0-4的整数', trigger: 'blur' },{ required: true, message: '状况值不能为空', trigger: 'blur'}],
        // num:[{ pattern: /^\d{1,8}(\.\d{0,3})?$/, message: '只能输数字，最多三位整数，最多3位小数', trigger: 'blur' },{ required: true, message: '数量不能为空', trigger: 'blur'}],
        // combineSkuUnit:[{ required: true, message: '单位不能为空', trigger: 'blur'}],
        // merchantId: [{required: true, message: '商家不能为空', trigger: 'blur'}],
        /*standardPrice: [
            { required: true, message: '标准价不能为空', trigger: 'blur' },
            {
                validator (rule, value, callback) {
                    var pattern = '^\\d{1,8}(\\.\\d{0,2})?$';
                    var reg = new RegExp(pattern, 'g');
                    if (reg.test(value)) {
                        callback()
                    } else {
                        callback(new Error('输入非法'))
                    }
                },
                trigger: 'blur'
            }
        ],*/
        // isDirectSending: [{required: true, message: '是否直送不能为空', trigger: 'blur'}],
        // needRequired: [{required: true, message: '是否质检不能为空', trigger: 'blur'}],
        // abbreviation: [{max:50, message: '简称最大为50个字符',trigger:'blur'}],
        // name: [{required: true, message: 'sku名称不能为空', trigger: 'blur'},{max:50, message: 'sku名称最大为50个字符',trigger:'blur'}]
      }
    };
  },
  created() {
    this.getList();
    this.getProject();
    this.getTunnel();
    this.token=getToken();
  },
  methods: {
    /** 查询隧道检查-洞口信息列表 */
    getList() {
      this.loading = true;
      console.log(this.queryParams)
      if(this.queryParams.onDate!=null&&this.queryParams.onDate.length!=0){
        this.queryParams.createBeginTime=this.queryParams.onDate[0];
        this.queryParams.createEndTime=this.queryParams.onDate[1];
      }
      listOpening(this.queryParams).then(response => {
        console.log("response")
        console.log(response)
        this.openingList = response.rows;
        this.openingList.forEach(item=>{Object.assign(item,{prePictureList:[item.pictureUrl]})});
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    closeFileUpload(){
      this.handleQuery();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectTunnelInspectionId: null,
        tunnelCode: null,
        tunnelName: null,
        beginTime: null,
        endTime: null,
        pileNumber: null,
        inspectioContent: null,
        judgmentDescription: null,
        pictureUrl: null,
        conditionValue: null,
        creatorCode: null,
        creatorName: null,
        creatorTime: null,
        modifierCode: null,
        modifierName: null,
        modifierTime: null,
        isAvaliable: null,
        isDeleted: null
      };
      this.resetForm("form");
      this.fileLists=[];
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.createEndTime=null;
      this.queryParams.createBeginTime=null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加隧道检查-洞口信息";
    },
    /** 批量新增按钮操作 */
    handleBatchAdd() {
      this.batchAddOpen = true;
      this.batchAddTitle = "批量新增";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getOpening(id).then(response => {
        this.form = response.data;
        this.form.date1=[response.data.beginTime,response.data.endTime]
        console.log("11111")
        if(response.data.pictureUrl!=null){
          let pictureList = [response.data.pictureUrl];
          this.fileLists = pictureList.map(item => {
            return {
              name: item,
              url: item
            }
          });
        }
        this.open = true;
        this.title = "修改隧道检查-洞口信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {

        if (valid) {
          if (this.form.date1!=null&&this.form.date1.length != 0) {
            this.form.beginTime=this.form.date1[0];
            this.form.endTime=this.form.date1[1];
          }
          this.projects.forEach((item,index,arr)=>{
            if(item.projectCode==this.form.projectCode){
              this.form.projectName=item.projectName;
            }
          })
          this.tunnels.forEach((item,index,arr)=>{
            if(item.tunnelCode==this.form.tunnelCode){
              this.form.tunnelName=item.tunnelName;
            }
          })
          if (this.form.id != null) {
            updateOpening(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addOpening(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除隧道检查-洞口信息编号为"' + ids + '"的数据项？').then(function() {
        return delOpening(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport: function () {
      if (this.openingList.length == 0) {
        this.$alert('无导出数据', '', {
          confirmButtonText: '确定'
        })
      } else {
        //开启loading
        this.isExport = false;
        this.isExportIng = true;
        if (this.queryParams.onDate!=null&&this.queryParams.onDate.length != 0) {
          this.queryParams.createBeginTime = this.queryParams.onDate[0];
          this.queryParams.createEndTime = this.queryParams.onDate[1];
        }
        exportData(this.queryParams).then(res => {
          if (res.code == '500') {
            this.$modal.msgSuccess("导出失败，请联系开发人员");
            return
          } else {
            this.$modal.msgSuccess("导出成功");
          }
          let blob = new Blob([res], {type: "application/force-download"})
          console.log(blob);
          let fileReader = new FileReader()
          fileReader.readAsDataURL(blob)
          fileReader.onload = (e) => {
            let a = document.createElement('a')
            let date = new Date();
            let year = date.getFullYear();
            let month = date.getMonth()+1;
            let day = date.getDate();
            a.download = `导出数据${year+"_"+month+"_"+day}.xlsx`
            a.href = e.target.result
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
          }
          this.isExport = true;
          this.isExportIng = false;
        }).catch(() => {
          this.isExport = true;
          this.isExportIng = false;
        });

      }
    },
    sizeChangeHandle(val) {
      this.queryParams.pageSize = val;
      this.queryParams.pageNum = 1;
      this.getList();
    },
    currentChangeHandle: function currentChangeHandle(val) {
      this.queryParams.pageNum = val;
      this.getList();
    },
    handleExceed(files, fileList){
      this.$modal.msgWarning(`只能上传一个文件`);
    },
    getProject() {
      getProjects().then(response => {
        this.projects = response.data;
      });
    },
    getTunnel() {
      getTunnels().then(response => {
        this.tunnels = response.data;
        this.tunnels.forEach(function (item, index, array) {
          item.tunnelNameAndHole=item.tunnelName+"-"+item.hole;
        })
      });
    },
    //上传图片
    fileUpload: function (response, file, fileList) {

      let pictureList = [response.msg];
      this.fileLists = pictureList.map(item => {
        return {
          name: item,
          url: item
        }
      });
      var url = response.msg;
      this.form.pictureUrl = url;
    },
    //上传图片
    handleRemove() {
      this.form.pictureUrl = null;
    },
  }
};
</script>


<style lang="scss" scoped>
.batch-container {
  .download {
    color: #409eff;
    cursor: pointer;
  }

  .desc-section {
    display: flex;
    flex-direction: column;
  }
  .img-list {
    display: flex;

    .img-item {
      width: 50px;
      height: 50px;
      margin-left: 5px;

      &:first-child {
        margin-left: 0;
      }
    }
  }
}
.app-container {
  height: 200%;
  padding: 10 20px;
  width: 100%;

  .search-section {
    padding: 20px 30;
  }

  .page-section {
    padding-top: 20px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}
.myInput {width: 215px}
</style>