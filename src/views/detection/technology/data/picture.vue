<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="auto">
      <el-form-item label="项目隧道关联ID" prop="projectTunnelInspectionId">
        <el-input
          v-model="queryParams.projectTunnelInspectionId"
          placeholder="请输入项目隧道关联ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="隧道名称" prop="tunnelName">
        <el-input
          v-model="queryParams.tunnelName"
          placeholder="请输入隧道名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="隧道编码" prop="tunnelCode">
        <el-input
          v-model="queryParams.tunnelCode"
          placeholder="请输入隧道编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="展布图原图名称" prop="layoutName">
        <el-input
          v-model="queryParams.layoutName"
          placeholder="请输入展布图原图名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="展布图缩略图名称" prop="thumbnailName">
        <el-input
          v-model="queryParams.thumbnailName"
          placeholder="请输入展布图缩略图名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="图片排序" prop="sort">
        <el-input
          v-model="queryParams.sort"
          placeholder="请输入图片排序"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建人编码" prop="creatorCode">
        <el-input
          v-model="queryParams.creatorCode"
          placeholder="请输入创建人编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建人名称" prop="creatorName">
        <el-input
          v-model="queryParams.creatorName"
          placeholder="请输入创建人名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="creatorTime">
        <el-date-picker clearable
          v-model="queryParams.creatorTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择创建时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="修改人编码" prop="modifierCode">
        <el-input
          v-model="queryParams.modifierCode"
          placeholder="请输入修改人编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="修改人名称" prop="modifierName">
        <el-input
          v-model="queryParams.modifierName"
          placeholder="请输入修改人名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="修改时间" prop="modifierTime">
        <el-date-picker clearable
          v-model="queryParams.modifierTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择修改时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="数据是否可用" prop="isAvaliable">
        <el-input
          v-model="queryParams.isAvaliable"
          placeholder="请输入数据是否可用"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="数据是否删除" prop="isDeleted">
        <el-input
          v-model="queryParams.isDeleted"
          placeholder="请输入数据是否删除"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['project:picture:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['project:picture:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['project:picture:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['project:picture:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="pictureList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="项目隧道关联ID" align="center" prop="projectTunnelInspectionId" />
      <el-table-column label="隧道名称" align="center" prop="tunnelName" />
      <el-table-column label="隧道编码" align="center" prop="tunnelCode" />
      <el-table-column label="展布图原图名称" align="center" prop="layoutName" />
      <el-table-column label="展布图原图url" align="center" prop="layoutUrl" />
      <el-table-column label="展布图缩略图名称" align="center" prop="thumbnailName" />
      <el-table-column label="展布图缩略图url" align="center" prop="thumbnailUrl" />
      <el-table-column label="图片类型" align="center" prop="type" />
      <el-table-column label="图片排序" align="center" prop="sort" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['project:picture:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['project:picture:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="queryParams.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="queryParams.pageSize"
        :total="total"
        layout="->,total, sizes, prev, pager, next, jumper"
        style="margin-top:10px;text-align: center"
    >
    </el-pagination>

    <!-- 添加或修改展布图管理对话框 -->
    <el-dialog :title="title" v-model="open" width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="项目隧道关联ID" prop="projectTunnelInspectionId">
          <el-input v-model="form.projectTunnelInspectionId" placeholder="请输入项目隧道关联ID" />
        </el-form-item>
        <el-form-item label="隧道名称" prop="tunnelName">
          <el-input v-model="form.tunnelName" placeholder="请输入隧道名称" />
        </el-form-item>
        <el-form-item label="隧道编码" prop="tunnelCode">
          <el-input v-model="form.tunnelCode" placeholder="请输入隧道编码" />
        </el-form-item>
        <el-form-item label="展布图原图名称" prop="layoutName">
          <el-input v-model="form.layoutName" placeholder="请输入展布图原图名称" />
        </el-form-item>
        <el-form-item label="展布图原图url" prop="layoutUrl">
          <el-input v-model="form.layoutUrl" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="展布图缩略图名称" prop="thumbnailName">
          <el-input v-model="form.thumbnailName" placeholder="请输入展布图缩略图名称" />
        </el-form-item>
        <el-form-item label="展布图缩略图url" prop="thumbnailUrl">
          <el-input v-model="form.thumbnailUrl" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="图片排序" prop="sort">
          <el-input v-model="form.sort" placeholder="请输入图片排序" />
        </el-form-item>
        <el-form-item label="创建人编码" prop="creatorCode">
          <el-input v-model="form.creatorCode" placeholder="请输入创建人编码" />
        </el-form-item>
        <el-form-item label="创建人名称" prop="creatorName">
          <el-input v-model="form.creatorName" placeholder="请输入创建人名称" />
        </el-form-item>
        <el-form-item label="创建时间" prop="creatorTime">
          <el-date-picker clearable
            v-model="form.creatorTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择创建时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="修改人编码" prop="modifierCode">
          <el-input v-model="form.modifierCode" placeholder="请输入修改人编码" />
        </el-form-item>
        <el-form-item label="修改人名称" prop="modifierName">
          <el-input v-model="form.modifierName" placeholder="请输入修改人名称" />
        </el-form-item>
        <el-form-item label="修改时间" prop="modifierTime">
          <el-date-picker clearable
            v-model="form.modifierTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择修改时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="数据是否可用" prop="isAvaliable">
          <el-input v-model="form.isAvaliable" placeholder="请输入数据是否可用" />
        </el-form-item>
        <el-form-item label="数据是否删除" prop="isDeleted">
          <el-input v-model="form.isDeleted" placeholder="请输入数据是否删除" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPicture, getPicture, delPicture, addPicture, updatePicture } from "@/api/system/picture";

export default {
  name: "Picture",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 展布图管理表格数据
      pictureList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectTunnelInspectionId: null,
        tunnelName: null,
        tunnelCode: null,
        layoutName: null,
        layoutUrl: null,
        thumbnailName: null,
        thumbnailUrl: null,
        type: null,
        sort: null,
        creatorCode: null,
        creatorName: null,
        creatorTime: null,
        modifierCode: null,
        modifierName: null,
        modifierTime: null,
        isAvaliable: null,
        isDeleted: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询展布图管理列表 */
    getList() {
      this.loading = false;
      // listPicture(this.queryParams).then(response => {
      //   this.pictureList = response.rows;
      //   this.total = response.total;
      //   this.loading = false;
      // });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectTunnelInspectionId: null,
        tunnelName: null,
        tunnelCode: null,
        layoutName: null,
        layoutUrl: null,
        thumbnailName: null,
        thumbnailUrl: null,
        type: null,
        sort: null,
        creatorCode: null,
        creatorName: null,
        creatorTime: null,
        modifierCode: null,
        modifierName: null,
        modifierTime: null,
        isAvaliable: null,
        isDeleted: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    sizeChangeHandle(val) {
      this.queryParams.pageSize = val;
      this.queryParams.pageNum = 1;
      this.getList();
    },
    currentChangeHandle: function currentChangeHandle(val) {
      this.queryParams.pageNum = val;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加展布图管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getPicture(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改展布图管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updatePicture(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPicture(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除展布图管理编号为"' + ids + '"的数据项？').then(function() {
        return delPicture(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('project/picture/export', {
        ...this.queryParams
      }, `picture_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
