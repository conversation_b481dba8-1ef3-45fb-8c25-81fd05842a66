<template>
    <div class="project-detail-container">
      <el-table v-loading="state.loading" :data="state.logList">
        <el-table-column label="图片压缩包上传名称" align="center" prop="zipName" width="150" show-tooltip-when-overflow="true"/>
        <el-table-column label="图片压缩包总数" align="center" prop="picNum" width="150" show-tooltip-when-overflow="true"/>
        <el-table-column label="图片压缩包成功数" align="center" prop="successNum" width="150" show-tooltip-when-overflow="true"/>
        <el-table-column label="图片压缩包失败数" align="center" prop="failNum" width="150" show-tooltip-when-overflow="true"/>
        <el-table-column label="图片压缩包失败原因" align="center" prop="errorMsg" width="150" show-tooltip-when-overflow="true"/>
        <el-table-column label="图片压缩包处理时间" align="center" prop="dealTime" width="150" show-tooltip-when-overflow="true"/>
      </el-table>
    </div>
</template>
    
<script setup name="logDetail">
import { ref, reactive, onMounted } from 'vue';
import { listLog, getLog, delLog, addLog } from "@/api/system/log";
const emit = defineEmits(['btnTap']);
const props = defineProps({
    projectObj: {
        type: Object,
        default: () => {}
    }
});
const state = reactive({
    detailForm: {
    },
    rules: [],
    statusList: [],
    companyList: [],
    // 遮罩层
    loading: true,
    // 选中数组
    ids: [],
    // 非单个禁用
    single: true,
    // 非多个禁用
    multiple: true,
    // 显示搜索条件
    showSearch: true,
    // 总条数
    total: 0,
    // 图片压缩包处理日志表格数据
    logList: [],
    // 弹出层标题
    title: "",
    // 是否显示弹出层
    open: false,
    uploadZipId:null,
    // 查询参数
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      uploadZipId: null,
      zipName: null,
      picNum: null,
      successNum: null,
      createBeginTime: null,
      createEndTime: null,
      failNum: null,
      errorMsg: null,
      dealTime: null,
      dataType: null
    },
    // 表单参数
    form: {},
})

const btnAction = (type) => {
    emit('btnTap', type);
}

onMounted(() => {
    state.detailForm = props.projectObj;
    console.log(state.detailForm);
    state.uploadZipId=props.projectObj.id;
    getList(props.projectObj.id);

})
const getList = (id) => {
  state.loading = true;
  console.log("this.uploadZipId"+id)
  state.queryParams.uploadZipId=id;
  listLog(state.queryParams).then(response => {
    state.logList = response.rows;
    state.total = response.total;
    state.loading = false;
  });
}
</script>
    
<style lang="scss" scoped>
:deep(.el-input) {
    width: 250px;
}

.btn-section {
    text-align: right;
}
</style>