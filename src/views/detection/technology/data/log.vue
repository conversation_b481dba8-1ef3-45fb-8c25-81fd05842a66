<template>
  <div class="app-container">


    <el-table v-loading="loading" :data="logList" @selection-change="handleSelectionChange">
      <el-table-column label="图片压缩包上传名称" align="center" prop="zipName" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="图片压缩包总数" align="center" prop="picNum" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="图片压缩包成功数" align="center" prop="successNum" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="图片压缩包失败数" align="center" prop="failNum" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="图片压缩包失败原因" align="center" prop="errorMsg" width="150" show-tooltip-when-overflow="true"/>
      <el-table-column label="图片压缩包处理时间" align="center" prop="dealTime" width="150" show-tooltip-when-overflow="true" />
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改图片压缩包处理日志对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="图片压缩包上传表id" prop="uploadZipId">
          <el-input v-model="form.uploadZipId" placeholder="请输入图片压缩包上传表id" />
        </el-form-item>
        <el-form-item label="图片压缩包上传名称" prop="zipName">
          <el-input v-model="form.zipName" placeholder="请输入图片压缩包上传名称" />
        </el-form-item>
        <el-form-item label="图片压缩包总数" prop="picNum">
          <el-input v-model="form.picNum" placeholder="请输入图片压缩包总数" />
        </el-form-item>
        <el-form-item label="图片压缩包成功数" prop="successNum">
          <el-input v-model="form.successNum" placeholder="请输入图片压缩包成功数" />
        </el-form-item>
        <el-form-item label="图片压缩包失败数" prop="failNum">
          <el-input v-model="form.failNum" placeholder="请输入图片压缩包失败数" />
        </el-form-item>
        <el-form-item label="图片压缩包失败原因" prop="errorMsg">
          <el-input v-model="form.errorMsg" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="图片压缩包处理时间" prop="dealTime">
          <el-date-picker clearable
            v-model="form.dealTime"
            type="date"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            placeholder="请选择图片压缩包处理时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listLog, getLog, delLog, addLog, updateLog, } from "@/api/system/log";

export default {
  name: "Log",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 图片压缩包处理日志表格数据
      logList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      uploadZipId:null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        uploadZipId: null,
        zipName: null,
        picNum: null,
        createBeginTime: null,
        createEndTime: null,
        successNum: null,
        failNum: null,
        errorMsg: null,
        dealTime: null,
        dataType: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询图片压缩包处理日志列表 */
    getList(id) {
      this.loading = false;
      console.log("this.uploadZipId"+id)
      // listLog(this.queryParams).then(response => {
      //   this.logList = response.rows;
      //   this.total = response.total;
      //   this.loading = false;
      // });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        uploadZipId: null,
        zipName: null,
        picNum: null,
        successNum: null,
        failNum: null,
        errorMsg: null,
        dealTime: null,
        dataType: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.createEndTime=null;
      this.queryParams.createBeginTime=null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加图片压缩包处理日志";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getLog(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改图片压缩包处理日志";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateLog(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addLog(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除图片压缩包处理日志编号为"' + ids + '"的数据项？').then(function() {
        return delLog(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('project/log/export', {
        ...this.queryParams
      }, `log_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
