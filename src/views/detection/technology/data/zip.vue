<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="auto"
    >
      <el-form-item label="项目编码" prop="projectCode">
        <el-input
          v-model="queryParams.projectCode"
          placeholder="请输入项目编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-select
          v-model="queryParams.projectName"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="item in projects"
            :key="item.projectCode"
            :label="item.projectName"
            :value="item.projectName"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="隧道编码" prop="tunnelCode">
        <el-input
          v-model="queryParams.tunnelCode"
          placeholder="请输入隧道编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="隧道名称" prop="tunnelName">
        <el-select
          v-model="queryParams.tunnelName"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="item in tunnels"
            :key="item.tunnelCode"
            :label="item.tunnelName"
            :value="item.tunnelName"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="压缩包名称" prop="zipName" label-width="auto">
        <el-input
          v-model="queryParams.zipName"
          placeholder="请输入压缩包名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="上传时间" prop="onDate">
        <el-date-picker
          v-model="queryParams.onDate"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="~"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="mini" @click="handleQuery"
          >搜索</el-button
        >
        <el-button size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          @click="handleAdd"
          v-hasPermi="['project:zip:add']"
          >新增</el-button
        >
      </el-col>
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--          type="success"-->
      <!--          plain-->
      <!--          size="mini"-->
      <!--          :disabled="single"-->
      <!--          @click="handleUpdate"-->
      <!--          v-hasPermi="['project:zip:edit']"-->
      <!--        >修改</el-button>-->
      <!--      </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['project:zip:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          size="mini"
          @click="handleExport"
          v-hasPermi="['project:zip:export']"
          v-if="isExport"
          >导出</el-button
        >
        <el-button type="primary" v-if="isExportIng" :loading="true"
          >导出中</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="zipList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" fixed="left" label="序号" width="70" />
      <el-table-column
        v-for="(item, index) in tableConfig"
        :key="index"
        :prop="item.prop"
        :show-tooltip-when-overflow="true"
        :label="item.label"
        :min-width="item?.width || 120"
        :formatter="item.isFormatter ? formatStatus : null"
      >
        <template #default="scope">
          <a
            v-if="item.label === '上传压缩包名称'"
            style="color: blue"
            :href="scope.row.zipUrl"
            @click="handleBtnClick(scope.row)"
            >{{
              scope.row.zipUrl == null ? "文件上传中" : scope.row.zipName
            }}</a
          >
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        :resizable="false"
        align="center"
        width="200"
        label="操作"
      >
        <template #default="scope">
          <div class="operate-section">
            <!--            <el-button @click="handleUpdate(scope.row)" type="success" class="btn">编辑</el-button>-->
            <el-button
              v-if="scope.row.handleStatus === 0"
              hidden="hidden"
              type="primary"
              @click="associate(scope.row)"
              class="btn"
              >关联
            </el-button>
            <el-button @click="log(scope.row)" type="primary" class="btn"
              >操作日志</el-button
            >
            <el-button
              @click="handleDelete(scope.row)"
              type="danger"
              class="btn"
              >删除</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="queryParams.pageSize"
      :total="total"
      layout="->,total, sizes, prev, pager, next, jumper"
      style="margin-top: 10px; text-align: center"
    >
    </el-pagination>

    <!-- 添加或修改图片压缩包上传管理对话框 -->
    <el-dialog :title="title" v-model="open" width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="项目名称" prop="projectCode">
          <el-select
            v-model="form.projectCode"
            placeholder="请选择"
            clearable
            :disabled="selectDisabled"
            filterable
          >
            <el-option
              v-for="item in projects"
              :key="item.projectCode"
              :label="item.projectName"
              :value="item.projectCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="隧道名称" prop="tunnelCode">
          <el-select
            v-model="form.tunnelCode"
            placeholder="请选择"
            clearable
            :disabled="selectDisabled"
            filterable
          >
            <el-option
              v-for="item in tunnels"
              :key="item.tunnelCode"
              :label="item.tunnelNameAndHole"
              :value="item.tunnelCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="压缩包" prop="zipName">
          <el-upload
            ref="upload"
            action=""
            v-model="form.zipName"
            drag
            :limit="1"
            :on-exceed="uploadExceed"
            :auto-upload="false"
            :multiple="false"
            :on-change="uploadFile"
            :before-upload="beforeUpload"
            :on-remove="handleRemove"
            accept=".zip,.rar"
            :file-list="fileList"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <div class="el-upload__tip" slot="tip">
              只能上传zip/rar文件，且不超过500M，一次只能上传一个
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="syncLoading"
          >确 定</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog v-model="logOpen" :title="logTitle" width="60%" v-if="logOpen">
      <logDetail
        @btnTap="modifyBtnTapHandler"
        :projectObj="currentRow"
      ></logDetail>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZip,
  getZip,
  delZip,
  addZip,
  updateZip,
  associateZip,
  getProjects,
  getTunnels,
  exportData,
} from "@/api/system/zip";
import logDetail from "./logDetail.vue";
import { reactive, ref } from "vue";
import axios from "axios";
import { getToken } from "@/utils/auth";

const modifyBtnTapHandler = (type) => {
  switch (type) {
    case "cancel":
      state.modifyModal = false;
      break;
    case "sure":
      break;
  }
};
export default {
  name: "Zip",
  components: { logDetail },
  data() {
    return {
      syncLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      id: null,
      isUpdateZipFile: false,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      fileList: [],
      uploadZipId: null,
      currentRow: {},
      selectDisabled: false,
      // 图片压缩包上传管理表格数据
      zipList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      logTitle: "",
      logOpen: false,
      isExport: true,
      isExportIng: false,
      token: null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectTunnelInspectionId: null,
        tunnelName: null,
        tunnelCode: null,
        zipName: null,
        zipUrl: null,
        thumbnailZipName: null,
        thumbnailZpUrl: null,
        dataType: null,
        handleStatus: null,
        creatorCode: null,
        createBeginTime: null,
        createEndTime: null,
        creatorName: null,
        creatorTime: null,
        modifierCode: null,
        modifierName: null,
        modifierTime: null,
        isAvaliable: null,
        isDeleted: null,
        onDate: [],
        offDate: [],
      },
      projects: [
        // }
      ],
      tunnels: [],
      // 表单参数
      form: {
        date1: [],
      },
      // 表单校验
      rules: {
        tunnelCode: [
          { required: true, message: "隧道名称不能为空", trigger: "blur" },
        ],
        projectCode: [
          { required: true, message: "项目名称不能为空", trigger: "blur" },
        ],
        zipName: [
          { required: true, message: "压缩包必须上传", trigger: "blur" },
        ],
        pileNumber: [
          { required: true, message: "桩号不能为空", trigger: "blur" },
        ],
        conditionValue: [
          { pattern: /^[0-4]?$/, message: "只能输0-4的整数", trigger: "blur" },
          { required: true, message: "状况值不能为空", trigger: "blur" },
        ],
      },
      tableConfig: [
        {
          label: "项目名称",
          prop: "projectName",
          width: "180",
        },
        {
          label: "所属路段",
          prop: "affiliatedRoadSection",
        },
        {
          label: "隧道名称",
          prop: "tunnelName",
        },
        {
          label: "上传压缩包名称",
          prop: "--",
          width: "240",
        },
        {
          label: "关联状态",
          prop: "handleStatus",
          isFormatter: true
        },
      ],
    };
  },
  created() {
    this.getList();
    this.getProject();
    this.getTunnel();
    this.token = getToken();
  },
  methods: {
    /** 查询图片压缩包上传管理列表 */
    getList() {
      this.loading = true;
      console.log(this.queryParams);
      if (
        this.queryParams.onDate != null &&
        this.queryParams.onDate.length != 0
      ) {
        this.queryParams.createBeginTime = this.queryParams.onDate[0];
        this.queryParams.createEndTime = this.queryParams.onDate[1];
      }
      this.queryParams.dataType = 1;
      listZip(this.queryParams).then((response) => {
        this.zipList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 关联 */
    associate(row) {
      associateZip(row.id).then((response) => {
        this.$modal.msgSuccess("关联成功");
        this.getList();
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectTunnelInspectionId: null,
        tunnelName: null,
        tunnelCode: null,
        zipName: null,
        zipUrl: null,
        thumbnailZipName: null,
        thumbnailZpUrl: null,
        dataType: null,
        handleStatus: 0,
        creatorCode: null,
        creatorName: null,
        creatorTime: null,
        modifierCode: null,
        modifierName: null,
        modifierTime: null,
        isAvaliable: null,
        isDeleted: null,
      };
      (this.fileList = []), this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.createEndTime = null;
      this.queryParams.createBeginTime = null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.selectDisabled = false;
      this.title = "添加";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.selectDisabled = true;
      const id = row.id || this.ids;
      this.id = row.id;
      getZip(id).then((response) => {
        this.form = response.data;
        this.fileList.push({
          name: this.form.zipName,
          url: this.form.zipUrl,
        });
        this.open = true;
        this.title = "修改";
      });
    },
    /** 提交按钮 */
    submitForm() {
      let _this = this;
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.projects.forEach((item, index, arr) => {
            if (item.projectCode == this.form.projectCode) {
              this.form.projectName = item.projectName;
            }
          });
          console.log(this.tunnels);
          console.log(this.form.tunnelCode);
          this.tunnels.forEach((item, index, arr) => {
            if (item.tunnelCode == this.form.tunnelCode) {
              this.form.tunnelName = item.tunnelName;
            }
          });
          let formData = new FormData();
          if (this.isUpdateZipFile) {
            formData.append("files", this.form.files);
            formData.append("zipName", this.form.files.name);
          }
          formData.append("thumbnailZipName", "");
          formData.append("dataType", 1);
          formData.append("handleStatus", 0);
          formData.append("tunnelCode", this.form.tunnelCode);
          formData.append("tunnelName", this.form.tunnelName);
          formData.append("projectCode", this.form.projectCode);
          formData.append("projectName", this.form.projectName);
          formData.append("isUpdateZipFile", this.isUpdateZipFile);
          console.log("formData");
          console.log(formData);
          _this.syncLoading = true;
          if (this.form.id != null) {
            formData.append("id", this.form.id);
            updateZip(formData)
              .then((response) => {
                _this.syncLoading = false;
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              })
              .catch(() => {
                _this.syncLoading = false;
              });
          } else {
            addZip(formData)
              .then((response) => {
                _this.syncLoading = false;
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              })
              .catch(() => {
                _this.syncLoading = false;
              });
          }
        }
      });
    },
    uploadFile(param) {
      this.form.files = param.raw;
      this.form.zipName = param.raw.name;
      this.isUpdateZipFile = true;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZip(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.zipList.length == 0) {
        this.$alert("无导出数据", "", {
          confirmButtonText: "确定",
        });
      } else {
        //开启loading
        this.isExport = false;
        this.isExportIng = true;
        if (
          this.queryParams.onDate != null &&
          this.queryParams.onDate.length != 0
        ) {
          this.queryParams.createBeginTime = this.queryParams.onDate[0];
          this.queryParams.createEndTime = this.queryParams.onDate[1];
        }
        this.queryParams.dataType = 1;
        exportData(this.queryParams)
          .then((res) => {
            if (res.code == "500") {
              this.$modal.msgSuccess("导出失败，请联系开发人员");
              return;
            } else {
              this.$modal.msgSuccess("导出成功");
            }
            let blob = new Blob([res], { type: "application/force-download" });
            console.log(blob);
            let fileReader = new FileReader();
            fileReader.readAsDataURL(blob);
            fileReader.onload = (e) => {
              let a = document.createElement("a");
              let date = new Date();
              let year = date.getFullYear();
              let month = date.getMonth() + 1;
              let day = date.getDate();
              a.download = `导出数据${year + "_" + month + "_" + day}.xlsx`;
              a.href = e.target.result;
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
            };
            this.isExport = true;
            this.isExportIng = false;
          })
          .catch(() => {
            this.isExport = true;
            this.isExportIng = false;
          });
      }
    },
    sizeChangeHandle(val) {
      this.queryParams.pageSize = val;
      this.queryParams.pageNum = 1;
      this.getList();
    },
    currentChangeHandle: function currentChangeHandle(val) {
      this.queryParams.pageNum = val;
      this.getList();
    },
    //关联状态
    formatStatus(row, column, cellValue, index) {
      if (cellValue === 0) {
        return "未关联";
      } else if (cellValue === 1) {
        return "关联中";
      } else if (cellValue === 2) {
        return "已关联";
      } else if (cellValue === 3) {
        return "关联失败";
      }
      return cellValue;
    },
    // 文件上传前对文件类型、文件大小判断限制
    beforeUpload(file) {
      const { name, size } = file;
      const index = name.lastIndexOf(".");
      // 判断文件名是否有后缀，没后缀文件错误
      if (index === -1) {
        this.$notify.error({
          title: "错误",
          message: "文件错误，请重新上传！",
        });
        return false;
      }
      const fileType = name.substr(index + 1);
      const acceptFileTypes = ["zip", "rar"];
      // 判断文件类型
      if (!acceptFileTypes.includes(fileType)) {
        this.$notify.error({
          title: "错误",
          message: "文件类型错误，请重新上传！",
        });
        return false;
      }
      // 判断文件大小
      if (size > 500 * 1024 * 1024) {
        this.$notify.error({
          title: "错误",
          message: "文件大小超过500M，请重新上传！",
        });
        return false;
      }
      // 默认true
      return true;
    },

    // 上传接口调取成功status为200
    uploadSuccess(res) {
      if (res.code === 200) {
        // 文件上传成功
        this.$notify.success({
          title: "成功",
          message: "文件上传成功！",
        });
      } else {
        this.uploadError();
      }
    },

    // 文件上传失败
    uploadError() {
      this.$notify.error({
        title: "错误",
        message: "文件上传失败！",
      });
    },
    handleRemove() {
      this.form.files = null;
      this.form.zipName = null;
      this.fileList = [];
      if (this.id != null) {
        this.isUpdateZipFile = true;
      }
    },

    // 文件个数超过限制
    uploadExceed() {
      this.$notify.warning({
        title: "提示",
        message: "您已添加了一个文件，如需替换，请先删除已添加的文件！",
      });
    },
    handleDownload(row) {
      location.href = row.zipUrl;
    },
    /** 操作日志 */
    log(row) {
      console.log(row);
      this.currentRow = row;
      this.logOpen = true;
      this.logTitle = "操作日志";
      console.log("111111");
    },
    getProject() {
      getProjects().then((response) => {
        this.projects = response.data;
      });
    },
    getTunnel() {
      getTunnels().then((response) => {
        this.tunnels = response.data;
        this.tunnels.forEach(function (item, index, array) {
          item.tunnelNameAndHole = item.tunnelName + "-" + item.hole;
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.operate-section {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
}
</style>
