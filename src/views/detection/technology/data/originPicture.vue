<template>
    <div class="project-detail-container">
      <el-table v-loading="state.loading" :data="state.pictureList">
        <el-table-column align="center">
          <template #default="scope">
            <img :src="scope.row.layoutUrl" width="500" height="500" />
            <p>{{ scope.row.layoutName }}</p>
  　　    </template>
        </el-table-column>
      </el-table>
    </div>
</template>

<script setup name="pictureDetail">
import { ref, reactive, onMounted } from 'vue';
const emit = defineEmits(['btnTap']);
const props = defineProps({
    projectObj: {
        type: Object,
        default: () => {}
    }
});
const state = reactive({
    detailForm: {
    },
    rules: [],
    statusList: [],
    companyList: [],
    // 遮罩层
    loading: true,
    // 选中数组
    ids: [],
    // 非单个禁用
    single: true,
    // 非多个禁用
    multiple: true,
    originTitle:"展布原图",
    // 显示搜索条件
    showSearch: true,
    // 总条数
    total: 0,
    currentRow:{},
    pictureList: [],
    // 弹出层标题
    title: "",
    // 是否显示弹出层
    open: false,
    uploadZipId:null,
    // 查询参数
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      uploadZipId: null,
      zipName: null,
      picNum: null,
      createBeginTime: null,
      createEndTime: null,
      successNum: null,
      failNum: null,
      errorMsg: null,
      dealTime: null,
      dataType: null
    },
    // 表单参数
    form: {},
})

const btnAction = (type) => {
    emit('btnTap', type);
}

onMounted(() => {
    state.detailForm = props.projectObj;
    console.log(state.detailForm.layoutUrl);
    state.uploadZipId=props.projectObj.id;
    state.pictureList = [{
      "layoutUrl":state.detailForm.layoutUrl,
      "layoutName":state.detailForm.layoutName
    }]
  getList(props.projectObj.id);

})
const getList = (id) => {
  state.loading = false;
  console.log("this.uploadZipId"+id)
}

</script>
    
<style lang="scss" scoped>
:deep(.el-input) {
    width: 250px;
}

.btn-section {
    text-align: right;
}
</style>