<template>
    <div class="project-detail-container">
      <el-table v-loading="state.loading" :data="state.pictureList" :span-method="objectSpanMethod" border :header-cell-style="handerMethod">
<!--        <el-table-column align="center"  label="隧道情况"></el-table-column>-->
<!--        <el-table-column align="center" label="隧道名称"></el-table-column>-->
<!--        <el-table-column align="center" label="桃花潭隧道"></el-table-column>-->
<!--        <el-table-column align="center" prop="amount2" label="缴存业务"></el-table-column>-->
<!--        <el-table-column align="center" prop="amount3" label="提取业务"></el-table-column>-->
<!--        <el-table-column align="center" prop="amount4" label="贷款业务"></el-table-column>-->
        <el-table-column align="center" label="隧道情况">
          <el-table-column align="center" label="隧道名称" prop="tunnelName" width="150" show-tooltip-when-overflow="true"></el-table-column>
          <el-table-column align="center" label="路线名称" prop="affiliatedRoadSection" width="150" show-tooltip-when-overflow="true"></el-table-column>
          <el-table-column align="center" label="隧道长度(m)" prop="tunnelLength" width="150" show-tooltip-when-overflow="true"></el-table-column>
          <el-table-column align="center" label="建成时间" prop="tunnelCompletionTime" width="150" show-tooltip-when-overflow="true"></el-table-column>
        </el-table-column>
        <el-table-column align="center" label="评定情况">
          <el-table-column align="center" label="营养等级" prop="level" width="150" show-tooltip-when-overflow="true"></el-table-column>
<!--          <el-table-column align="center" label="上次评定等级" prop="lastLevel" width="150" show-tooltip-when-overflow="true"></el-table-column>-->
          <el-table-column align="center" label="评分" prop="civilStructureScore" width="150" show-tooltip-when-overflow="true"></el-table-column>
          <el-table-column align="center" label="土建评定等级" prop="civilStructureLevel" width="150" show-tooltip-when-overflow="true"></el-table-column>

<!--          <el-table-column align="center" label="上次评定日期" prop="lastDate" width="180" show-tooltip-when-overflow="true"></el-table-column>-->
          <el-table-column align="center" label="本次评定日期" prop="scoreTime" width="180" show-tooltip-when-overflow="true"></el-table-column>
        </el-table-column>
      </el-table>

    <div class="project-detail-container"  style="margin-top: 10px;">
      <el-form  :model="state.scoreDeatilForm" ref="state.scoreDeatilForm" label-width="auto"  >
          <el-row >
              <el-col :span="7">
                  <el-form-item label="洞口:" prop="openingConditionValue" >
                      <el-input v-model="state.scoreDeatilForm.openingConditionValue" :disabled="true"/>
                  </el-form-item>
              </el-col>
              <el-col :span="7">
                  <el-form-item label="洞门:" prop="portalConditionValue" >
                      <el-input v-model="state.scoreDeatilForm.portalConditionValue" :disabled="true" />
                  </el-form-item>
              </el-col>
              <el-col :span="7">
                  <el-form-item label="衬砌渗漏水:" prop="liningsSlsConditionValue" >
                      <el-input v-model="state.scoreDeatilForm.liningsSlsConditionValue" :disabled="true" />
                  </el-form-item>
              </el-col>
          </el-row>
          <el-row>
              <el-col :span="7">
                  <el-form-item label="衬砌破损:" prop="liningsPsConditionValue" >
                      <el-input v-model="state.scoreDeatilForm.liningsPsConditionValue" :disabled="true" />
                  </el-form-item>
              </el-col>
              <el-col :span="7">
                  <el-form-item label="路面:" prop="roadConditionValue" >
                      <el-input v-model="state.scoreDeatilForm.roadConditionValue" :disabled="true" />
                  </el-form-item>
              </el-col>
              <el-col :span="7">
                  <el-form-item label="检修道:" prop="manholeConditionValue" >
                      <el-input v-model="state.scoreDeatilForm.manholeConditionValue" :disabled="true" />
                  </el-form-item>
              </el-col>
          </el-row>
          <el-row>
              <el-col :span="7">
                  <el-form-item label="排水系统:" prop="drainageConditionValue" >
                      <el-input v-model="state.scoreDeatilForm.drainageConditionValue" :disabled="true" />
                  </el-form-item>
              </el-col>
              <el-col :span="7">
                  <el-form-item label="吊顶预埋件:" prop="ceilingConditionValue" >
                      <el-input v-model="state.scoreDeatilForm.ceilingConditionValue" :disabled="true" />
                  </el-form-item>
              </el-col>
              <el-col :span="7">
                  <el-form-item label="内装饰:" prop="manholeInteriorConditionValue" >
                      <el-input v-model="state.scoreDeatilForm.manholeInteriorConditionValue" :disabled="true" />
                  </el-form-item>
              </el-col>
          </el-row>
          <el-col :span="7">
              <el-form-item label="标线轮廓线:" prop="outlineConditionValue" >
                  <el-input v-model="state.scoreDeatilForm.outlineConditionValue" :disabled="true" />
              </el-form-item>
          </el-col>
      </el-form>
  </div>
    </div>
</template>
    
<script setup name="reportDetail">
import { ref, reactive, onMounted } from 'vue';
import { getDetailReport,getDetailScoreValue } from "@/api/system/report";
import originPicture from "./originPicture.vue";
const emit = defineEmits(['btnTap']);
const props = defineProps({
    projectObj: {
        type: Object,
        default: () => {}
    }
});
const state = reactive({
    detailForm: {
        startDate: '',
        endDate: ''
    },
    rules: [],
    statusList: [],
    companyList: [],
    // 遮罩层
    loading: true,
    // 选中数组
    ids: [],
    // 非单个禁用
    single: true,
    // 非多个禁用
    multiple: true,
    originOpen:false,
    relateScoreDeatil:true,
    originTitle:"展布原图",
    // 显示搜索条件
    showSearch: true,
    // 总条数
    total: 0,
    currentRow:{},
    pictureList: [
    //     {
    //   "id":1,
    //   "level":"好",
    //   "lastDate":"2023-08-05",
    //   "thisDate":"2023-08-05",
    //   "tunnelName":"桃花潭隧道",
    //   "lastLevel":"一般",
    //   "tunnelLong":"100m",
    //   "roadName":"左洞",
    //   "buildDate":"2023-08-01",
    //   "civilLevel":"2类",
    //   "score":"82.72",
    // }
    ],
    // 弹出层标题
    title: "",
    // 是否显示弹出层
    open: false,
    // 查询参数
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      zipName: null,
      picNum: null,
      successNum: null,
      failNum: null,
      errorMsg: null,
      dealTime: null,
      dataType: null
    },
    // 表单参数
    form: {},
    scoreDeatilForm: {},
})

const btnAction = (type) => {
    emit('btnTap', type);
}

onMounted(() => {
    state.detailForm = props.projectObj;
    console.log(state.detailForm);
    getList(props.projectObj.id);
    getDetailScoreCondtionValue(props.projectObj.id);

})
const getList = (id) => {
  state.loading = false;
  console.log("this.id"+id)
  getDetailReport(id).then(response => {
    state.pictureList.push(response.data);
    this.loading = false;
  });
}

const getDetailScoreCondtionValue = (id) => {
    state.loading = false;
    console.log("this.id"+id)
    getDetailScoreValue(id).then(response => {
        state.scoreDeatilForm.openingConditionValue=response.openingConditionValue;
        state.scoreDeatilForm.portalConditionValue=response.portalConditionValue;
        state.scoreDeatilForm.liningsSlsConditionValue=response.liningsSlsConditionValue;
        state.scoreDeatilForm.liningsPsConditionValue=response.liningsPsConditionValue;
        state.scoreDeatilForm.roadConditionValue=response.roadConditionValue;
        state.scoreDeatilForm.manholeConditionValue=response.manholeConditionValue;
        state.scoreDeatilForm.drainageConditionValue=response.drainageConditionValue;
        state.scoreDeatilForm.ceilingConditionValue=response.ceilingConditionValue;
        state.scoreDeatilForm.manholeInteriorConditionValue=response.manholeInteriorConditionValue;
        state.scoreDeatilForm.outlineConditionValue=response.outlineConditionValue;


        this.loading = false;
    });
}

const turnOrigin = (row) => {
  console.log(row)
  state.currentRow=row;
  state.originOpen=true;
}
const objectSpanMethod = ( row, column, rowIndex, columnIndex ) =>{
  // 对第六、七行 进行合并
  if (rowIndex === 4) {
    if (columnIndex === 0) {
      return [2, 2]
    } else if (columnIndex === 1) {
      return [0, 0]
    }
  }
  // 对第一列 第二列 进行合并
  if (columnIndex === 1 || columnIndex === 0) {
    // 当 当前行与上一行内容相同时 返回0 0 意味消除
    if (rowIndex > 0 && row[column.property] === this.tableData[rowIndex - 1][column.property]) {
      return {
        rowspan: 0,
        colspan: 0
      }
    } else {
      let rows = 1
      // 反之 查询相同的内容有多少行 进行合并
      for (let i = rowIndex; i < this.tableData.length - 1; i++) {
        if (row[column.property] === this.tableData[i + 1][column.property]) {
          rows++
        }
      }
      // 返回相同内容的行数
      return {
        rowspan: rows,
        colspan: 1
      }
    }
  }
  // 对第一、二、三、四、五行 进行合并
  if (rowIndex === 0 || rowIndex === 1 || rowIndex === 2 || rowIndex === 3 || rowIndex === 4) {
    // 处理合计，[1,2]表示合并1行2列，[0,0]表示改行不显示
    if (columnIndex === 5) {
      // 定位到5列的一、二、三、四、五行，告诉该单元格合并1行2列
      return [1, 2]
    } else if (columnIndex === 6) {
      // 定位到6列的一、二、三、四、五行，告诉该单元格不显示
      return [0, 0]
    }
  }
}
</script>
    
<style lang="scss" scoped>
:deep(.el-input) {
    width: 250px;
}

.btn-section {
    text-align: right;
}
</style>