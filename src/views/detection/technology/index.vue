<template>
    <div class="app-container">
        <Navbar @navTap="navTapHandler" :title-arr="state.navList"></Navbar>
        <div class="list-section">
            <template v-if="state.currentTab == 0">
                <CheckData></CheckData>
            </template>
            <template v-else-if="state.currentTab == 1">
                <CheckImport></CheckImport>
            </template>
            <template v-else-if="state.currentTab == 2">
                <CheckRank></CheckRank>
            </template>
            <template v-else>
                <CheckAnalyse></CheckAnalyse>
            </template>
        </div>
    </div>
</template>
    
<script setup name="Technology">
import { reactive, ref } from 'vue';
import Navbar from './components/technologyNavbar.vue';
import CheckData from './components/checkData.vue';
import CheckRank from './components/checkRank.vue';
import CheckAnalyse from './components/checkAnalyse.vue';
import CheckImport from './components/checkImport.vue';

const state = reactive({
    navList: [
        {
            title: '定期检查内容',
            select: true
        },  {
            title: '隧道检测情况录入',
            select: false
        },  {
            title: '技术状况评分',
            select: false
        },  {
            title: '检测数据统计分析',
            select: false
        }
    ],
    currentTab: 0
})

const navTapHandler = (index) => {
    state.currentTab = index;
    let list = state.navList;
    list.forEach((item, indexPath) => {
        item.select = false;
        if (indexPath == index) {
            item.select = true;
        }
    })
}

</script>
    
<style lang="scss" scoped>
.list-section {
    width: 100%;
    padding-top: 20px;
}
</style>