<template>
    <div class="app-container">
        <div class="search-section">
            <el-form :model="searchParams" ref="queryRef" :inline="true">
                <el-form-item label="隧道名称" prop="name">
                    <el-input v-model="searchParams.name" placeholder="请输入隧道名称" clearable />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="Search" @click="searchAction">搜索</el-button>
                    <el-button icon="Refresh" @click="resetAction">重置</el-button>
                    <el-button icon="Plus" @click="addProjectAction" v-hasPermi="['project:info:add']">新增</el-button>
                    <el-button icon="Plus" @click="batchProjectAction" v-hasPermi="['project:info:add']">批量新增</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="table-section">
            <el-table :data="state.evaluateList" style="width: 100%">
                <el-table-column prop="name" label="隧道名称" />
                <el-table-column prop="checkTime" label="检测时间" />
                <el-table-column prop="evaluateProgress" label="检测报告生成">
                    <template #default="scope">
                        <div class="operate-section">
                            <el-button @click="btnAction('detail', scope.row)" type="primary" class="btn">点击生成</el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="evaluateTime" label="操作">
                    <template #default="scope">
                        <div class="operate-section">
                            <el-button @click="btnAction('detail', scope.row)" type="primary" class="btn">查看</el-button>
                            <el-button @click="btnAction('detail', scope.row)" type="primary" class="btn">导出</el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="evaluateTime" label="历史检测报告管理" width="300px">
                    <template #default="scope">
                        <div class="operate-section">
                            <el-button @click="btnAction('detail', scope.row)" class="btn">时间选择</el-button>
                            <el-button @click="btnAction('detail', scope.row)" type="primary" class="btn">查看</el-button>
                            <el-button @click="btnAction('detail', scope.row)" type="primary" class="btn">导出</el-button>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="operatePerson" label="操作人" />
                <el-table-column prop="operateTime" label="操作时间" />
            </el-table>
        </div>
        <div class="page-section">
            <el-pagination background layout="prev, pager, next" :total="1000" />
        </div>
    </div>
</template>
    
<script setup name="Report">
import { reactive, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();

const searchParams = reactive({
    name: '',
    code: '',
    company: '',
    status: ''
});
const state = reactive({
    batchModal: false,
    relateModal: false,
    detailModal: false,
    addModal: false,
    modifyModal: false,
    statusList: [
        {
            label: '施工中',
            value: 'sgz'
        }, {
            label: '未开始',
            value: 'wks'
        }, {
            label: '已竣工',
            value: 'yjg'
        }, {
            label: '停工',
            value: 'tg'
        }
    ],
    companyList: [
        {
            label: '中国交建',
            value: 'zgjj'
        }, {
            label: '中交三局',
            value: 'zjsj'
        }, {
            label: '中铁三局',
            value: 'ztsj'
        }
    ],
    currentProject: {},
    evaluateList: [
        {
            name: '中建五局武汉市黄家湖隧道工程',
            checkProgress: '已完成检测',
            checkTime: '2023年4月10日',
            evaluateProgress: '评定完成',
            evaluateTime: '2023年5月20日',
            operatePerson: '张三',
            operateTime: '2023年4月30日'
        }, {
            name: '中建五局武汉市黄家湖隧道工程',
            checkProgress: '已完成检测',
            checkTime: '2023年4月10日',
            evaluateProgress: '评定完成',
            evaluateTime: '2023年5月20日',
            operatePerson: '张三',
            operateTime: '2023年4月30日'
        }, {
            name: '中建五局武汉市黄家湖隧道工程',
            checkProgress: '已完成检测',
            checkTime: '2023年4月10日',
            evaluateProgress: '评定完成',
            evaluateTime: '2023年5月20日',
            operatePerson: '张三',
            operateTime: '2023年4月30日'
        }, {
            name: '中建五局武汉市黄家湖隧道工程',
            checkProgress: '已完成检测',
            checkTime: '2023年4月10日',
            evaluateProgress: '评定完成',
            evaluateTime: '2023年5月20日',
            operatePerson: '张三',
            operateTime: '2023年4月30日'
        }, {
            name: '中建五局武汉市黄家湖隧道工程',
            checkProgress: '已完成检测',
            checkTime: '2023年4月10日',
            evaluateProgress: '评定完成',
            evaluateTime: '2023年5月20日',
            operatePerson: '张三',
            operateTime: '2023年4月30日'
        }
    ]
})

const searchAction = () => {

};
const resetAction = () => {

};

const btnTapHandler = (type) => {
    switch (type) {
        case 'cancel':
            state.addModal = false;
            break;
        case 'sure':

            break;
    }
}

const relateBtnTapHandler = (type) => {
    switch (type) {
        case 'cancel':
            state.relateModal = false;
            break;
        case 'sure':

            break;
    }
}

const modifyBtnTapHandler = (type) => {
    switch (type) {
        case 'cancel':
            state.modifyModal = false;
            break;
        case 'sure':

            break;
    }
}

const addProjectAction = () => {
    state.addModal = true;
}

const batchProjectAction = () => {
    state.batchModal = true;
}

const btnAction = (type, project) => {
    state.currentProject = project;
    switch (type) {
        case 'detail':
            state.detailModal = true;
            break;
        case 'relate':
            // state.relateModal = true;
            router.push('projectTunnel');
            break;
        case 'modify':
            state.modifyModal = true;
            break;
        case 'delete':
            ElMessageBox.confirm(
                '确定要删除该项目信息?',
                '温馨提示',
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }
            ).then(() => {
            }).catch(() => {
            })
            break;
    }
}
</script>
    
<style lang="scss" scoped>
.app-container {
    height: 100%;
    padding: 0 20px;
    .search-section {
        padding: 20px 0;
    }
    .page-section {
        padding-top: 20px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }
}
</style>