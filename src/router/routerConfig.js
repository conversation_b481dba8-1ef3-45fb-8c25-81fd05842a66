const routerData = {
    "msg": "操作成功",
    "code": 200,
    "data": [
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-06-29 21:30:44",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2153,
            "menuName": "基础数据",
            "parentName": null,
            "parentId": 0,
            "orderNum": 1,
            "path": "basic",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "M",
            "visible": "0",
            "status": "0",
            "perms": "",
            "icon": "cascader",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-02-02 20:10:50",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2063,
            "menuName": "隧道档案",
            "parentName": null,
            "parentId": 0,
            "orderNum": 8,
            "path": "project",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "M",
            "visible": "0",
            "status": "1",
            "perms": "",
            "icon": "build",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-08-28 16:15:42",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2096,
            "menuName": "检测评定",
            "parentName": null,
            "parentId": 0,
            "orderNum": 9,
            "path": "detection",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "M",
            "visible": "0",
            "status": "0",
            "perms": "",
            "icon": "example",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-08-28 16:05:18",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2079,
            "menuName": "实景三维",
            "parentName": null,
            "parentId": 0,
            "orderNum": 10,
            "path": "tunnel",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "M",
            "visible": "0",
            "status": "0",
            "perms": "",
            "icon": "cascader",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2025-06-18 17:51:34",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2359,
            "menuName": "项目交付",
            "parentName": null,
            "parentId": 0,
            "orderNum": 11,
            "path": "project-delivery",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "M",
            "visible": "0",
            "status": "0",
            "perms": "",
            "icon": "clipboard",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-04-15 21:25:15",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2131,
            "menuName": "养护管理",
            "parentName": null,
            "parentId": 0,
            "orderNum": 11,
            "path": "maintenance",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "M",
            "visible": "0",
            "status": "0",
            "perms": "",
            "icon": "edit",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1,
            "menuName": "系统管理",
            "parentName": null,
            "parentId": 0,
            "orderNum": 12,
            "path": "system",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "M",
            "visible": "0",
            "status": "0",
            "perms": "",
            "icon": "system",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2,
            "menuName": "系统监控",
            "parentName": null,
            "parentId": 0,
            "orderNum": 13,
            "path": "monitor",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "M",
            "visible": "1",
            "status": "1",
            "perms": "",
            "icon": "monitor",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 3,
            "menuName": "系统工具",
            "parentName": null,
            "parentId": 0,
            "orderNum": 14,
            "path": "tool",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "M",
            "visible": "0",
            "status": "0",
            "perms": "",
            "icon": "tool",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 100,
            "menuName": "用户管理",
            "parentName": null,
            "parentId": 1,
            "orderNum": 1,
            "path": "user",
            "component": "system/user/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "system:user:list",
            "icon": "user",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2020-10-15 20:09:02",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2000,
            "menuName": "测试管理",
            "parentName": null,
            "parentId": 1,
            "orderNum": 1,
            "path": "test",
            "component": "test/user/list",
            "query": null,
            "isFrame": "1",
            "isCache": "1",
            "menuType": "C",
            "visible": "1",
            "status": "1",
            "perms": "test:user:list",
            "icon": "checkbox",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 101,
            "menuName": "角色管理",
            "parentName": null,
            "parentId": 1,
            "orderNum": 2,
            "path": "role",
            "component": "system/role/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "system:role:list",
            "icon": "peoples",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 102,
            "menuName": "菜单管理",
            "parentName": null,
            "parentId": 1,
            "orderNum": 3,
            "path": "menu",
            "component": "system/menu/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "system:menu:list",
            "icon": "tree-table",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 103,
            "menuName": "公司管理",
            "parentName": null,
            "parentId": 1,
            "orderNum": 4,
            "path": "dept",
            "component": "system/dept/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "system:dept:list",
            "icon": "tree",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 104,
            "menuName": "岗位管理",
            "parentName": null,
            "parentId": 1,
            "orderNum": 5,
            "path": "post",
            "component": "system/post/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "system:post:list",
            "icon": "post",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 105,
            "menuName": "字典管理",
            "parentName": null,
            "parentId": 1,
            "orderNum": 6,
            "path": "dict",
            "component": "system/dict/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "system:dict:list",
            "icon": "dict",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 106,
            "menuName": "参数设置",
            "parentName": null,
            "parentId": 1,
            "orderNum": 7,
            "path": "config",
            "component": "system/config/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "system:config:list",
            "icon": "edit",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 107,
            "menuName": "通知公告",
            "parentName": null,
            "parentId": 1,
            "orderNum": 8,
            "path": "notice",
            "component": "system/notice/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "1",
            "status": "0",
            "perms": "system:notice:list",
            "icon": "message",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 108,
            "menuName": "日志管理",
            "parentName": null,
            "parentId": 1,
            "orderNum": 9,
            "path": "log",
            "component": "system/log/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "M",
            "visible": "0",
            "status": "0",
            "perms": "",
            "icon": "log",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 109,
            "menuName": "在线用户",
            "parentName": null,
            "parentId": 2,
            "orderNum": 1,
            "path": "online",
            "component": "monitor/online/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "monitor:online:list",
            "icon": "online",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 110,
            "menuName": "定时任务",
            "parentName": null,
            "parentId": 2,
            "orderNum": 2,
            "path": "job",
            "component": "monitor/job/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "monitor:job:list",
            "icon": "job",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 111,
            "menuName": "数据监控",
            "parentName": null,
            "parentId": 2,
            "orderNum": 3,
            "path": "druid",
            "component": "monitor/druid/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "monitor:druid:list",
            "icon": "druid",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 112,
            "menuName": "服务监控",
            "parentName": null,
            "parentId": 2,
            "orderNum": 4,
            "path": "server",
            "component": "monitor/server/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "monitor:server:list",
            "icon": "server",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 113,
            "menuName": "表单构建",
            "parentName": null,
            "parentId": 3,
            "orderNum": 1,
            "path": "build",
            "component": "tool/build/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "tool:build:list",
            "icon": "build",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 114,
            "menuName": "代码生成",
            "parentName": null,
            "parentId": 3,
            "orderNum": 2,
            "path": "gen",
            "component": "tool/gen/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "tool:gen:list",
            "icon": "code",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 115,
            "menuName": "系统接口",
            "parentName": null,
            "parentId": 3,
            "orderNum": 3,
            "path": "swagger",
            "component": "tool/swagger/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "tool:swagger:list",
            "icon": "swagger",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1001,
            "menuName": "用户查询",
            "parentName": null,
            "parentId": 100,
            "orderNum": 1,
            "path": "",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:user:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1002,
            "menuName": "用户新增",
            "parentName": null,
            "parentId": 100,
            "orderNum": 2,
            "path": "",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:user:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1003,
            "menuName": "用户修改",
            "parentName": null,
            "parentId": 100,
            "orderNum": 3,
            "path": "",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:user:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1004,
            "menuName": "用户删除",
            "parentName": null,
            "parentId": 100,
            "orderNum": 4,
            "path": "",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:user:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1005,
            "menuName": "用户导出",
            "parentName": null,
            "parentId": 100,
            "orderNum": 5,
            "path": "",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:user:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1006,
            "menuName": "用户导入",
            "parentName": null,
            "parentId": 100,
            "orderNum": 6,
            "path": "",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:user:import",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1007,
            "menuName": "重置密码",
            "parentName": null,
            "parentId": 100,
            "orderNum": 7,
            "path": "",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:user:resetPwd",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1008,
            "menuName": "角色查询",
            "parentName": null,
            "parentId": 101,
            "orderNum": 1,
            "path": "",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:role:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1009,
            "menuName": "角色新增",
            "parentName": null,
            "parentId": 101,
            "orderNum": 2,
            "path": "",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:role:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1010,
            "menuName": "角色修改",
            "parentName": null,
            "parentId": 101,
            "orderNum": 3,
            "path": "",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:role:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1011,
            "menuName": "角色删除",
            "parentName": null,
            "parentId": 101,
            "orderNum": 4,
            "path": "",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:role:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1012,
            "menuName": "角色导出",
            "parentName": null,
            "parentId": 101,
            "orderNum": 5,
            "path": "",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:role:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1013,
            "menuName": "菜单查询",
            "parentName": null,
            "parentId": 102,
            "orderNum": 1,
            "path": "",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:menu:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1014,
            "menuName": "菜单新增",
            "parentName": null,
            "parentId": 102,
            "orderNum": 2,
            "path": "",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:menu:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1015,
            "menuName": "菜单修改",
            "parentName": null,
            "parentId": 102,
            "orderNum": 3,
            "path": "",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:menu:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1016,
            "menuName": "菜单删除",
            "parentName": null,
            "parentId": 102,
            "orderNum": 4,
            "path": "",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:menu:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1017,
            "menuName": "部门查询",
            "parentName": null,
            "parentId": 103,
            "orderNum": 1,
            "path": "",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:dept:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1018,
            "menuName": "部门新增",
            "parentName": null,
            "parentId": 103,
            "orderNum": 2,
            "path": "",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:dept:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1019,
            "menuName": "部门修改",
            "parentName": null,
            "parentId": 103,
            "orderNum": 3,
            "path": "",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:dept:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1020,
            "menuName": "部门删除",
            "parentName": null,
            "parentId": 103,
            "orderNum": 4,
            "path": "",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:dept:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1021,
            "menuName": "岗位查询",
            "parentName": null,
            "parentId": 104,
            "orderNum": 1,
            "path": "",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:post:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1022,
            "menuName": "岗位新增",
            "parentName": null,
            "parentId": 104,
            "orderNum": 2,
            "path": "",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:post:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1023,
            "menuName": "岗位修改",
            "parentName": null,
            "parentId": 104,
            "orderNum": 3,
            "path": "",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:post:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1024,
            "menuName": "岗位删除",
            "parentName": null,
            "parentId": 104,
            "orderNum": 4,
            "path": "",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:post:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1025,
            "menuName": "岗位导出",
            "parentName": null,
            "parentId": 104,
            "orderNum": 5,
            "path": "",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:post:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1026,
            "menuName": "字典查询",
            "parentName": null,
            "parentId": 105,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:dict:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1027,
            "menuName": "字典新增",
            "parentName": null,
            "parentId": 105,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:dict:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1028,
            "menuName": "字典修改",
            "parentName": null,
            "parentId": 105,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:dict:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1029,
            "menuName": "字典删除",
            "parentName": null,
            "parentId": 105,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:dict:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1030,
            "menuName": "字典导出",
            "parentName": null,
            "parentId": 105,
            "orderNum": 5,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:dict:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1031,
            "menuName": "参数查询",
            "parentName": null,
            "parentId": 106,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:config:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1032,
            "menuName": "参数新增",
            "parentName": null,
            "parentId": 106,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:config:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1033,
            "menuName": "参数修改",
            "parentName": null,
            "parentId": 106,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:config:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1034,
            "menuName": "参数删除",
            "parentName": null,
            "parentId": 106,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:config:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1035,
            "menuName": "参数导出",
            "parentName": null,
            "parentId": 106,
            "orderNum": 5,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:config:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1036,
            "menuName": "公告查询",
            "parentName": null,
            "parentId": 107,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:notice:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1037,
            "menuName": "公告新增",
            "parentName": null,
            "parentId": 107,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:notice:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1038,
            "menuName": "公告修改",
            "parentName": null,
            "parentId": 107,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:notice:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1039,
            "menuName": "公告删除",
            "parentName": null,
            "parentId": 107,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:notice:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 500,
            "menuName": "操作日志",
            "parentName": null,
            "parentId": 108,
            "orderNum": 1,
            "path": "operlog",
            "component": "monitor/operlog/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "monitor:operlog:list",
            "icon": "form",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 501,
            "menuName": "登录日志",
            "parentName": null,
            "parentId": 108,
            "orderNum": 2,
            "path": "logininfor",
            "component": "monitor/logininfor/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "monitor:logininfor:list",
            "icon": "logininfor",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1046,
            "menuName": "在线查询",
            "parentName": null,
            "parentId": 109,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "monitor:online:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1047,
            "menuName": "批量强退",
            "parentName": null,
            "parentId": 109,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "monitor:online:batchLogout",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1048,
            "menuName": "单条强退",
            "parentName": null,
            "parentId": 109,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "monitor:online:forceLogout",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1049,
            "menuName": "任务查询",
            "parentName": null,
            "parentId": 110,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "monitor:job:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1050,
            "menuName": "任务新增",
            "parentName": null,
            "parentId": 110,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "monitor:job:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1051,
            "menuName": "任务修改",
            "parentName": null,
            "parentId": 110,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "monitor:job:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1052,
            "menuName": "任务删除",
            "parentName": null,
            "parentId": 110,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "monitor:job:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1053,
            "menuName": "状态修改",
            "parentName": null,
            "parentId": 110,
            "orderNum": 5,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "monitor:job:changeStatus",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1054,
            "menuName": "任务导出",
            "parentName": null,
            "parentId": 110,
            "orderNum": 7,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "monitor:job:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1055,
            "menuName": "生成查询",
            "parentName": null,
            "parentId": 114,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tool:gen:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1056,
            "menuName": "生成修改",
            "parentName": null,
            "parentId": 114,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tool:gen:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1058,
            "menuName": "导入代码",
            "parentName": null,
            "parentId": 114,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tool:gen:import",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1057,
            "menuName": "生成删除",
            "parentName": null,
            "parentId": 114,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tool:gen:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1059,
            "menuName": "预览代码",
            "parentName": null,
            "parentId": 114,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tool:gen:preview",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1060,
            "menuName": "生成代码",
            "parentName": null,
            "parentId": 114,
            "orderNum": 5,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tool:gen:code",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1040,
            "menuName": "操作查询",
            "parentName": null,
            "parentId": 500,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "monitor:operlog:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1041,
            "menuName": "操作删除",
            "parentName": null,
            "parentId": 500,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "monitor:operlog:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1042,
            "menuName": "日志导出",
            "parentName": null,
            "parentId": 500,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "monitor:operlog:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1043,
            "menuName": "登录查询",
            "parentName": null,
            "parentId": 501,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "monitor:logininfor:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1044,
            "menuName": "登录删除",
            "parentName": null,
            "parentId": 501,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "monitor:logininfor:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2018-03-16 11:33:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 1045,
            "menuName": "日志导出",
            "parentName": null,
            "parentId": 501,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "monitor:logininfor:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-02-02 20:11:20",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2064,
            "menuName": "隧道基础信息",
            "parentName": null,
            "parentId": 2063,
            "orderNum": 1,
            "path": "info",
            "component": "project/info/index",
            "query": null,
            "isFrame": "1",
            "isCache": "1",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "project:info:pagination",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-02-02 20:12:12",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2065,
            "menuName": "机电设施档案",
            "parentName": null,
            "parentId": 2063,
            "orderNum": 2,
            "path": "disaster",
            "component": "project/disaster/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "project:disaster:pagination",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-02-02 20:12:33",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2066,
            "menuName": "设备管理",
            "parentName": null,
            "parentId": 2063,
            "orderNum": 3,
            "path": "trajectory",
            "component": "project/trajectory/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "1",
            "status": "1",
            "perms": "project:trajectory:pagination",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-02-02 20:13:12",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2067,
            "menuName": "统计分析",
            "parentName": null,
            "parentId": 2063,
            "orderNum": 4,
            "path": "#",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "1",
            "status": "1",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-08-28 16:12:38",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2087,
            "menuName": "病害复核",
            "parentName": null,
            "parentId": 2063,
            "orderNum": 4,
            "path": "#",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "1",
            "status": "1",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-29 16:05:03",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2068,
            "menuName": "大屏展示",
            "parentName": null,
            "parentId": 2063,
            "orderNum": 5,
            "path": "#",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "1",
            "status": "1",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-08-28 16:13:14",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2088,
            "menuName": "巡检人员管理",
            "parentName": null,
            "parentId": 2063,
            "orderNum": 5,
            "path": "#",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "1",
            "status": "1",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 15:12:47",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2101,
            "menuName": "工程查询",
            "parentName": null,
            "parentId": 2064,
            "orderNum": 1,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:info:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 15:19:39",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2102,
            "menuName": "工程新增",
            "parentName": null,
            "parentId": 2064,
            "orderNum": 2,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:info:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 15:19:43",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2103,
            "menuName": "工程修改",
            "parentName": null,
            "parentId": 2064,
            "orderNum": 3,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:info:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 15:21:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2104,
            "menuName": "工程删除",
            "parentName": null,
            "parentId": 2064,
            "orderNum": 4,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:info:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 15:22:42",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2105,
            "menuName": "工程导出",
            "parentName": null,
            "parentId": 2064,
            "orderNum": 5,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:info:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 15:23:34",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2106,
            "menuName": "工程导入",
            "parentName": null,
            "parentId": 2064,
            "orderNum": 6,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:info:import",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 15:30:49",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2107,
            "menuName": "工程管理1",
            "parentName": null,
            "parentId": 2064,
            "orderNum": 7,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "1",
            "status": "1",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 15:31:36",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2108,
            "menuName": "工程管理2",
            "parentName": null,
            "parentId": 2064,
            "orderNum": 8,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "1",
            "status": "1",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 15:32:27",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2109,
            "menuName": "工程管理3",
            "parentName": null,
            "parentId": 2064,
            "orderNum": 9,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "1",
            "status": "1",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 15:33:16",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2110,
            "menuName": "工程管理4",
            "parentName": null,
            "parentId": 2064,
            "orderNum": 10,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "1",
            "status": "1",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 15:35:41",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2111,
            "menuName": "病害查询",
            "parentName": null,
            "parentId": 2065,
            "orderNum": 1,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:disaster:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 15:45:01",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2112,
            "menuName": "病害新增",
            "parentName": null,
            "parentId": 2065,
            "orderNum": 2,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:disaster:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 15:46:29",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2113,
            "menuName": "病害修改",
            "parentName": null,
            "parentId": 2065,
            "orderNum": 3,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:disaster:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 15:47:12",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2114,
            "menuName": "病害删除",
            "parentName": null,
            "parentId": 2065,
            "orderNum": 4,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:disaster:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 15:48:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2115,
            "menuName": "病害导出",
            "parentName": null,
            "parentId": 2065,
            "orderNum": 5,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:disaster:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 15:48:46",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2116,
            "menuName": "病害导入",
            "parentName": null,
            "parentId": 2065,
            "orderNum": 6,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:disaster:import",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 15:49:32",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2117,
            "menuName": "病害管理1",
            "parentName": null,
            "parentId": 2065,
            "orderNum": 7,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "1",
            "status": "1",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 15:50:14",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2118,
            "menuName": "病害管理2",
            "parentName": null,
            "parentId": 2065,
            "orderNum": 8,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "1",
            "status": "1",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 15:50:55",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2119,
            "menuName": "病害管理3",
            "parentName": null,
            "parentId": 2065,
            "orderNum": 9,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "1",
            "status": "1",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 15:51:32",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2120,
            "menuName": "病害管理4",
            "parentName": null,
            "parentId": 2065,
            "orderNum": 10,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "1",
            "status": "1",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 18:47:34",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2121,
            "menuName": "轨迹查询",
            "parentName": null,
            "parentId": 2066,
            "orderNum": 1,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:trajectory:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 18:49:11",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2122,
            "menuName": "轨迹新增",
            "parentName": null,
            "parentId": 2066,
            "orderNum": 2,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:trajectory:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 18:50:01",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2123,
            "menuName": "轨迹修改",
            "parentName": null,
            "parentId": 2066,
            "orderNum": 3,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:trajectory:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 18:50:45",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2124,
            "menuName": "轨迹删除",
            "parentName": null,
            "parentId": 2066,
            "orderNum": 4,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:trajectory:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 18:51:28",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2125,
            "menuName": "轨迹导出",
            "parentName": null,
            "parentId": 2066,
            "orderNum": 5,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:trajectory:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 18:52:09",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2126,
            "menuName": "轨迹导入",
            "parentName": null,
            "parentId": 2066,
            "orderNum": 6,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:trajectory:import",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 18:53:03",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2127,
            "menuName": "巡检车管理1",
            "parentName": null,
            "parentId": 2066,
            "orderNum": 7,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "1",
            "status": "1",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 18:54:37",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2128,
            "menuName": "巡检车管理2",
            "parentName": null,
            "parentId": 2066,
            "orderNum": 8,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "1",
            "status": "1",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 18:54:45",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2129,
            "menuName": "巡检车管理3",
            "parentName": null,
            "parentId": 2066,
            "orderNum": 9,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "1",
            "status": "1",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-11-28 18:54:51",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2130,
            "menuName": "巡检车管理4",
            "parentName": null,
            "parentId": 2066,
            "orderNum": 10,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "1",
            "status": "1",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-08-28 16:06:51",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2080,
            "menuName": "工程管理",
            "parentName": null,
            "parentId": 2079,
            "orderNum": 1,
            "path": "#",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "1",
            "status": "1",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-08-28 16:07:51",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2083,
            "menuName": "隧道三维可视化旧",
            "parentName": null,
            "parentId": 2079,
            "orderNum": 1,
            "path": "t3d",
            "component": "tunnel/t3d/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "1",
            "status": "1",
            "perms": "tunnel:t3d:list",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-06 16:06:59",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2236,
            "menuName": "隧道三维可视化",
            "parentName": null,
            "parentId": 2079,
            "orderNum": 1,
            "path": "visulize",
            "component": "detection/technology/data/visulize",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-17 23:09:24",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2277,
            "menuName": "病害数据同步",
            "parentName": null,
            "parentId": 2079,
            "orderNum": 1,
            "path": "case",
            "component": "tunnel/case/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:case:list",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-08-28 16:07:16",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2081,
            "menuName": "病害可视化管理",
            "parentName": null,
            "parentId": 2079,
            "orderNum": 2,
            "path": "disease",
            "component": "tunnel/disease/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:disease:list",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-08-28 16:07:39",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2082,
            "menuName": "历史比对",
            "parentName": null,
            "parentId": 2079,
            "orderNum": 3,
            "path": "compare",
            "component": "tunnel/compare/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:compare:list",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-08-28 16:08:44",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2084,
            "menuName": "隧道基本信息",
            "parentName": null,
            "parentId": 2079,
            "orderNum": 5,
            "path": "#",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "1",
            "status": "1",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-08-28 16:08:57",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2085,
            "menuName": "隧道土建结构",
            "parentName": null,
            "parentId": 2079,
            "orderNum": 6,
            "path": "#",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "1",
            "status": "1",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-08-28 16:09:17",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2086,
            "menuName": "隧道机电设施",
            "parentName": null,
            "parentId": 2079,
            "orderNum": 7,
            "path": "#",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "1",
            "status": "1",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-08-28 16:15:58",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2097,
            "menuName": "技术状况评定old",
            "parentName": null,
            "parentId": 2096,
            "orderNum": 1,
            "path": "technology",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "M",
            "visible": "0",
            "status": "1",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-20 15:37:28",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2158,
            "menuName": "技术状况评定o",
            "parentName": null,
            "parentId": 2096,
            "orderNum": 1,
            "path": "technology",
            "component": "detection/technology/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "1",
            "perms": "",
            "icon": "chart",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-08-28 16:16:10",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2098,
            "menuName": "评定状态管理",
            "parentName": null,
            "parentId": 2096,
            "orderNum": 2,
            "path": "evaluate",
            "component": "detection/evaluate/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "1",
            "perms": "",
            "icon": "checkbox",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-04-26 15:38:57",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2136,
            "menuName": "检测数据",
            "parentName": null,
            "parentId": 2096,
            "orderNum": 2,
            "path": "technology/data",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "M",
            "visible": "0",
            "status": "0",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-08-28 16:16:20",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2099,
            "menuName": "检测报告管理old",
            "parentName": null,
            "parentId": 2096,
            "orderNum": 3,
            "path": "report",
            "component": "detection/report/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "1",
            "perms": "",
            "icon": "monitor",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2022-08-28 16:16:32",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2100,
            "menuName": "管理驾驶舱",
            "parentName": null,
            "parentId": 2096,
            "orderNum": 4,
            "path": "#",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "1",
            "status": "1",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-08-05 21:14:07",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2231,
            "menuName": "技术状况评定",
            "parentName": null,
            "parentId": 2096,
            "orderNum": 5,
            "path": "main",
            "component": "detection/technology/data/main",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "project:main:list,project:project:searchAlllist,tunnel:manage:searchAllList",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-08-05 21:28:43",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2232,
            "menuName": "检测报告管理",
            "parentName": null,
            "parentId": 2096,
            "orderNum": 6,
            "path": "reportManage",
            "component": "detection/technology/data/reportManage",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "project:report:list,project:project:searchAlllist,tunnel:manage:searchAllList",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-04-26 15:37:46",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2135,
            "menuName": "定检内容",
            "parentName": null,
            "parentId": 2097,
            "orderNum": 1,
            "path": "#",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "1",
            "status": "1",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-04-26 15:40:07",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2137,
            "menuName": "技术评分",
            "parentName": null,
            "parentId": 2097,
            "orderNum": 7,
            "path": "score",
            "component": "detection/technology/score/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "1",
            "status": "1",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-04-26 15:43:11",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2138,
            "menuName": "统计分析",
            "parentName": null,
            "parentId": 2097,
            "orderNum": 8,
            "path": "technology/statistics",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "M",
            "visible": "0",
            "status": "1",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-05-05 14:19:47",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2139,
            "menuName": "评定管理",
            "parentName": null,
            "parentId": 2097,
            "orderNum": 9,
            "path": "#",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "1",
            "status": "1",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-05-05 14:22:14",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2140,
            "menuName": "报告管理",
            "parentName": null,
            "parentId": 2097,
            "orderNum": 10,
            "path": "#",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "1",
            "status": "1",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-04-15 21:26:56",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2132,
            "menuName": "养护数据管理",
            "parentName": null,
            "parentId": 2131,
            "orderNum": 1,
            "path": "data",
            "component": "maintenance/data/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "maintenance:data:list",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-04-15 21:29:24",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2133,
            "menuName": "养护管理决策",
            "parentName": null,
            "parentId": 2131,
            "orderNum": 2,
            "path": "manage",
            "component": "maintenance/manage/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "maintenance:manage:list",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-04-15 21:30:57",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2134,
            "menuName": "养护历史数据",
            "parentName": null,
            "parentId": 2131,
            "orderNum": 3,
            "path": "history",
            "component": "maintenance/history/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "maintenance:history:list",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-27 11:45:30",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2284,
            "menuName": "土建结构",
            "parentName": null,
            "parentId": 2136,
            "orderNum": 1,
            "path": "land",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "M",
            "visible": "0",
            "status": "0",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-27 11:54:57",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2286,
            "menuName": "机电设施",
            "parentName": null,
            "parentId": 2136,
            "orderNum": 2,
            "path": "jidian",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "M",
            "visible": "0",
            "status": "0",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-27 11:46:21",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2285,
            "menuName": "其他工程设施",
            "parentName": null,
            "parentId": 2136,
            "orderNum": 3,
            "path": "other",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "M",
            "visible": "0",
            "status": "0",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-08-15 14:37:48",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2233,
            "menuName": "图片管理",
            "parentName": null,
            "parentId": 2136,
            "orderNum": 10,
            "path": "technology/data",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "M",
            "visible": "0",
            "status": "0",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-04-26 16:09:13",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2151,
            "menuName": "资产统计",
            "parentName": null,
            "parentId": 2138,
            "orderNum": 1,
            "path": "property",
            "component": "detection/technology/statistics/property",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-04-26 16:10:13",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2152,
            "menuName": "评定分析",
            "parentName": null,
            "parentId": 2138,
            "orderNum": 2,
            "path": "assess",
            "component": "detection/technology/statistics/assess",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:05",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2190,
            "menuName": "隧道检查-洞口信息查询",
            "parentName": null,
            "parentId": 2141,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:opening:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:05",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2191,
            "menuName": "隧道检查-洞口信息新增",
            "parentName": null,
            "parentId": 2141,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:opening:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:05",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2192,
            "menuName": "隧道检查-洞口信息修改",
            "parentName": null,
            "parentId": 2141,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:opening:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:05",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2193,
            "menuName": "隧道检查-洞口信息删除",
            "parentName": null,
            "parentId": 2141,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:opening:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:05",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2194,
            "menuName": "隧道检查-洞口信息导出",
            "parentName": null,
            "parentId": 2141,
            "orderNum": 5,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:opening:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:25:36",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2252,
            "menuName": "洞口-批量新增",
            "parentName": null,
            "parentId": 2141,
            "orderNum": 6,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:opening:batchAdd",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:06",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2202,
            "menuName": "隧道检查-洞门信息查询",
            "parentName": null,
            "parentId": 2142,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:portal:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:06",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2203,
            "menuName": "隧道检查-洞门信息新增",
            "parentName": null,
            "parentId": 2142,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:portal:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:06",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2204,
            "menuName": "隧道检查-洞门信息修改",
            "parentName": null,
            "parentId": 2142,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:portal:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:06",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2205,
            "menuName": "隧道检查-洞门信息删除",
            "parentName": null,
            "parentId": 2142,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:portal:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:06",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2206,
            "menuName": "隧道检查-洞门信息导出",
            "parentName": null,
            "parentId": 2142,
            "orderNum": 5,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:portal:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:26:17",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2253,
            "menuName": "洞门-批量新增",
            "parentName": null,
            "parentId": 2142,
            "orderNum": 6,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:portal:batchAdd",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:05",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2178,
            "menuName": "隧道检查-衬砌查询",
            "parentName": null,
            "parentId": 2143,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:lining:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:05",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2179,
            "menuName": "隧道检查-衬砌新增",
            "parentName": null,
            "parentId": 2143,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:lining:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:05",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2180,
            "menuName": "隧道检查-衬砌修改",
            "parentName": null,
            "parentId": 2143,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:lining:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:05",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2181,
            "menuName": "隧道检查-衬砌删除",
            "parentName": null,
            "parentId": 2143,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:lining:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:05",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2182,
            "menuName": "隧道检查-衬砌导出",
            "parentName": null,
            "parentId": 2143,
            "orderNum": 5,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:lining:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:27:10",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2254,
            "menuName": "寸砌批量新增",
            "parentName": null,
            "parentId": 2143,
            "orderNum": 6,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:lining:batchAdd",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:06",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2214,
            "menuName": "隧道检查-路面查询",
            "parentName": null,
            "parentId": 2144,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:road:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:06",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2215,
            "menuName": "隧道检查-路面新增",
            "parentName": null,
            "parentId": 2144,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:road:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:06",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2216,
            "menuName": "隧道检查-路面修改",
            "parentName": null,
            "parentId": 2144,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:road:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:06",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2217,
            "menuName": "隧道检查-路面删除",
            "parentName": null,
            "parentId": 2144,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:road:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:06",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2218,
            "menuName": "隧道检查-路面导出",
            "parentName": null,
            "parentId": 2144,
            "orderNum": 5,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:road:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:28:13",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2255,
            "menuName": "路面-批量新增",
            "parentName": null,
            "parentId": 2144,
            "orderNum": 6,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:road:batchAdd",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:05",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2184,
            "menuName": "隧道检查-检修道查询",
            "parentName": null,
            "parentId": 2145,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:manhole:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:05",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2185,
            "menuName": "隧道检查-检修道新增",
            "parentName": null,
            "parentId": 2145,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:manhole:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:05",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2186,
            "menuName": "隧道检查-检修道修改",
            "parentName": null,
            "parentId": 2145,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:manhole:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:05",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2187,
            "menuName": "隧道检查-检修道删除",
            "parentName": null,
            "parentId": 2145,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:manhole:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:05",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2188,
            "menuName": "隧道检查-检修道导出",
            "parentName": null,
            "parentId": 2145,
            "orderNum": 5,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:manhole:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:28:50",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2256,
            "menuName": "检修道-批量新增",
            "parentName": null,
            "parentId": 2145,
            "orderNum": 6,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:manhole:batchAdd",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:04",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2166,
            "menuName": "隧道检查-排水系统查询",
            "parentName": null,
            "parentId": 2146,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:drainage:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:04",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2167,
            "menuName": "隧道检查-排水系统新增",
            "parentName": null,
            "parentId": 2146,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:drainage:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:04",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2168,
            "menuName": "隧道检查-排水系统修改",
            "parentName": null,
            "parentId": 2146,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:drainage:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:04",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2169,
            "menuName": "隧道检查-排水系统删除",
            "parentName": null,
            "parentId": 2146,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:drainage:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:04",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2170,
            "menuName": "隧道检查-排水系统导出",
            "parentName": null,
            "parentId": 2146,
            "orderNum": 5,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:drainage:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:30:06",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2257,
            "menuName": "排水系统-批量新增",
            "parentName": null,
            "parentId": 2146,
            "orderNum": 6,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:drainage:batchAdd",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:04",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2160,
            "menuName": "隧道检查-吊顶预埋件查询",
            "parentName": null,
            "parentId": 2147,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:ceiling:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:04",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2161,
            "menuName": "隧道检查-吊顶预埋件新增",
            "parentName": null,
            "parentId": 2147,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:ceiling:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:04",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2162,
            "menuName": "隧道检查-吊顶预埋件修改",
            "parentName": null,
            "parentId": 2147,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:ceiling:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:04",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2163,
            "menuName": "隧道检查-吊顶预埋件删除",
            "parentName": null,
            "parentId": 2147,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:ceiling:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:04",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2164,
            "menuName": "隧道检查-吊顶预埋件导出",
            "parentName": null,
            "parentId": 2147,
            "orderNum": 5,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:ceiling:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:31:20",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2258,
            "menuName": "吊顶预埋件-批量新增",
            "parentName": null,
            "parentId": 2147,
            "orderNum": 6,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:ceiling:batchAdd",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:05",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2172,
            "menuName": "隧道检查-内装饰查询",
            "parentName": null,
            "parentId": 2148,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:interior:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:05",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2173,
            "menuName": "隧道检查-内装饰新增",
            "parentName": null,
            "parentId": 2148,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:interior:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:05",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2174,
            "menuName": "隧道检查-内装饰修改",
            "parentName": null,
            "parentId": 2148,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:interior:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:05",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2175,
            "menuName": "隧道检查-内装饰删除",
            "parentName": null,
            "parentId": 2148,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:interior:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:05",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2176,
            "menuName": "隧道检查-内装饰导出",
            "parentName": null,
            "parentId": 2148,
            "orderNum": 5,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:interior:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:31:56",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2259,
            "menuName": "内装饰-批量新增",
            "parentName": null,
            "parentId": 2148,
            "orderNum": 6,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:interior:batchAdd",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:05",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2196,
            "menuName": "隧道检查-标线轮廓标查询",
            "parentName": null,
            "parentId": 2149,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:outline:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:05",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2197,
            "menuName": "隧道检查-标线轮廓标新增",
            "parentName": null,
            "parentId": 2149,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:outline:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:05",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2198,
            "menuName": "隧道检查-标线轮廓标修改",
            "parentName": null,
            "parentId": 2149,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:outline:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:05",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2199,
            "menuName": "隧道检查-标线轮廓标删除",
            "parentName": null,
            "parentId": 2149,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:outline:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-25 17:01:05",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2200,
            "menuName": "隧道检查-标线轮廓标导出",
            "parentName": null,
            "parentId": 2149,
            "orderNum": 5,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:outline:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:32:56",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2260,
            "menuName": "标线轮廓-批量新增",
            "parentName": null,
            "parentId": 2149,
            "orderNum": 6,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:outline:batchAdd",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-06-29 21:32:12",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2154,
            "menuName": "项目管理old",
            "parentName": null,
            "parentId": 2153,
            "orderNum": 1,
            "path": "projectManage",
            "component": "basic/projectManage/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "1",
            "perms": "basic:projectManage:list",
            "icon": "monitor",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-06-29 21:33:13",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2155,
            "menuName": "隧道档案信息管理old",
            "parentName": null,
            "parentId": 2153,
            "orderNum": 2,
            "path": "recordManage",
            "component": "basic/recordManage/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "1",
            "perms": "basic:recordManage:list",
            "icon": "select",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-03 11:38:45",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2156,
            "menuName": "隧道关联",
            "parentName": null,
            "parentId": 2153,
            "orderNum": 3,
            "path": "projectTunnel",
            "component": "basic/projectManage/projectTunnel",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "1",
            "status": "0",
            "perms": "basic:projectTunnel",
            "icon": "date",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-06-03 19:54:00",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2358,
            "menuName": "首页大屏数据维护",
            "parentName": null,
            "parentId": 2153,
            "orderNum": 11,
            "path": "indexData",
            "component": "project/indexData/index",
            "query": null,
            "isFrame": "1",
            "isCache": "1",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-08-30 16:49:39",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2234,
            "menuName": "项目管理",
            "parentName": null,
            "parentId": 2153,
            "orderNum": 13,
            "path": "project",
            "component": "detection/technology/data/project",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "project:project:list,tunnel:inspection:list,tunnel:inspection:add,tunnel:inspection:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-08-30 16:50:32",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2235,
            "menuName": "隧道档案信息管理",
            "parentName": null,
            "parentId": 2153,
            "orderNum": 14,
            "path": "tunnlel",
            "component": "detection/technology/data/tunnlel",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:manage:list",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-07-09 18:48:21",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2157,
            "menuName": "隧道关联",
            "parentName": null,
            "parentId": 2154,
            "orderNum": 1,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "basic:projectManage:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-08-04 16:41:36",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2220,
            "menuName": "衬砌图片管理查询",
            "parentName": null,
            "parentId": 2219,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:zip:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-08-04 16:41:36",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2221,
            "menuName": "衬砌图片管理新增",
            "parentName": null,
            "parentId": 2219,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:zip:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-08-04 16:41:36",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2222,
            "menuName": "衬砌图片管理修改",
            "parentName": null,
            "parentId": 2219,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:zip:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-08-04 16:41:36",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2223,
            "menuName": "衬砌图片管理删除",
            "parentName": null,
            "parentId": 2219,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:zip:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-08-04 16:41:36",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2224,
            "menuName": "衬砌图片管理导出",
            "parentName": null,
            "parentId": 2219,
            "orderNum": 5,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:zip:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-03-04 23:03:23",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2357,
            "menuName": "关联",
            "parentName": null,
            "parentId": 2219,
            "orderNum": 6,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:zip:relationZipPicture",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-08-04 16:58:15",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2226,
            "menuName": "展布图管理查询",
            "parentName": null,
            "parentId": 2225,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:zip:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-08-04 16:58:15",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2227,
            "menuName": "展布图管理新增",
            "parentName": null,
            "parentId": 2225,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:zip:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-08-04 16:58:15",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2228,
            "menuName": "展布图管理修改",
            "parentName": null,
            "parentId": 2225,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:zip:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-08-04 16:58:15",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2229,
            "menuName": "展布图管理删除",
            "parentName": null,
            "parentId": 2225,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:zip:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-08-04 16:58:15",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2230,
            "menuName": "展布图管理导出",
            "parentName": null,
            "parentId": 2225,
            "orderNum": 5,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:zip:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:38:15",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2261,
            "menuName": "技术状况-新增",
            "parentName": null,
            "parentId": 2231,
            "orderNum": 1,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:main:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:38:54",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2262,
            "menuName": "技术状况-删除",
            "parentName": null,
            "parentId": 2231,
            "orderNum": 2,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:main:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:39:27",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2263,
            "menuName": "技术状况-导出",
            "parentName": null,
            "parentId": 2231,
            "orderNum": 3,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:main:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:40:16",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2264,
            "menuName": "技术状况-查看",
            "parentName": null,
            "parentId": 2231,
            "orderNum": 4,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:score:detail",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:42:26",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2265,
            "menuName": "技术状况-评定",
            "parentName": null,
            "parentId": 2231,
            "orderNum": 5,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:score:score",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:42:59",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2266,
            "menuName": "检测报告-新增",
            "parentName": null,
            "parentId": 2232,
            "orderNum": 1,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:report:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:43:31",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2267,
            "menuName": "检测报告-删除",
            "parentName": null,
            "parentId": 2232,
            "orderNum": 2,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:report:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:44:09",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2268,
            "menuName": "检测报告列表数据-导出",
            "parentName": null,
            "parentId": 2232,
            "orderNum": 3,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:report:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:44:42",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2269,
            "menuName": "检测报告-生成",
            "parentName": null,
            "parentId": 2232,
            "orderNum": 4,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:report:generater",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-12-26 22:31:52",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2271,
            "menuName": "检测报告-导出",
            "parentName": null,
            "parentId": 2232,
            "orderNum": 5,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:report:exportSingle",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-08-04 16:41:36",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2219,
            "menuName": "病害图",
            "parentName": null,
            "parentId": 2233,
            "orderNum": 3,
            "path": "zip",
            "component": "detection/technology/data/zip",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "project:zip:list",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-08-04 16:58:15",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2225,
            "menuName": "展布图",
            "parentName": null,
            "parentId": 2233,
            "orderNum": 4,
            "path": "picture",
            "component": "detection/technology/data/zhanBuPicture",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "project:zip:list",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:15:35",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2240,
            "menuName": "项目管理-查询",
            "parentName": null,
            "parentId": 2234,
            "orderNum": 1,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:project:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:16:12",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2241,
            "menuName": "项目管理-新增",
            "parentName": null,
            "parentId": 2234,
            "orderNum": 2,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:project:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:16:50",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2242,
            "menuName": "项目管理-删除",
            "parentName": null,
            "parentId": 2234,
            "orderNum": 3,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:project:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:17:35",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2243,
            "menuName": "项目管理-批量新增",
            "parentName": null,
            "parentId": 2234,
            "orderNum": 4,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:project:batchAdd",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:18:12",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2244,
            "menuName": "项目管理-导出",
            "parentName": null,
            "parentId": 2234,
            "orderNum": 5,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:project:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:19:18",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2245,
            "menuName": "项目管理-修改",
            "parentName": null,
            "parentId": 2234,
            "orderNum": 6,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:project:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-12-26 22:49:45",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2272,
            "menuName": "项目管理-关联",
            "parentName": null,
            "parentId": 2234,
            "orderNum": 7,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "project:project:relate",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-12-26 23:15:57",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2273,
            "menuName": "关联隧道-删除",
            "parentName": null,
            "parentId": 2234,
            "orderNum": 8,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:inspection:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-12-26 23:20:31",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2274,
            "menuName": "关联隧道编辑",
            "parentName": null,
            "parentId": 2234,
            "orderNum": 9,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:inspection:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-12-26 23:21:09",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2275,
            "menuName": "关联隧道-批量新增",
            "parentName": null,
            "parentId": 2234,
            "orderNum": 10,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:inspection:batchAdd",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-12-26 23:22:02",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2276,
            "menuName": "关联隧道-导出",
            "parentName": null,
            "parentId": 2234,
            "orderNum": 11,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:inspection:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:21:19",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2246,
            "menuName": "隧道档案-查询",
            "parentName": null,
            "parentId": 2235,
            "orderNum": 1,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:manage:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:21:47",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2247,
            "menuName": "隧道档案-新增",
            "parentName": null,
            "parentId": 2235,
            "orderNum": 2,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:manage:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:22:14",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2248,
            "menuName": "隧道档案-修改",
            "parentName": null,
            "parentId": 2235,
            "orderNum": 3,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:manage:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:22:55",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2249,
            "menuName": "隧道档案-删除",
            "parentName": null,
            "parentId": 2235,
            "orderNum": 4,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:manage:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:23:56",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2250,
            "menuName": "隧道档案-批量新增",
            "parentName": null,
            "parentId": 2235,
            "orderNum": 5,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:manage:batchAdd",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-07 23:24:35",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2251,
            "menuName": "隧道档案-导出",
            "parentName": null,
            "parentId": 2235,
            "orderNum": 6,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:manage:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-12-26 12:53:52",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2270,
            "menuName": "查询",
            "parentName": null,
            "parentId": 2236,
            "orderNum": 1,
            "path": "tunnel:visulize:list",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:visulize:list",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-06 17:34:57",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2237,
            "menuName": "隧道三维可视化-查看按钮",
            "parentName": null,
            "parentId": 2236,
            "orderNum": 2,
            "path": "#",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:visulize:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-06 17:36:15",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2238,
            "menuName": "隧道三维可视化-删除按钮",
            "parentName": null,
            "parentId": 2236,
            "orderNum": 3,
            "path": "#",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:visulize:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-10-06 18:08:43",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2239,
            "menuName": "隧道三维可视化-新增按钮",
            "parentName": null,
            "parentId": 2236,
            "orderNum": 4,
            "path": "#",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:visulize:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-17 23:09:24",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2278,
            "menuName": "病害数据同步查询",
            "parentName": null,
            "parentId": 2277,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:case:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-17 23:09:24",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2279,
            "menuName": "病害数据同步新增",
            "parentName": null,
            "parentId": 2277,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:case:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-17 23:09:24",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2280,
            "menuName": "病害数据同步修改",
            "parentName": null,
            "parentId": 2277,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:case:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-17 23:09:24",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2281,
            "menuName": "病害数据同步删除",
            "parentName": null,
            "parentId": 2277,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:case:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-17 23:09:24",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2282,
            "menuName": "病害数据同步导出",
            "parentName": null,
            "parentId": 2277,
            "orderNum": 5,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:case:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-18 01:29:04",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2283,
            "menuName": "病害数据同步按钮",
            "parentName": null,
            "parentId": 2277,
            "orderNum": 6,
            "path": "",
            "component": null,
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:case:sync",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-04-26 15:54:50",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2141,
            "menuName": "洞口",
            "parentName": null,
            "parentId": 2284,
            "orderNum": 1,
            "path": "opening",
            "component": "detection/technology/data/opening",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:opening:list,project:project:searchAlllist,tunnel:manage:searchAllList",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-04-26 15:57:38",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2142,
            "menuName": "洞门",
            "parentName": null,
            "parentId": 2284,
            "orderNum": 2,
            "path": "portal",
            "component": "detection/technology/data/portal",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:portal:list,project:project:searchAlllist,tunnel:manage:searchAllList",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-04-26 15:58:44",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2143,
            "menuName": "衬砌",
            "parentName": null,
            "parentId": 2284,
            "orderNum": 3,
            "path": "lining",
            "component": "detection/technology/data/lining",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:lining:list,project:project:searchAlllist,tunnel:manage:searchAllList",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-04-26 15:59:43",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2144,
            "menuName": "路面",
            "parentName": null,
            "parentId": 2284,
            "orderNum": 4,
            "path": "road",
            "component": "detection/technology/data/road",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:road:list,project:project:searchAlllist,tunnel:manage:searchAllList",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-04-26 16:00:59",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2145,
            "menuName": "检修道",
            "parentName": null,
            "parentId": 2284,
            "orderNum": 5,
            "path": "manhole",
            "component": "detection/technology/data/manhole",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:manhole:list,project:project:searchAlllist,tunnel:manage:searchAllList",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-05-05 14:28:08",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2146,
            "menuName": "排水系统",
            "parentName": null,
            "parentId": 2284,
            "orderNum": 6,
            "path": "drainage",
            "component": "detection/technology/data/drainage",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:drainage:list,project:project:searchAlllist,tunnel:manage:searchAllList",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-05-05 14:29:14",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2147,
            "menuName": "吊顶预埋件",
            "parentName": null,
            "parentId": 2284,
            "orderNum": 7,
            "path": "ceiling",
            "component": "detection/technology/data/ceiling",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:ceiling:list,project:project:searchAlllist,tunnel:manage:searchAllList",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-05-05 14:30:05",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2148,
            "menuName": "内装饰",
            "parentName": null,
            "parentId": 2284,
            "orderNum": 8,
            "path": "interior",
            "component": "detection/technology/data/interior",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:interior:list,project:project:searchAlllist,tunnel:manage:searchAllList",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2023-05-05 14:30:57",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2149,
            "menuName": "标线轮廓标",
            "parentName": null,
            "parentId": 2284,
            "orderNum": 9,
            "path": "outline",
            "component": "detection/technology/data/outline",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "tunnel:outline:list,project:project:searchAlllist,tunnel:manage:searchAllList",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:12",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2308,
            "menuName": "设备洞室",
            "parentName": null,
            "parentId": 2285,
            "orderNum": 1,
            "path": "deviceHole",
            "component": "other/deviceHole/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "system:deviceHole:list",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:21",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2315,
            "menuName": "洞口限高门架",
            "parentName": null,
            "parentId": 2285,
            "orderNum": 1,
            "path": "frame",
            "component": "other/frame/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "other:frame:list",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:29",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2322,
            "menuName": "房屋设施",
            "parentName": null,
            "parentId": 2285,
            "orderNum": 1,
            "path": "housingTreatement",
            "component": "other/housingTreatement/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "system:housingTreatement:list",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:38",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2329,
            "menuName": "减光设施",
            "parentName": null,
            "parentId": 2285,
            "orderNum": 1,
            "path": "lightReduction",
            "component": "other/lightReduction/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "other:lightReduction:list",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:49",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2336,
            "menuName": "消音设施",
            "parentName": null,
            "parentId": 2285,
            "orderNum": 1,
            "path": "noiseReduction",
            "component": "other/noiseReduction/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "other:noiseReduction:list",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:59",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2343,
            "menuName": "联络通道",
            "parentName": null,
            "parentId": 2285,
            "orderNum": 1,
            "path": "passageAccess",
            "component": "other/passageAccess/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "other:passageAccess:list",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:44:09",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2350,
            "menuName": "污水处理设施",
            "parentName": null,
            "parentId": 2285,
            "orderNum": 1,
            "path": "wastewaterTreatment",
            "component": "other/wastewaterTreatment/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "other:wastewaterTreatment:list",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:42:42",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2287,
            "menuName": "电缆沟",
            "parentName": null,
            "parentId": 2285,
            "orderNum": 1,
            "path": "cableGou",
            "component": "other/cableGou/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "other:cableGou:list",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:42:51",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2294,
            "menuName": "洞口绿化",
            "parentName": null,
            "parentId": 2285,
            "orderNum": 1,
            "path": "caveGreening",
            "component": "other/caveGreening/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "other:caveGreening:list",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:01",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2301,
            "menuName": "洞口雕塑/隧道铭牌",
            "parentName": null,
            "parentId": 2285,
            "orderNum": 1,
            "path": "caveTunnelNameplate",
            "component": "other/caveTunnelNameplate/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "other:caveTunnelNameplate:list",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:42:42",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2288,
            "menuName": "电缆沟查询",
            "parentName": null,
            "parentId": 2287,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:cableGou:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:42:42",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2289,
            "menuName": "电缆沟新增",
            "parentName": null,
            "parentId": 2287,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:cableGou:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:42:42",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2290,
            "menuName": "电缆沟修改",
            "parentName": null,
            "parentId": 2287,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:cableGou:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:42:42",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2291,
            "menuName": "电缆沟删除",
            "parentName": null,
            "parentId": 2287,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:cableGou:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:42:42",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2292,
            "menuName": "电缆沟导出",
            "parentName": null,
            "parentId": 2287,
            "orderNum": 5,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:cableGou:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:42:42",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2293,
            "menuName": "电缆沟批量新增",
            "parentName": null,
            "parentId": 2287,
            "orderNum": 6,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:cableGou:batchAdd",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:42:51",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2295,
            "menuName": "洞口绿化查询",
            "parentName": null,
            "parentId": 2294,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:caveGreening:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:42:51",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2296,
            "menuName": "洞口绿化新增",
            "parentName": null,
            "parentId": 2294,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:caveGreening:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:42:51",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2297,
            "menuName": "洞口绿化修改",
            "parentName": null,
            "parentId": 2294,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:caveGreening:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:42:51",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2298,
            "menuName": "洞口绿化删除",
            "parentName": null,
            "parentId": 2294,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:caveGreening:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:42:51",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2299,
            "menuName": "洞口绿化导出",
            "parentName": null,
            "parentId": 2294,
            "orderNum": 5,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:caveGreening:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:42:51",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2300,
            "menuName": "洞口绿化批量新增",
            "parentName": null,
            "parentId": 2294,
            "orderNum": 6,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:caveGreening:batchAdd",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:01",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2302,
            "menuName": "洞口雕塑/隧道铭牌查询",
            "parentName": null,
            "parentId": 2301,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:caveTunnelNameplate:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:01",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2303,
            "menuName": "洞口雕塑/隧道铭牌新增",
            "parentName": null,
            "parentId": 2301,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:caveTunnelNameplate:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:01",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2304,
            "menuName": "洞口雕塑/隧道铭牌修改",
            "parentName": null,
            "parentId": 2301,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:caveTunnelNameplate:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:01",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2305,
            "menuName": "洞口雕塑/隧道铭牌删除",
            "parentName": null,
            "parentId": 2301,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:caveTunnelNameplate:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:01",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2306,
            "menuName": "洞口雕塑/隧道铭牌导出",
            "parentName": null,
            "parentId": 2301,
            "orderNum": 5,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:caveTunnelNameplate:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:01",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2307,
            "menuName": "洞口雕塑/隧道铭牌批量新增",
            "parentName": null,
            "parentId": 2301,
            "orderNum": 6,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:caveTunnelNameplate:batchAdd",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:12",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2309,
            "menuName": "设备洞室查询",
            "parentName": null,
            "parentId": 2308,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:deviceHole:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:12",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2310,
            "menuName": "设备洞室新增",
            "parentName": null,
            "parentId": 2308,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:deviceHole:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:12",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2311,
            "menuName": "设备洞室修改",
            "parentName": null,
            "parentId": 2308,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:deviceHole:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:12",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2312,
            "menuName": "设备洞室删除",
            "parentName": null,
            "parentId": 2308,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:deviceHole:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:12",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2313,
            "menuName": "设备洞室导出",
            "parentName": null,
            "parentId": 2308,
            "orderNum": 5,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:deviceHole:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:12",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2314,
            "menuName": "设备洞室批量新增",
            "parentName": null,
            "parentId": 2308,
            "orderNum": 6,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:deviceHole:batchAdd",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:21",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2316,
            "menuName": "洞口限高门架查询",
            "parentName": null,
            "parentId": 2315,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:frame:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:21",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2317,
            "menuName": "洞口限高门架新增",
            "parentName": null,
            "parentId": 2315,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:frame:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:21",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2318,
            "menuName": "洞口限高门架修改",
            "parentName": null,
            "parentId": 2315,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:frame:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:21",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2319,
            "menuName": "洞口限高门架删除",
            "parentName": null,
            "parentId": 2315,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:frame:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:21",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2320,
            "menuName": "洞口限高门架导出",
            "parentName": null,
            "parentId": 2315,
            "orderNum": 5,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:frame:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:21",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2321,
            "menuName": "洞口限高门架批量新增",
            "parentName": null,
            "parentId": 2315,
            "orderNum": 6,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:frame:batchAdd",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:29",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2323,
            "menuName": "房屋设施查询",
            "parentName": null,
            "parentId": 2322,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:housingTreatement:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:29",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2324,
            "menuName": "房屋设施新增",
            "parentName": null,
            "parentId": 2322,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:housingTreatement:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:29",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2325,
            "menuName": "房屋设施修改",
            "parentName": null,
            "parentId": 2322,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:housingTreatement:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:29",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2326,
            "menuName": "房屋设施删除",
            "parentName": null,
            "parentId": 2322,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:housingTreatement:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:29",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2327,
            "menuName": "房屋设施导出",
            "parentName": null,
            "parentId": 2322,
            "orderNum": 5,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:housingTreatement:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:29",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2328,
            "menuName": "房屋设施批量新增",
            "parentName": null,
            "parentId": 2322,
            "orderNum": 6,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "system:housingTreatement:batchAdd",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:38",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2330,
            "menuName": "减光设施查询",
            "parentName": null,
            "parentId": 2329,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:lightReduction:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:38",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2331,
            "menuName": "减光设施新增",
            "parentName": null,
            "parentId": 2329,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:lightReduction:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:38",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2332,
            "menuName": "减光设施修改",
            "parentName": null,
            "parentId": 2329,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:lightReduction:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:38",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2333,
            "menuName": "减光设施删除",
            "parentName": null,
            "parentId": 2329,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:lightReduction:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:38",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2334,
            "menuName": "减光设施导出",
            "parentName": null,
            "parentId": 2329,
            "orderNum": 5,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:lightReduction:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:38",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2335,
            "menuName": "减光设施批量新增",
            "parentName": null,
            "parentId": 2329,
            "orderNum": 6,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:lightReduction:batchAdd",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:49",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2337,
            "menuName": "消音设施查询",
            "parentName": null,
            "parentId": 2336,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:noiseReduction:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:49",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2338,
            "menuName": "消音设施新增",
            "parentName": null,
            "parentId": 2336,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:noiseReduction:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:49",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2339,
            "menuName": "消音设施修改",
            "parentName": null,
            "parentId": 2336,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:noiseReduction:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:49",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2340,
            "menuName": "消音设施删除",
            "parentName": null,
            "parentId": 2336,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:noiseReduction:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:49",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2341,
            "menuName": "消音设施导出",
            "parentName": null,
            "parentId": 2336,
            "orderNum": 5,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:noiseReduction:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:49",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2342,
            "menuName": "消音设施批量新增",
            "parentName": null,
            "parentId": 2336,
            "orderNum": 6,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:noiseReduction:batchAdd",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:59",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2344,
            "menuName": "联络通道查询",
            "parentName": null,
            "parentId": 2343,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:passageAccess:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:59",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2345,
            "menuName": "联络通道新增",
            "parentName": null,
            "parentId": 2343,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:passageAccess:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:59",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2346,
            "menuName": "联络通道修改",
            "parentName": null,
            "parentId": 2343,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:passageAccess:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:59",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2347,
            "menuName": "联络通道删除",
            "parentName": null,
            "parentId": 2343,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:passageAccess:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:59",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2348,
            "menuName": "联络通道导出",
            "parentName": null,
            "parentId": 2343,
            "orderNum": 5,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:passageAccess:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:43:59",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2349,
            "menuName": "联络通道批量新增",
            "parentName": null,
            "parentId": 2343,
            "orderNum": 6,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:passageAccess:batchAdd",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:44:09",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2351,
            "menuName": "污水处理设施查询",
            "parentName": null,
            "parentId": 2350,
            "orderNum": 1,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:wastewaterTreatment:query",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:44:09",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2352,
            "menuName": "污水处理设施新增",
            "parentName": null,
            "parentId": 2350,
            "orderNum": 2,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:wastewaterTreatment:add",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:44:09",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2353,
            "menuName": "污水处理设施修改",
            "parentName": null,
            "parentId": 2350,
            "orderNum": 3,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:wastewaterTreatment:edit",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:44:09",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2354,
            "menuName": "污水处理设施删除",
            "parentName": null,
            "parentId": 2350,
            "orderNum": 4,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:wastewaterTreatment:remove",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:44:09",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2355,
            "menuName": "污水处理设施导出",
            "parentName": null,
            "parentId": 2350,
            "orderNum": 5,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:wastewaterTreatment:export",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2024-01-29 12:44:09",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2356,
            "menuName": "污水处理设施批量新增",
            "parentName": null,
            "parentId": 2350,
            "orderNum": 6,
            "path": "#",
            "component": "",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "F",
            "visible": "0",
            "status": "0",
            "perms": "other:wastewaterTreatment:batchAdd",
            "icon": "#",
            "children": []
        },
        {
            "searchValue": null,
            "createBy": null,
            "createTime": "2025-06-18 17:54:20",
            "updateBy": null,
            "updateTime": null,
            "remark": null,
            "params": {},
            "menuId": 2360,
            "menuName": "交付中心",
            "parentName": null,
            "parentId": 2359,
            "orderNum": 1,
            "path": "delivery-tenter",
            "component": "project-delivery/delivery-tenter/index",
            "query": null,
            "isFrame": "1",
            "isCache": "0",
            "menuType": "C",
            "visible": "0",
            "status": "0",
            "perms": "delivery-tenter:data:list",
            "icon": "#",
            "children": []
        }
    ]
}