import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询全部病害列表
export function listDisaster(query) {
  return request({
    url: '/project/disaster/list',
    method: 'get',
    params: query
  })
}

// 查询某工程病害列表
export function listByProjectName(projectName) {
  return request({
    url: '/project/disaster/listByProjectName/' + parseStrEmpty(projectName),
    method: 'get',
  })
}

// 查询病害详细
export function getById(disasterId) {
  return request({
    url: '/project/disaster/' + parseStrEmpty(disasterId),
    method: 'get'
  })
}

// 查询病害详细列表
export function selectById(disasterId) {
  return request({
    url: '/project/disaster/select/' + parseStrEmpty(disasterId),
    method: 'get'
  })
}

// 新增病害
export function addDisaster(data) {
  return request({
    url: '/project/disaster/add',
    method: 'post',
    data: data
  })
}

// 修改病害
export function updateDisaster(data, disasterId) {
  return request({
    url: '/project/disaster/' + parseStrEmpty(disasterId),
    method: 'put',
    data: data
  })
}

// 删除病害
export function delDisaster(disasterId) {
  return request({
    url: '/project/disaster/' + parseStrEmpty(disasterId),
    method: 'delete'
  })
}

