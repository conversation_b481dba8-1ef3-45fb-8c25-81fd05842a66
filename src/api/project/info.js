import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询工程列表
export function listInfo(query) {
  return request({
    url: '/project/info/list',
    method: 'get',
    params: query
  })
}

// 查询id工程详细分页
export function getInfo(infoId) {
  return request({
    url: '/project/info/' + parseStrEmpty(infoId),
    method: 'get'
  })
}

// 查询工程详细
export function selectInfo(infoId) {
  return request({
    url: '/project/info/select/' + parseStrEmpty(infoId),
    method: 'get'
  })
}

// 查询name工程详细分页
export function getByName(name) {
  return request({
    url: '/project/info/getByName/' + parseStrEmpty(name),
    method: 'get'
  })
}

// 新增工程
export function addInfo(data) {
  return request({
    url: '/project/info/add',
    method: 'post',
    data: data
  })
}

// 修改工程
export function updateInfo(data, infoId) {
  return request({
    url: '/project/info/' + parseStrEmpty(infoId),
    method: 'put',
    data: data
  })
}

// 删除工程
export function delInfo(infoId) {
  return request({
    url: '/project/info/' + infoId,
    method: 'delete'
  })
}

