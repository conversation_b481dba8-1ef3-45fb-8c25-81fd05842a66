import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询全部巡检车轨迹列表
export function listTrajectories(query) {
  return request({
    url: '/project/trajectory/list',
    method: 'get',
    params: query
  })
}

// 查询某工程巡检车轨迹详细
export function listByProjectName(projectName) {
  return request({
    url: '/project/trajectory/listByProjectName/' + parseStrEmpty(projectName),
    method: 'get'
  })
}

// 查询轨迹点详细列表
export function getById(trajectoryId) {
  return request({
    url: '/project/trajectory/' + parseStrEmpty(trajectoryId),
    method: 'get'
  })
}

// 查询轨迹点详细
export function selectById(trajectoryId) {
  return request({
    url: '/project/trajectory/select/' + parseStrEmpty(trajectoryId),
    method: 'get'
  })
}

// 新增巡检车轨迹点
export function addTrajectory(data) {
  return request({
    url: '/project/trajectory/add',
    method: 'post',
    data: data
  })
}

// 修改巡检车轨迹点
export function updateTrajectory(data, trajectoryId) {
  return request({
    url: '/project/trajectory/' + parseStrEmpty(trajectoryId),
    method: 'put',
    data: data
  })
}

// 删除巡检车轨迹点
export function delTrajectory(trajectoryId) {
  return request({
    url: '/project/trajectory/' + parseStrEmpty(trajectoryId),
    method: 'delete'
  })
}

