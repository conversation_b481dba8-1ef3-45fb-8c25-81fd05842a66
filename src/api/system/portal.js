import request from '@/utils/request'

// 查询隧道检查-洞门信息列表
export function listPortal(query) {
  return request({
    url: '/tunnel/portal/list?pageSize='+query.pageSize+'&pageNum='+query.pageNum,
    method: 'post',
    data: query
  })
}

// 查询隧道检查-洞门信息详细
export function getPortal(id) {
  return request({
    url: '/tunnel/portal/' + id,
    method: 'get'
  })
}

// 新增隧道检查-洞门信息
export function addPortal(data) {
  return request({
    url: '/tunnel/portal/add',
    method: 'post',
    data: data
  })
}

// 修改隧道检查-洞门信息
export function updatePortal(data) {
  return request({
    url: '/tunnel/portal/edit',
    method: 'put',
    data: data
  })
}

// 删除隧道检查-洞门信息
export function delPortal(id) {
  return request({
    url: '/tunnel/portal/' + id,
    method: 'delete'
  })
}

export function getProjects() {
  return request({
    url: '/project/project/searchAlllist',
    method: 'post',
      data: {}
  })
}

export function getTunnels() {
  return request({
    url: '/tunnel/manage/searchAllList',
    method: 'post',
      data: {}
  })
}

export function exportData(data) {
  return request({
    url: '/tunnel/portal/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}
