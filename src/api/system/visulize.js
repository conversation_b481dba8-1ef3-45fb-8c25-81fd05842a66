import request from '@/utils/request'

// 查询项目管理列表
export function listVisulize(query) {
  return request({
    url: '/tunnel/visulize/list?pageSize='+query.pageSize+'&pageNum='+query.pageNum,
    method: 'post',
    data: query
  })
}

// 查看三维可视化
export function getVisulize(totalTunnelCode) {
  // const url ='http://47.103.13.128:8899/api/v1/data/index3DOrder?tunnelCode='+totalTunnelCode+'&caveOrder=1';
  //   const url ='https://three.whjsyc.cn/api/v1/data/index3DOrder?tunnelCode='+totalTunnelCode+'&caveOrder=1';
  const url = 'https://three.whjsyc.cn/api/v1/data/index3DOrder?tunnelCode='+totalTunnelCode+'&caveOrder=1';
    //window.location.href=url;
    window.open(url, '_blank');
}

// 新增项目管理
export function addVisulize(data) {
  return request({
    url: '/tunnel/visulize/add',
    method: 'post',
    data: data
  })
}

// 删除项目管理
export function delVisulize(id) {
  return request({
    url: '/tunnel/visulize/remove/' + id,
    method: 'delete'
  })
}
