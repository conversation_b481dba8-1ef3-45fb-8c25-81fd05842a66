import request from '@/utils/request'

// 查询展布图管理列表
export function listPicture(query) {
  return request({
    url: '/tunnel/layout/picture/list?pageSize='+query.pageSize+'&pageNum='+query.pageNum,
    method: 'post',
    data: query
  })
}

// 查询展布图管理详细
export function getPicture(id) {
  return request({
    url: '/project/picture/' + id,
    method: 'get'
  })
}

// 新增展布图管理
export function addPicture(data) {
  return request({
    url: '/project/picture',
    method: 'post',
    data: data
  })
}

// 修改展布图管理
export function updatePicture(data) {
  return request({
    url: '/project/picture',
    method: 'put',
    data: data
  })
}

// 删除展布图管理
export function delPicture(id) {
  return request({
    url: '/project/picture/' + id,
    method: 'delete'
  })
}
