import request from '@/utils/request'

// 查询隧道检查-路面列表
export function listRoad(query) {
  return request({
    url: '/tunnel/road/list?pageSize='+query.pageSize+'&pageNum='+query.pageNum,
    method: 'post',
    data: query
  })
}

// 查询隧道检查-路面详细
export function getRoad(id) {
  return request({
    url: '/tunnel/road/' + id,
    method: 'get'
  })
}

// 新增隧道检查-路面
export function addRoad(data) {
  return request({
    url: '/tunnel/road/add',
    method: 'post',
    data: data
  })
}

// 修改隧道检查-路面
export function updateRoad(data) {
  return request({
    url: '/tunnel/road/edit',
    method: 'put',
    data: data
  })
}

// 删除隧道检查-路面
export function delRoad(id) {
  return request({
    url: '/tunnel/road/' + id,
    method: 'delete'
  })
}

export function getProjects() {
  return request({
    url: '/project/project/searchAlllist',
    method: 'post',
      data: {}
  })
}

export function getTunnels() {
  return request({
    url: '/tunnel/manage/searchAllList',
    method: 'post',
      data: {}
  })
}

export function exportData(data) {
  return request({
    url: '/tunnel/road/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}

