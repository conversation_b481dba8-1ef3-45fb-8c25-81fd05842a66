import request from '@/utils/request'

// 查询隧道检查-标线轮廓标列表
export function listOutline(query) {
  return request({
    url: '/tunnel/outline/list?pageSize='+query.pageSize+'&pageNum='+query.pageNum,
    method: 'post',
    data: query
  })
}

// 查询隧道检查-标线轮廓标详细
export function getOutline(id) {
  return request({
    url: '/tunnel/outline/' + id,
    method: 'get'
  })
}

// 新增隧道检查-标线轮廓标
export function addOutline(data) {
  return request({
    url: '/tunnel/outline/add',
    method: 'post',
    data: data
  })
}

// 修改隧道检查-标线轮廓标
export function updateOutline(data) {
  return request({
    url: '/tunnel/outline/edit',
    method: 'put',
    data: data
  })
}

// 删除隧道检查-标线轮廓标
export function delOutline(id) {
  return request({
    url: '/tunnel/outline/' + id,
    method: 'delete'
  })
}

export function getProjects() {
  return request({
    url: '/project/project/searchAlllist',
    method: 'post',
      data: {}
  })
}

export function getTunnels() {
  return request({
    url: '/tunnel/manage/searchAllList',
    method: 'post',
      data: {}
  })
}

export function exportData(data) {
  return request({
    url: '/tunnel/outline/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}
