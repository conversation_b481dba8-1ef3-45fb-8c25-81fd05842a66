import request from '@/utils/request'

// 查询图片压缩包上传管理列表
export function listZip(query) {
  return request({
    url: '/tunnel/zip/list?pageSize='+query.pageSize+'&pageNum='+query.pageNum,
    method: 'post',
    data: query
  })
}

// 查询图片压缩包上传管理详细
export function getZip(id) {
  return request({
    url: '/tunnel/zip/' + id,
    method: 'get'
  })
}

// 新增图片压缩包上传管理
export function addZip(data) {
  return request({
    headers: { 'Content-Type': 'multipart/form-data'},
    url: '/tunnel/zip/add',
    method: 'post',
    data: data,
    timeout:500000
  })
}

// 修改图片压缩包上传管理
export function updateZip(data) {
  return request({
    headers: { 'Content-Type': 'multipart/form-data'},
    url: '/tunnel/zip',
    method: 'put',
    data: data,
    timeout:500000
  })
}

// 删除图片压缩包上传管理
export function delZip(id) {
  return request({
    url: '/tunnel/zip/' + id,
    method: 'delete'
  })
}


export function exportData(data) {
  return request({
    url: '/tunnel/zip/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}

export function associateZip(id) {
  return request({
    url: '/tunnel/zip/relationZipPicture?id='+id,
    method: 'post',
    data:{}
  })
}

export function getProjects() {
  return request({
    url: '/project/project/searchAlllist',
    method: 'post',
      data: {}
  })
}

export function getTunnels() {
  return request({
    url: '/tunnel/manage/searchAllList',
    method: 'post',
      data: {}
  })
}
