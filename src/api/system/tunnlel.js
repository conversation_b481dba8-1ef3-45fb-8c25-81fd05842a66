import request from '@/utils/request'

export function listTunnelAll(data) {
    return request({
        url: '/tunnel/manage/listByProjectId',
        method: 'post',
        data:data
    })
}
// 查询隧道档案管理基本信息列表
export function listManage(query) {
  return request({
    url: '/tunnel/manage/list?pageSize='+query.pageSize+'&pageNum='+query.pageNum,
    method: 'post',
    data: query
  })
}

// 查询隧道档案管理基本信息详细
export function getManage(id) {
  return request({
    url: '/tunnel/manage/' + id,
    method: 'get'
  })
}

// 新增隧道档案管理基本信息
export function addManage(data) {
  return request({
    url: '/tunnel/manage/add',
    method: 'post',
    data: data
  })
}

// 修改隧道档案管理基本信息
export function updateManage(data) {
  return request({
    url: '/tunnel/manage/edit',
    method: 'put',
    data: data
  })
}

// 删除隧道档案管理基本信息
export function delManage(id) {
  return request({
    url: '/tunnel/manage/' + id,
    method: 'delete'
  })
}

export function exportData(data) {
  return request({
    url: '/tunnel/manage/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}