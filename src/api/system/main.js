import request from '@/utils/request'

// 查询项目隧道评定主列表
export function listMain(query) {
  return request({
    url: '/tunnel/main/list?pageSize='+query.pageSize+'&pageNum='+query.pageNum,
    method: 'post',
    data: query
  })
}

// 查询项目隧道评定主详细
export function getMain(id) {
  return request({
    url: '/tunnel/main/' + id,
    method: 'get'
  })
}

// 新增项目隧道评定主
export function addMain(data) {
  return request({
    url: '/tunnel/main',
    method: 'post',
    data: data
  })
}

// 修改项目隧道评定主
export function updateMain(data) {
  return request({
    url: '/tunnel/main',
    method: 'put',
    data: data
  })
}

// 删除项目隧道评定主
export function delMain(id) {
  return request({
    url: '/tunnel/main/' + id,
    method: 'delete'
  })
}
export function doJudge(id) {
  return request({
    url: '/tunnel/score/score?id=' + id,
    method: 'post'
  })
}

export function getProjects() {
  return request({
    url: '/project/project/searchAlllist',
    method: 'post',
      data: {}
  })
}

export function getTunnels() {
  return request({
    url: '/tunnel/manage/searchAllList',
    method: 'post',
      data: {}
  })
}

export function exportData(data) {
  return request({
    url: '/tunnel/main/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}
