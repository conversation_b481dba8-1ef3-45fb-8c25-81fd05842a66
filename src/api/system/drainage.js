import request from '@/utils/request'

// 查询隧道检查-排水系统列表
export function listDrainage(query) {
  return request({
    url: '/tunnel/drainage/list?pageSize='+query.pageSize+'&pageNum='+query.pageNum,
    method: 'post',
    data: query
  })
}

// 查询隧道检查-排水系统详细
export function getDrainage(id) {
  return request({
    url: '/tunnel/drainage/' + id,
    method: 'get'
  })
}

// 新增隧道检查-排水系统
export function addDrainage(data) {
  return request({
    url: '/tunnel/drainage/add',
    method: 'post',
    data: data
  })
}

// 修改隧道检查-排水系统
export function updateDrainage(data) {
  return request({
    url: '/tunnel/drainage/edit',
    method: 'put',
    data: data
  })
}

// 删除隧道检查-排水系统
export function delDrainage(id) {
  return request({
    url: '/tunnel/drainage/' + id,
    method: 'delete'
  })
}

export function getProjects() {
  return request({
    url: '/project/project/searchAlllist',
    method: 'post',
      data: {}
  })
}

export function getTunnels() {
  return request({
    url: '/tunnel/manage/searchAllList',
    method: 'post',
      data: {}
  })
}

export function exportData(data) {
  return request({
    url: '/tunnel/drainage/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}
