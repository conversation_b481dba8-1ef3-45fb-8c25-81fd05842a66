import request from '@/utils/request'

// 查询隧道检查-内装饰列表
export function listInterior(query) {
  return request({
    url: '/tunnel/interior/list?pageSize='+query.pageSize+'&pageNum='+query.pageNum,
    method: 'post',
    data: query
  })
}

// 查询隧道检查-内装饰详细
export function getInterior(id) {
  return request({
    url: '/tunnel/interior/' + id,
    method: 'get'
  })
}

// 新增隧道检查-内装饰
export function addInterior(data) {
  return request({
    url: '/tunnel/interior/add',
    method: 'post',
    data: data
  })
}

// 修改隧道检查-内装饰
export function updateInterior(data) {
  return request({
    url: '/tunnel/interior/edit',
    method: 'put',
    data: data
  })
}

// 删除隧道检查-内装饰
export function delInterior(id) {
  return request({
    url: '/tunnel/interior/' + id,
    method: 'delete'
  })
}

export function getProjects() {
  return request({
    url: '/project/project/searchAlllist',
    method: 'post',
      data: {}
  })
}

export function getTunnels() {
  return request({
    url: '/tunnel/manage/searchAllList',
    method: 'post',
      data: {}
  })
}

export function exportData(data) {
  return request({
    url: '/tunnel/interior/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}
