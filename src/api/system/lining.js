import request from '@/utils/request'

// 查询隧道检查-衬砌列表
export function listLining(query) {
  return request({
    url: '/tunnel/lining/list?pageSize='+query.pageSize+'&pageNum='+query.pageNum,
    method: 'post',
    data: query
  })
}

// 查询隧道检查-衬砌详细
export function getLining(id) {
  return request({
    url: '/tunnel/lining/' + id,
    method: 'get'
  })
}

// 新增隧道检查-衬砌
export function addLining(data) {
  return request({
    url: '/tunnel/lining/add',
    method: 'post',
    data: data
  })
}

// 修改隧道检查-衬砌
export function updateLining(data) {
  return request({
    url: '/tunnel/lining/edit',
    method: 'put',
    data: data
  })
}

// 删除隧道检查-衬砌
export function delLining(id) {
  return request({
    url: '/tunnel/lining/' + id,
    method: 'delete'
  })
}

export function getProjects() {
  return request({
    url: '/project/project/searchAlllist',
    method: 'post',
      data: {}
  })
}

export function getTunnels() {
  return request({
    url: '/tunnel/manage/searchAllList',
    method: 'post',
      data: {}
  })
}

export function exportData(data) {
  return request({
    url: '/tunnel/lining/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}
