import request from '@/utils/request'

// 查询隧道检查-吊顶预埋件列表
export function listCeiling(query) {
  return request({
    url: '/tunnel/ceiling/list?pageSize='+query.pageSize+'&pageNum='+query.pageNum,
    method: 'post',
    data: query
  })
}

// 查询隧道检查-吊顶预埋件详细
export function getCeiling(id) {
  return request({
    url: '/tunnel/ceiling/' + id,
    method: 'get'
  })
}

// 新增隧道检查-吊顶预埋件
export function addCeiling(data) {
  return request({
    url: '/tunnel/ceiling/add',
    method: 'post',
    data: data
  })
}

// 修改隧道检查-吊顶预埋件
export function updateCeiling(data) {
  return request({
    url: '/tunnel/ceiling/edit',
    method: 'put',
    data: data
  })
}

// 删除隧道检查-吊顶预埋件
export function delCeiling(id) {
  return request({
    url: '/tunnel/ceiling/' + id,
    method: 'delete'
  })
}

export function getProjects() {
  return request({
    url: '/project/project/searchAlllist',
    method: 'post',
      data: {}
  })
}

export function getTunnels() {
  return request({
    url: '/tunnel/manage/searchAllList',
    method: 'post',
      data: {}
  })
}

export function exportData(data) {
  return request({
    url: '/tunnel/ceiling/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}
