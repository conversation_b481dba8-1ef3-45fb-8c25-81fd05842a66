import request from '@/utils/request'

// 查询隧道检查-检修道列表
export function listManhole(query) {
  return request({
    url: '/tunnel/manhole/list?pageSize='+query.pageSize+'&pageNum='+query.pageNum,
    method: 'post',
    data: query
  })
}

// 查询隧道检查-检修道详细
export function getManhole(id) {
  return request({
    url: '/tunnel/manhole/' + id,
    method: 'get'
  })
}

// 新增隧道检查-检修道
export function addManhole(data) {
  return request({
    url: '/tunnel/manhole/add',
    method: 'post',
    data: data
  })
}

// 修改隧道检查-检修道
export function updateManhole(data) {
  return request({
    url: '/tunnel/manhole/edit',
    method: 'put',
    data: data
  })
}

// 删除隧道检查-检修道
export function delManhole(id) {
  return request({
    url: '/tunnel/manhole/' + id,
    method: 'delete'
  })
}

export function getProjects() {
  return request({
    url: '/project/project/searchAlllist',
    method: 'post',
      data: {}
  })
}

export function getTunnels() {
  return request({
    url: '/tunnel/manage/searchAllList',
    method: 'post',
      data: {}
  })
}

export function exportData(data) {
  return request({
    url: '/tunnel/manhole/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}
