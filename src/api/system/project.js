import request from '@/utils/request'

export function listProjectAll() {
    return request({
        url: '/project/project/listAll',
        method: 'post',
    })
}
// 查询项目管理列表
export function listProject(query) {
  return request({
    url: '/project/project/list?pageSize='+query.pageSize+'&pageNum='+query.pageNum,
    method: 'post',
    data: query
  })
}

// 查询项目管理详细
export function getProject(id) {
  return request({
    url: '/project/project/getInfo/' + id,
    method: 'get'
  })
}

// 新增项目管理
export function addProject(data) {
  return request({
    url: '/project/project/add',
    method: 'post',
    data: data
  })
}

// 修改项目管理
export function updateProject(data) {
  return request({
    url: '/project/project/edit',
    method: 'put',
    data: data
  })
}

// 删除项目管理
export function delProject(id) {
  return request({
    url: '/project/project/remove/' + id,
    method: 'delete'
  })
}

export function exportData(data) {
  return request({
    url: '/project/project/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}

export function listRelateTunnelList(query) {
  return request({
    url: '/project/inspection/list?pageSize='+query.pageSize+'&pageNum='+query.pageNum,
    method: 'post',
    data: query
  })
}
export function listTunnelList(query) {
  return request({
    url: '/tunnel/manage/list?pageSize='+query.pageSize+'&pageNum='+query.pageNum,
    method: 'post',
    data: query
  })
}

export function delRelate(id) {
  return request({
    url: '/project/inspection/' + id,
    method: 'delete'
  })
}

export function getInspection(id) {
  return request({
    url: '/project/inspection/' + id,
    method: 'get'
  })
}

export function updateInspection(data) {
  return request({
    url: '/project/inspection',
    method: 'put',
    data: data
  })
}

export function tunnelRelates(data) {
  return request({
    url: '/project/inspection',
    method: 'post',
    data: data
  })
}
