import request from '@/utils/request'
import axios from "axios";
import {ElLoading, ElMessage} from "element-plus";
import service from "../../utils/request";

// 查询隧道检查-洞口信息列表
export function listOpening(query) {
  return request({
    url: '/tunnel/opening/list?pageSize='+query.pageSize+'&pageNum='+query.pageNum,
    method: 'post',
    data: query
  })
}

// 查询隧道检查-洞口信息详细
export function getOpening(id) {
  return request({
    url: '/tunnel/opening/' + id,
    method: 'get'
  })
}

// 新增隧道检查-洞口信息
export function addOpening(data) {
  return request({
    url: '/tunnel/opening/add',
    method: 'post',
    data: data
  })
}

// 修改隧道检查-洞口信息
export function updateOpening(data) {
  return request({
    url: '/tunnel/opening/edit',
    method: 'put',
    data: data
  })
}

// 删除隧道检查-洞口信息
export function delOpening(id) {
  return request({
    url: '/tunnel/opening/' + id,
    method: 'delete'
  })
}

export function getProjects() {
  return request({
    url: '/project/project/searchAlllist',
    method: 'post',
      data: {}
  })
}

export function getTunnels() {
  return request({
    url: '/tunnel/manage/searchAllList',
    method: 'post',
      data: {}
  })
}

export function exportData(data) {
  return request({
    url: '/tunnel/opening/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}




