import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询3d隧道列表
export function listT3d(query) {
  return request({
    url: '/tunnel/t3d/list',
    method: 'get',
    params: query
  })
}

// 查询id-3d隧道详细分页
export function getT3d(t3dId) {
  return request({
    url: '/tunnel/t3d/' + parseStrEmpty(t3dId),
    method: 'get'
  })
}

// 查询3d隧道详细
export function selectT3d(t3dId) {
  return request({
    url: '/tunnel/t3d/select/' + parseStrEmpty(t3dId),
    method: 'get'
  })
}

// 查询name-3d隧道详细分页
export function getByName(t3dName) {
  return request({
    url: '/tunnel/t3d/getByName/' + parseStrEmpty(t3dName),
    method: 'get'
  })
}

// 新增3d隧道
export function addT3d(data) {
  return request({
    url: '/tunnel/t3d/add',
    method: 'post',
    data: data
  })
}

// 删除3d隧道
export function delT3d(t3dId) {
  return request({
    url: '/tunnel/t3d/' + t3dId,
    method: 'delete'
  })
}

// 修改3d隧道
export function updateT3d(data, t3dId) {
  return request({
    url: '/tunnel/t3d/' + parseStrEmpty(t3dId),
    method: 'put',
    data: data
  })
}

