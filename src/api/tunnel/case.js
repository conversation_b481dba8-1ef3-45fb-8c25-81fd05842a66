import request from '@/utils/request'

// 查询病害数据同步列表
export function listCase(query) {
  return request({
    url: '/tunnel/case/list',
    method: 'get',
    params: query
  })
}

// 查询病害数据同步详细
export function getCase(id) {
  return request({
    url: '/tunnel/case/' + id,
    method: 'get'
  })
}

// 新增病害数据同步
export function addCase(data) {
  return request({
    url: '/tunnel/case',
    method: 'post',
    data: data
  })
}

// 修改病害数据同步
export function updateCase(data) {
  return request({
    url: '/tunnel/case',
    method: 'put',
    data: data
  })
}

// 删除病害数据同步
export function delCase(id) {
    return request({
        url: '/tunnel/case/' + id,
        method: 'delete'
    })
}
// 同步病害数据同步
export function sync(id) {
    return request({
        url: '/tunnel/case/sync/' + id,
        method: 'post'
    })
}
