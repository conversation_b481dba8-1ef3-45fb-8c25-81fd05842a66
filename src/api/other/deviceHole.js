import request from '@/utils/request'

// 查询设备洞室(其他设施)列表
export function listDeviceHole(query) {
  return request({
    url: '/other/deviceHole/list',
    method: 'get',
    params: query
  })
}

// 查询设备洞室(其他设施)详细
export function getDeviceHole(id) {
  return request({
    url: '/other/deviceHole/' + id,
    method: 'get'
  })
}

// 新增设备洞室(其他设施)
export function addDeviceHole(data) {
  return request({
    url: '/other/deviceHole',
    method: 'post',
    data: data
  })
}

// 修改设备洞室(其他设施)
export function updateDeviceHole(data) {
  return request({
    url: '/other/deviceHole',
    method: 'put',
    data: data
  })
}

// 删除设备洞室(其他设施)
export function delDeviceHole(id) {
  return request({
    url: '/other/deviceHole/' + id,
    method: 'delete'
  })
}
