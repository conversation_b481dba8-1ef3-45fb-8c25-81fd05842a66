import request from '@/utils/request'

// 查询联络通道列表
export function listPassageAccess(query) {
  return request({
    url: '/other/passageAccess/list',
    method: 'get',
    params: query
  })
}

// 查询联络通道详细
export function getPassageAccess(id) {
  return request({
    url: '/other/passageAccess/' + id,
    method: 'get'
  })
}

// 新增联络通道
export function addPassageAccess(data) {
  return request({
    url: '/other/passageAccess',
    method: 'post',
    data: data
  })
}

// 修改联络通道
export function updatePassageAccess(data) {
  return request({
    url: '/other/passageAccess',
    method: 'put',
    data: data
  })
}

// 删除联络通道
export function delPassageAccess(id) {
  return request({
    url: '/other/passageAccess/' + id,
    method: 'delete'
  })
}
