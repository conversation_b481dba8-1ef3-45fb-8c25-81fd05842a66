import request from '@/utils/request'

// 查询减光设施列表
export function listLightReduction(query) {
  return request({
    url: '/other/lightReduction/list',
    method: 'get',
    params: query
  })
}

// 查询减光设施详细
export function getLightReduction(id) {
  return request({
    url: '/other/lightReduction/' + id,
    method: 'get'
  })
}

// 新增减光设施
export function addLightReduction(data) {
  return request({
    url: '/other/lightReduction',
    method: 'post',
    data: data
  })
}

// 修改减光设施
export function updateLightReduction(data) {
  return request({
    url: '/other/lightReduction',
    method: 'put',
    data: data
  })
}

// 删除减光设施
export function delLightReduction(id) {
  return request({
    url: '/other/lightReduction/' + id,
    method: 'delete'
  })
}
