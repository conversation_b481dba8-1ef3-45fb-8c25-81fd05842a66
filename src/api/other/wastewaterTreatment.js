import request from '@/utils/request'

// 查询污水处理设施列表
export function listWastewaterTreatment(query) {
  return request({
    url: '/other/wastewaterTreatment/list',
    method: 'get',
    params: query
  })
}

// 查询污水处理设施详细
export function getWastewaterTreatment(id) {
  return request({
    url: '/other/wastewaterTreatment/' + id,
    method: 'get'
  })
}

// 新增污水处理设施
export function addWastewaterTreatment(data) {
  return request({
    url: '/other/wastewaterTreatment',
    method: 'post',
    data: data
  })
}

// 修改污水处理设施
export function updateWastewaterTreatment(data) {
  return request({
    url: '/other/wastewaterTreatment',
    method: 'put',
    data: data
  })
}

// 删除污水处理设施
export function delWastewaterTreatment(id) {
  return request({
    url: '/other/wastewaterTreatment/' + id,
    method: 'delete'
  })
}
