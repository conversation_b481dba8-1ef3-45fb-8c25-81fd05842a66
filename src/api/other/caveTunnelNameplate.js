import request from '@/utils/request'

// 查询洞口雕塑/隧道铭牌列表
export function listCaveTunnelNameplate(query) {
  return request({
    url: '/other/caveTunnelNameplate/list',
    method: 'get',
    params: query
  })
}

// 查询洞口雕塑/隧道铭牌详细
export function getCaveTunnelNameplate(id) {
  return request({
    url: '/other/caveTunnelNameplate/' + id,
    method: 'get'
  })
}

// 新增洞口雕塑/隧道铭牌
export function addCaveTunnelNameplate(data) {
  return request({
    url: '/other/caveTunnelNameplate',
    method: 'post',
    data: data
  })
}

// 修改洞口雕塑/隧道铭牌
export function updateCaveTunnelNameplate(data) {
  return request({
    url: '/other/caveTunnelNameplate',
    method: 'put',
    data: data
  })
}

// 删除洞口雕塑/隧道铭牌
export function delCaveTunnelNameplate(id) {
  return request({
    url: '/other/caveTunnelNameplate/' + id,
    method: 'delete'
  })
}
