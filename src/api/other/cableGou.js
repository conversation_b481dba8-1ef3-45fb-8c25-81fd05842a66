import request from '@/utils/request'

// 查询电缆沟列表
export function listCableGou(query) {
  return request({
    url: '/other/cableGou/list',
    method: 'get',
    params: query
  })
}

// 查询电缆沟详细
export function getCableGou(id) {
  return request({
    url: '/other/cableGou/' + id,
    method: 'get'
  })
}

// 新增电缆沟
export function addCableGou(data) {
  return request({
    url: '/other/cableGou',
    method: 'post',
    data: data
  })
}

// 修改电缆沟
export function updateCableGou(data) {
  return request({
    url: '/other/cableGou',
    method: 'put',
    data: data
  })
}

// 删除电缆沟
export function delCableGou(id) {
  return request({
    url: '/other/cableGou/' + id,
    method: 'delete'
  })
}
