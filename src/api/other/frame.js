import request from '@/utils/request'

// 查询洞口限高门架列表
export function listFrame(query) {
  return request({
    url: '/other/frame/list',
    method: 'get',
    params: query
  })
}

// 查询洞口限高门架详细
export function getFrame(id) {
  return request({
    url: '/other/frame/' + id,
    method: 'get'
  })
}

// 新增洞口限高门架
export function addFrame(data) {
  return request({
    url: '/other/frame',
    method: 'post',
    data: data
  })
}

// 修改洞口限高门架
export function updateFrame(data) {
  return request({
    url: '/other/frame',
    method: 'put',
    data: data
  })
}

// 删除洞口限高门架
export function delFrame(id) {
  return request({
    url: '/other/frame/' + id,
    method: 'delete'
  })
}
