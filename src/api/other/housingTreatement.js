import request from '@/utils/request'

// 查询房屋设施列表
export function listHousingTreatement(query) {
  return request({
    url: '/other/housingTreatement/list',
    method: 'get',
    params: query
  })
}

// 查询房屋设施详细
export function getHousingTreatement(id) {
  return request({
    url: '/other/housingTreatement/' + id,
    method: 'get'
  })
}

// 新增房屋设施
export function addHousingTreatement(data) {
  return request({
    url: '/other/housingTreatement',
    method: 'post',
    data: data
  })
}

// 修改房屋设施
export function updateHousingTreatement(data) {
  return request({
    url: '/other/housingTreatement',
    method: 'put',
    data: data
  })
}

// 删除房屋设施
export function delHousingTreatement(id) {
  return request({
    url: '/other/housingTreatement/' + id,
    method: 'delete'
  })
}
