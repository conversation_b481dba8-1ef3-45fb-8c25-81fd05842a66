import request from '@/utils/request'

// 查询洞口绿化列表
export function listCaveGreening(query) {
  return request({
    url: '/other/caveGreening/list',
    method: 'get',
    params: query
  })
}

// 查询洞口绿化详细
export function getCaveGreening(id) {
  return request({
    url: '/other/caveGreening/' + id,
    method: 'get'
  })
}

// 新增洞口绿化
export function addCaveGreening(data) {
  return request({
    url: '/other/caveGreening',
    method: 'post',
    data: data
  })
}

// 修改洞口绿化
export function updateCaveGreening(data) {
  return request({
    url: '/other/caveGreening',
    method: 'put',
    data: data
  })
}

// 删除洞口绿化
export function delCaveGreening(id) {
  return request({
    url: '/other/caveGreening/' + id,
    method: 'delete'
  })
}
