import request from '@/utils/request'

// 查询消音设施列表
export function listNoiseReduction(query) {
  return request({
    url: '/other/noiseReduction/list',
    method: 'get',
    params: query
  })
}

// 查询消音设施详细
export function getNoiseReduction(id) {
  return request({
    url: '/other/noiseReduction/' + id,
    method: 'get'
  })
}

// 新增消音设施
export function addNoiseReduction(data) {
  return request({
    url: '/other/noiseReduction',
    method: 'post',
    data: data
  })
}

// 修改消音设施
export function updateNoiseReduction(data) {
  return request({
    url: '/other/noiseReduction',
    method: 'put',
    data: data
  })
}

// 删除消音设施
export function delNoiseReduction(id) {
  return request({
    url: '/other/noiseReduction/' + id,
    method: 'delete'
  })
}
