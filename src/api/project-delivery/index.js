import request from '@/utils/request'

//    查询项目交付主表列表
export const projectDeliveryList = (data) => {
    return request({
        url: '/project/delivery/list',
        method: 'post',
        data
    })
}
// 新增项目交付主表
export const projectDeliveryAdd = (data) => {
    return request({
        url: '/project/delivery/add',
        method: 'post',
        data
    })
}


// 删除项目交付主表

export const deliveryRemove = (ids) => {
    return request({
        url: `/project/delivery/remove/${ids}`,
        method: 'delete',
    })
}
// /project/delivery/getGisMapInfo/{deliveryId}

export const getGisMapInfo = (deliveryId) => {
    return request({
        url: `/project/delivery/getGisMapInfo/${deliveryId}`,
        method: 'get',
    })
}
// /project/delivery/getInfo/{id}
export const getInfo = (id) => {
    return request({
        url: `/project/delivery/getInfo/${id}`,
        method: 'get',
    })
}
///project/delivery/getProjectDetail/{deliveryId}
export const getProjectDetail = (deliveryId) => {
    return request({
        url: `/project/delivery/getProjectDetail/${deliveryId}`,
        method: 'post',
    })
}

// /project/delivery/getProjectDetailTunnel/{deliveryId}
export const getProjectDetailTunnel = (deliveryId, data) => {
    return request({
        url: `/project/delivery/getProjectDetailTunnel/${deliveryId}`,
        method: 'post',
        data
    })
}
// /project/delivery/getProjectDetailTunnelInfo/{tunnelId}
export const getProjectDetailTunnelInfo = (tunnelId) => {
    return request({
        url: `/project/delivery/getProjectDetailTunnelInfo/${tunnelId}`,
        method: 'post',
    })
}
// /project/delivery/getDeliveryCheckInfo/{tunnelId}
export const getDeliveryCheckInfo = (tunnelId) => {
    return request({
        url: `/project/delivery/getDeliveryCheckInfo/${tunnelId}`,
        method: 'post',
    })
}
// /project/delivery/getDeliveryCheckInfoRightPic

export const getDeliveryCheckInfoRightPic = (query) => {
    return request({
        url: `/project/delivery/getDeliveryCheckInfoRightPic`,
        method: 'get',
        params: query
    })
}

// /project/delivery/getDiseaseTypeTree

export const getDiseaseTypeTree = () => {
    return request({
        url: `/project/delivery/getDiseaseTypeTree`,
        method: 'get',

    })
}

// /project/delivery/getKeyTunnels/{deliveryId} 重点隧道
export const getKeyTunnels = (deliveryId) => {
    return request({
        url: `/project/delivery/getKeyTunnels/${deliveryId}`,
        method: 'get',

    })
}

// /project/delivery/startReport/{id} 开始汇报
export const startReport = (id) => {
    return request({
        url: `/project/delivery/startReport/${id}`,
        method: 'post',
    })
}