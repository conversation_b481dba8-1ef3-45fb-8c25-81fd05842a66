import request from '@/utils/request'

//    查询项目交付主表列表
export const projectDeliveryList = (data) => {
    return request({
        url: '/project/delivery/list',
        method: 'post',
        data
    })
}
// 新增项目交付主表
export const projectDeliveryAdd = (data) => {
    return request({
        url: '/project/delivery/add',
        method: 'post',
        data
    })
}


// 删除项目交付主表

export const deliveryRemove = (ids) => {
    return request({
        url: `/project/delivery/remove/${ids}`,
        method: 'delete',
    })
}
// /project/delivery/getGisMapInfo/{deliveryId}

export const getGisMapInfo = (deliveryId) => {
    return request({
        url: `/project/delivery/getGisMapInfo/${deliveryId}`,
        method: 'get',
    })
}
// /project/delivery/getInfo/{id}
export const getInfo = (id) => {
    return request({
        url: `/project/delivery/getInfo/${id}`,
        method: 'get',
    })
}
///project/delivery/getProjectDetail/{deliveryId}
export const getProjectDetail = (deliveryId) => {
    return request({
        url: `/project/delivery/getProjectDetail/${deliveryId}`,
        method: 'get',
    })
}

// /project/delivery/getProjectDetailTunnel/{deliveryId} 隧道明细
export const getProjectDetailTunnel = (deliveryId, data) => {
    return request({
        url: `/project/delivery/getProjectDetailTunnel/${deliveryId}`,
        method: 'post',
        data
    })
}
// /project/delivery/getProjectDetailTunnelInfo/{tunnelId}
export const getProjectDetailTunnelInfo = (tunnelId) => {
    return request({
        url: `/project/delivery/getProjectDetailTunnelInfo/${tunnelId}`,
        method: 'get',
    })
}
// /project/delivery/getDeliveryCheckInfo/{tunnelId}
export const getDeliveryCheckInfo = (tunnelId) => {
    return request({
        url: `/project/delivery/getDeliveryCheckInfo/${tunnelId}`,
        method: 'get',
    })
}
// /project/delivery/getDeliveryCheckInfoRightPic

export const getDeliveryCheckInfoRightPic = (data) => {
    return request({
        url: `/project/delivery/getDeliveryCheckInfoRightPic`,
        method: 'post',
        data
    })
}

// /project/delivery/getDiseaseTypeTree

export const getDiseaseTypeTree = () => {
    return request({
        url: `/project/delivery/getDiseaseTypeTree`,
        method: 'get',

    })
}

// /project/delivery/getKeyTunnels/{deliveryId} 重点隧道
export const getKeyTunnels = (deliveryId, params) => {
    return request({
        url: `/project/delivery/getKeyTunnels/${deliveryId}`,
        method: 'get',
        params
    })
}

// /project/delivery/startReport/{id} 开始汇报
export const startReport = (id) => {
    return request({
        url: `/project/delivery/startReport/${id}`,
        method: 'post',
    })
}
// /project/delivery/edit 修改项目交付主表
export const edit = (data) => {
    return request({
        url: `/project/delivery/edit`,
        method: 'put',
        data
    })
}

// 查询项目管理列表
export function listVisulize(query) {
    return request({
        url: '/tunnel/visulize/list?pageSize=' + query.pageSize + '&pageNum=' + query.pageNum,
        method: 'post',
        data: query
    })
}
// /project/delivery/getDiseaseTypeEnum  获取项目明细-检测评定（0-所有病害类型 1-土建病害类型 2-其他设施病害类型）
export const getDiseaseTypeEnum = (params) => {
    return request({
        url: `/project/delivery/getDiseaseTypeEnum`,
        method: 'get',
        params
    })
}
// /project/delivery/getDeliveryCheckInfoDiseaseList 项目明细-检测评定（点土建结构/其他工程设施)

export const getDeliveryCheckInfoDiseaseList = (data) => {
    return request({
        url: `/project/delivery/getDeliveryCheckInfoDiseaseList`,
        method: 'post',
        data
    })
}

// /project/delivery/getKeyDiseaseList/{deliveryId} 重点病害
export const getKeyDiseaseList = (deliveryId, data) => {
    return request({
        url: `/project/delivery/getKeyDiseaseList/${deliveryId}`,
        method: 'post',
        data
    })
}
// /project/delivery/getDiseaseStatistics 病害分析
export const getDiseaseStatistics = (data) => {
    return request({
        url: `/project/delivery/getDiseaseStatistics`,
        method: 'post',
        data
    })
}
// /project/delivery/getScoreList 评定结果-右侧表格 /project/delivery/getScoreList

export const getScoreList = (data) => {
    return request({
        url: `/project/delivery/getScoreList`,
        method: 'post',
        data
    })
}
// /project/delivery/getTunnelTechnicalAssessmentStatistics/{deliveryId} 评定结果-左侧统计
export const getTunnelTechnicalAssessmentStatistics = (deliveryId, params) => {
    return request({
        url: `/project/delivery/getTunnelTechnicalAssessmentStatistics/${deliveryId}`,
        method: 'get',
        params
    })
}
// /project/delivery/getTunnelDisease/{deliveryId}  重点隧道-点击病害详情

export const getTunnelDisease = (deliveryId, data) => {
    return request({
        url: `/project/delivery/getTunnelDisease/${deliveryId}`,
        method: 'post',
        data
    })
}
// /project/delivery/getTunnelTechnicalAssessment/{tunnelId}/{deliveryId}  重点隧道-点击技术状况评定
export const getTunnelTechnicalAssessment = (tunnelId, deliveryId) => {
    return request({
        url: `/project/delivery/getTunnelTechnicalAssessment/${tunnelId}/${deliveryId}`,
        method: 'get'
    })
}