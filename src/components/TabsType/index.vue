<template>
  <div class="type-tabs">
    <span
      v-for="item in typeTabs"
      :key="item.value"
      :class="['type-tab', activeTab === item.value && 'activeTab']"
      @click="handeActiveTab(item)"
      >{{ item.name }}</span
    >
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
// const typeTabs = ref([
//   {
//     name: "土建",
//     value: "1",
//   },
//   {
//     name: "其他工程",
//     value: "2",
//   },
// ]);
const props = defineProps({
  typeTabs: {
    type: Array,
    default: () => [],
  },
});
const activeTab = ref<any>(props.typeTabs[0]?.value || "1");
const handeActiveTab = (row: any) => {
  activeTab.value = row.value;
  emit("change", row.value);
};
const emit = defineEmits(["change"]);
</script>
<style lang="scss" scoped>
.type-tabs {
  height: 30px;
  display: flex;
  align-items: center;
  // justify-content: end;
  color: #006fed;

  .type-tab {
    height: 30px;
    box-sizing: border-box;
    padding: 0 10px;
    line-height: 30px;
    cursor: pointer;
    background: rgb(182, 211, 234);
  }
  .activeTab {
    background: #006fed;
    color: #fff;
  }
}
</style>