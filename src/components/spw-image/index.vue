<template>
  <el-image
    :style="{ width, height }"
    :src="src"
    :zoom-rate="1.2"
    :max-scale="7"
    :min-scale="0.2"
    :preview-src-list="srcList"
    :initial-index="4"
    :z-index="12"
    preview-teleported
    fit="cover"
  >
    <template #error>
      <div class="image-slot">
        <el-icon><icon-picture /></el-icon>
      </div>
    </template>
  </el-image>
</template>
<script lang="ts" setup>
import { Picture as IconPicture } from "@element-plus/icons-vue";
const props = defineProps({
  src: {
    type: String,
    default: () => {
      return "";
    },
  },
  srcList: {
    type: Array,
    default: () => {
      return [];
    },
  },
  width: {
    type: String,
    default: () => {
      return "86px";
    },
  },
  height: {
    type: String,
    default: () => {
      return "50px";
    },
  },
});
</script>
<style lang="scss" >
:deep(.el-image-viewer__canvas) {
  height: 80% !important;
}
:deep(.el-image-viewer__img) {
  transform: scale(0.833) rotate(0deg) translate(0px, 0px);
  max-height: 75% !important;
}
:deep(.el-image-viewer__img) {
  max-width: 80% !important; /* 设置最大宽度为容器的80% */
  max-height: 80% !important; /* 设置最大高度为容器的80% */
  margin: auto; /* 使图片在容器中居中 */
}
</style>
<style>
.el-image-viewer__canvas {
  height: calc(100vh - 20vh);
  margin-top: 10vh;
}
</style>