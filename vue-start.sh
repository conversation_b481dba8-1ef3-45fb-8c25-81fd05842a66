#!/bin/bash
#拉最新的master分支
echo "开始切换到master分支,执行git pull"
AppName=tunnel-vue

cd /data/codes/$AppName
git checkout test
git pull
echo "开始执行npm打包"
npm install
echo "拷贝node_modules文件"
rm -rf ./node_modules/@jiaminghi/data-view/lib/components/decoration3/src/main.vue
cp ./public/decoration3/main.vue ./node_modules/@jiaminghi/data-view/lib/components/decoration3/src/main.vue
rm -rf ./node_modules/@jiaminghi/data-view/lib/components/decoration6/src/main.vue
cp ./public/decoration6/main.vue ./node_modules/@jiaminghi/data-view/lib/components/decoration6/src/main.vue
npm run build:prod
echo "拷贝dist文件"
rm -rf /usr/local/openresty/nginx/html/$AppName
cp -r ./dist/ /usr/local/openresty/nginx/html/$AppName


