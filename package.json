{"name": "ruoyi", "version": "3.8.4", "description": "隧道检测数字化平台", "author": "若依", "license": "MIT", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@element-plus/icons-vue": "2.0.10", "@jiaminghi/data-view": "^2.10.0", "@vueuse/core": "9.5.0", "amfe-flexible": "^2.2.1", "echarts": "^5.4.3", "element-plus": "2.2.21", "file-saver": "2.0.5", "fuse.js": "6.6.2", "js-cookie": "3.0.1", "jsencrypt": "3.3.1", "loadsh": "^0.0.4", "nprogress": "0.2.0", "pinia": "2.0.22", "vue": "3.2.45", "vue-cropper": "1.0.3", "vue-router": "4.1.4"}, "devDependencies": {"@vitejs/plugin-vue": "^4.6.2", "@vue/compiler-sfc": "3.2.45", "axios": "^1.10.0", "postcss-pxtorem": "^6.1.0", "sass": "1.56.1", "unplugin-auto-import": "^0.11.5", "vite": "^4.5.3", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend": "0.4.0"}}